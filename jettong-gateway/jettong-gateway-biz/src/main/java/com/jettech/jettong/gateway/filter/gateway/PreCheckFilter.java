package com.jettech.jettong.gateway.filter.gateway;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.core.Ordered;
import org.springframework.util.AntPathMatcher;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

/**
 * 按规则过滤请求
 * <p>
 * 参考：https://gitee.com/mrbirdd/FEBS-Cloud/blob/master/febs-gateway/src/main/java/cc/mrbird/febs/gateway/common
 * /filter/FebsGatewayRequestFilter.java
 *
 * <AUTHOR>
 * <AUTHOR>
 * @date 2020/7/25 下午4:02
 */
@Slf4j
@RequiredArgsConstructor
public class PreCheckFilter implements GlobalFilter, Ordered
{

    private final AntPathMatcher pathMatcher = new AntPathMatcher();

    @Override
    public int getOrder()
    {
        return 1;
    }

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain)
    {
        return chain.filter(exchange);
    }

}
