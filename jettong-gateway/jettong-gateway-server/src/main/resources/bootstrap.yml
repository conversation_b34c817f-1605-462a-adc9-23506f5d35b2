jettong:
  nacos:
    ip: ${NACOS_IP:@nacos.ip@}
    port: ${NACOS_PORT:@nacos.port@}
    namespace: ${NACOS_NAMESPACE:@nacos.namespace@}
    username: ${NACOS_USERNAME:@nacos.username@}
    password: ${NACOS_PASSWORD:@nacos.password@}
spring:
  main:
    allow-bean-definition-overriding: true
  application:
    name: '@project.artifactId@'
  profiles:
    active: '@profile.active@'
  cloud:
    sentinel:
      enabled: true
      filter:
        enabled: true
      eager: true
      transport:
        dashboard: ${jettong.sentinel.dashboard}
    nacos:
      config:
        server-addr: ${jettong.nacos.ip}:${jettong.nacos.port}
        file-extension: yml
        namespace: ${jettong.nacos.namespace}
        shared-configs:
          - dataId: common.yml
            refresh: true
          - dataId: redis.yml
            refresh: false
          - dataId: mysql.yml
            refresh: true
        enabled: true
        username: ${jettong.nacos.username}
        password: ${jettong.nacos.password}
      discovery:
        username: ${jettong.nacos.username}
        password: ${jettong.nacos.password}
        server-addr: ${jettong.nacos.ip}:${jettong.nacos.port}
        namespace: ${jettong.nacos.namespace}
        metadata: # 元数据，用于权限服务实时获取各个服务的所有接口
          management.context-path: ${server.servlet.context-path:}${spring.mvc.servlet.path:}${management.endpoints.web.base-path:}
          gray_version: jettong
  autoconfigure:
    exclude: org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration
# 只能配置在bootstrap.yml ，否则会生成 log.path_IS_UNDEFINED 文件夹
# window会自动在 代码所在盘 根目录下自动创建文件夹，  如： D:/data/projects/logs
logging:
  file:
    # 为什么要用绝对路径，而非相对路径？ 正式环境服务器磁盘通常是外挂到某个目录的，所以需要指定这个路径。 开发环境将所有系统日志存放在一起，便于后期清理日志文件，不用一个项目一个项目删除。
    path: '@logging.file.path@'
    name: ${logging.file.path}/${spring.application.name}/root.log
  config: classpath:logback-spring.xml

# 用于/actuator/info
info:
  name: '@project.name@'
  description: '@project.description@'
  version: '@project.version@'
