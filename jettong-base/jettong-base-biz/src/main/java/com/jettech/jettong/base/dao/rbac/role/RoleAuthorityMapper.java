package com.jettech.jettong.base.dao.rbac.role;

import com.jettech.basic.base.mapper.SuperMapper;
import com.jettech.jettong.base.entity.rbac.role.RoleAuthority;
import org.springframework.stereotype.Repository;

/**
 * 角色权限持久化层接口
 *
 * <AUTHOR>
 * @version 1.0
 * @description 角色权限持久化层接口
 * @projectName jettong
 * @package com.jettech.jettong.base.dao.rbac.role
 * @className RoleAuthorityMapper
 * @date 2021/9/15 9:43
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Repository
public interface RoleAuthorityMapper extends SuperMapper<RoleAuthority>
{

}
