package com.jettech.jettong.base.service.file.impl.s3;


import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.jettech.basic.utils.StrPool;
import com.jettech.jettong.base.dao.file.FileMapper;
import com.jettech.jettong.base.domain.file.FileDeleteBO;
import com.jettech.jettong.base.entity.file.File;
import com.jettech.jettong.base.enumeration.file.FileStorageType;
import com.jettech.jettong.base.properties.file.FileServerProperties;
import com.jettech.jettong.base.service.file.impl.AbstractFileStrategy;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider;
import software.amazon.awssdk.core.sync.RequestBody;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.S3Configuration;
import software.amazon.awssdk.services.s3.model.*;
import software.amazon.awssdk.services.s3.presigner.S3Presigner;
import software.amazon.awssdk.services.s3.presigner.model.GetObjectPresignRequest;
import software.amazon.awssdk.services.s3.presigner.model.PresignedGetObjectRequest;

import java.io.FileInputStream;
import java.io.InputStream;
import java.net.URI;
import java.net.URL;
import java.time.Duration;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @description TODO
 * @projectName jettong
 * @package com.jettech.jettong.base.service.file.impl.s3
 * @className S3FileStrategyImpl
 * @date 2025/8/18 16:54
 * @copyright 2021 www.jettech.cn Inc. All rights reserved.
 * @ 注意：本内容仅限于捷科智诚北京有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Slf4j
@DS("#thread.tenant")
@Component("S3")
public class S3FileStrategyImpl extends AbstractFileStrategy
{
    private final S3Client s3Client;

    private final S3Presigner s3Presigner;

    public S3FileStrategyImpl(FileServerProperties fileProperties,
                              FileMapper fileMapper)
    {
        super(fileProperties, fileMapper);
        // 初始化客户端（建议从配置读取区域）
        // 创建 S3 配置，启用路径样式访问
        S3Configuration s3Config = S3Configuration.builder()
                .pathStyleAccessEnabled(true) // MinIO 需要路径样式访问
                .build();

        this.s3Client = S3Client.builder()
                .region(Region.US_EAST_2)// 从配置读取区域
                .endpointOverride(URI.create(fileProperties.getS3().getEndpoint()))
                .credentialsProvider(StaticCredentialsProvider.create(
                        AwsBasicCredentials.create(fileProperties.getS3().getAccessKey(), fileProperties.getS3().getSecretKey())))
                .serviceConfiguration(s3Config)
                .build();

        this.s3Presigner = S3Presigner.builder()
                .region(Region.US_EAST_2)// 从配置读取区域
                .endpointOverride(URI.create(fileProperties.getS3().getEndpoint()))
                .credentialsProvider(StaticCredentialsProvider.create(
                        AwsBasicCredentials.create(fileProperties.getS3().getAccessKey(), fileProperties.getS3().getSecretKey())))
                .build();
    }

    @Override
    protected void uploadFile(File file, MultipartFile multipartFile, String bucket) throws Exception
    {
        //生成文件名
        String uniqueFileName = getUniqueFileName(file);
        // 企业id/功能点/年/月/日/file
        String path = getPath(file.getBizType(), uniqueFileName);

        bucket = StrUtil.isEmpty(bucket) ? fileProperties.getS3().getBucket() : bucket;

        // 检查桶是否存在，不存在则创建 - 使用 AWS SDK 2.x 的方法
        try {
            s3Client.headBucket(HeadBucketRequest.builder().bucket(bucket).build());
        } catch (Exception e) {
            CreateBucketRequest request = CreateBucketRequest.builder().bucket(bucket).build();
            s3Client.createBucket(request);
        }

        // 上传文件（使用 RequestBody 包装流）
        try (InputStream inputStream = multipartFile.getInputStream()) {
            PutObjectRequest request = PutObjectRequest.builder()
                    .bucket(bucket)
                    .key(path)
                    .contentType(multipartFile.getContentType())
                    .contentLength(multipartFile.getSize())
                    .build();

            s3Client.putObject(request, RequestBody.fromInputStream(inputStream, multipartFile.getSize()));
        }


        file.setBucket(bucket);
        file.setPath(path);
        file.setUniqueFileName(uniqueFileName);
        file.setStorageType(FileStorageType.S3);
    }

    @Override
    @SneakyThrows
    public boolean delete(FileDeleteBO fileDeleteBO)
    {
        String bucket = StrUtil.isEmpty(fileDeleteBO.getBucket()) ? fileProperties.getS3().getBucket() : fileDeleteBO.getBucket();

        DeleteObjectRequest deleteObjectRequest = DeleteObjectRequest.builder()
                .bucket(bucket)
                .key(fileDeleteBO.getPath())
                .build();

        s3Client.deleteObject(deleteObjectRequest);
        return true;
    }

    @Override
    public String findUrl(File file)
    {
        FileServerProperties.S3 s3 = fileProperties.getS3();
        String bucket = StrUtil.isEmpty(file.getBucket()) ? s3.getBucket() : file.getBucket();

        try {
            // 使用 AWS SDK 2.x 生成预签名URL
            GetObjectRequest getObjectRequest = GetObjectRequest.builder()
                    .bucket(bucket)
                    .key(file.getPath())
                    .build();

            GetObjectPresignRequest getObjectPresignRequest = GetObjectPresignRequest.builder()
                    .signatureDuration(Duration.ofSeconds(s3.getExpiry()))
                    .getObjectRequest(getObjectRequest)
                    .build();

            PresignedGetObjectRequest presignedGetObjectRequest = s3Presigner.presignGetObject(getObjectPresignRequest);
            return presignedGetObjectRequest.url().toString();
        } catch (Exception e) {
            log.warn("加载文件url地址失败，请确保yml中第三方存储参数配置正确. bucket={}, 文件名={} path={}",
                    bucket, file.getOriginalFileName(), file.getPath(), e);
            return StrPool.EMPTY;
        }
    }

    @Override
    public Boolean updateFile(java.io.File file, File model) throws Exception
    {
        try (InputStream inputStream = new FileInputStream(file)) {
            PutObjectRequest putObjectRequest = PutObjectRequest.builder()
                    .bucket(model.getBucket())
                    .key(model.getPath())
                    .contentType(model.getContentType())
                    .contentLength(file.length())
                    .build();

            s3Client.putObject(putObjectRequest, RequestBody.fromInputStream(inputStream, file.length()));
            return true;
        } catch (Exception e) {
            log.error("修改文件失败，原因：{}", e.getMessage(), e);
            throw e;
        }
    }

}
