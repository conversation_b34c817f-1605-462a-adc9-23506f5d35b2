package com.jettech.jettong.base.dao.msg;

import com.jettech.basic.base.mapper.SuperMapper;
import com.jettech.basic.database.mybatis.conditions.Wraps;
import com.jettech.basic.database.mybatis.conditions.query.LbqWrapper;
import com.jettech.jettong.base.entity.msg.template.MsgTemplateEngine;
import org.springframework.stereotype.Repository;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * 消息模版对应消息引擎类型mapper接口
 *
 * <AUTHOR>
 * @version 1.0
 * @description 消息模版对应消息引擎类型mapper接口
 * @projectName jettong-base-cloud
 * @package com.jettech.jettong.base.dao.msg
 * @className MsgTemplateMapper
 * @date 2023/5/16 11:40
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Repository
public interface MsgTemplateEngineMapper extends SuperMapper<MsgTemplateEngine> {

    /**
     * 根据模版id删除模版引擎关联
     * @param templateId
     */
    default int deleteByTemplateId(Long templateId) {
        LbqWrapper<MsgTemplateEngine> wrapper = Wraps.<MsgTemplateEngine>lbQ()
                .eq(MsgTemplateEngine::getTemplateId, templateId);
        return this.delete(wrapper);
    }

    /**
     * 根据模版id列表删除模版引擎关联
     *
     * @param idList    模版id列表
     * @return  删除条数
     */
    default int deleteByTemplateIds(Collection<? extends Serializable> idList) {
        LbqWrapper<MsgTemplateEngine> wrapper = Wraps.<MsgTemplateEngine>lbQ()
                .in(MsgTemplateEngine::getTemplateId, idList);
        return this.delete(wrapper);
    }

    default List<MsgTemplateEngine> selectByTemplateIds(Set<Long> templateIds) {
        LbqWrapper<MsgTemplateEngine> wrapper = Wraps.<MsgTemplateEngine>lbQ()
                .in(MsgTemplateEngine::getTemplateId, templateIds);
        return this.selectList(wrapper);
    }
}
