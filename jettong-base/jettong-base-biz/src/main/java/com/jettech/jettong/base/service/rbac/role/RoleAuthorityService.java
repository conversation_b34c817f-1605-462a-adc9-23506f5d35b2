package com.jettech.jettong.base.service.rbac.role;

import com.jettech.basic.base.service.SuperService;
import com.jettech.jettong.base.dto.rbac.role.RoleAuthoritySaveDTO;
import com.jettech.jettong.base.dto.rbac.user.UserRoleSaveDTO;
import com.jettech.jettong.base.entity.rbac.role.RoleAuthority;

import java.util.List;

/**
 * 角色权限业务处理层接口
 *
 * <AUTHOR>
 * @version 1.0
 * @description 角色权限业务处理层接口
 * @projectName jettong
 * @package com.jettech.jettong.base.service.rbac.role
 * @className RoleAuthorityService
 * @date 2021/9/15 9:43
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
public interface RoleAuthorityService extends SuperService<RoleAuthority>
{

    /**
     * 给用户分配角色
     *
     * @param userRole 用户角色
     * @return {@link boolean} 是否成功
     * <AUTHOR>
     * @date 2022/6/6 11:36
     * @update 2022/6/6 11:36
     * @since 1.0
     */
    boolean saveUserRole(UserRoleSaveDTO userRole);

    /**
     * 给角色重新分配 权限（资源/菜单）
     * @param roleAuthoritySaveDTO 角色授权信息
     * @return {@link boolean} 是否成功
     * <AUTHOR>
     * @date 2022/6/6 11:37
     * @update 2022/6/6 11:37
     * @since 1.0
     */
    boolean saveRoleAuthority(RoleAuthoritySaveDTO roleAuthoritySaveDTO);

    /**
     * 根据权限id 删除
     * @param ids id
     * @return {@link boolean} 是否成功
     * <AUTHOR>
     * @date 2022/6/6 11:37
     * @update 2022/6/6 11:37
     * @since 1.0
     */
    boolean removeByAuthorityId(List<Long> ids);
}
