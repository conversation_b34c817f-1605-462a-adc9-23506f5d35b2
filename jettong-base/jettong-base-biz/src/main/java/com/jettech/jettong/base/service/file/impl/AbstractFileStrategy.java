package com.jettech.jettong.base.service.file.impl;

import cn.hutool.core.util.StrUtil;
import com.jettech.basic.context.ContextUtil;
import com.jettech.basic.exception.BizException;
import com.jettech.basic.utils.StrPool;
import com.jettech.basic.uuid.UidGeneratorUtil;
import com.jettech.jettong.base.dao.file.FileMapper;
import com.jettech.jettong.base.entity.file.File;
import com.jettech.jettong.base.enumeration.file.FileBizType;
import com.jettech.jettong.base.properties.file.FileServerProperties;
import com.jettech.jettong.base.service.file.FileStrategy;
import com.jettech.jettong.base.utils.file.FileTypeUtil;
import com.twmacinta.util.MD5;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.StringJoiner;
import java.util.UUID;

import static com.jettech.basic.exception.code.ExceptionCode.BASE_VALID_PARAM;
import static com.jettech.basic.utils.DateUtils.SLASH_DATE_FORMAT;

/**
 * 文件抽象策略 处理类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 文件抽象策略 处理类
 * @projectName jettong
 * @package com.jettech.jettong.base.service.file.impl
 * @className AbstractFileStrategy
 * @date 2021/9/15 9:43
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Slf4j
@RequiredArgsConstructor
public abstract class AbstractFileStrategy implements FileStrategy
{

    private static final String FILE_SPLIT = ".";
    protected final FileServerProperties fileProperties;
    protected final FileMapper fileMapper;

    @Override
    public File upload(MultipartFile multipartFile, String bucket, FileBizType bizType)
    {
        try
        {
            if (!StrUtil.contains(multipartFile.getOriginalFilename(), FILE_SPLIT))
            {
                throw BizException.wrap(BASE_VALID_PARAM.build("文件缺少后缀名"));
            }

            // 后缀名
            String suffix = FilenameUtils.getExtension(multipartFile.getOriginalFilename());

            File file = File.builder()
                    .originalFileName(multipartFile.getOriginalFilename())
                    .contentType(multipartFile.getContentType())
                    .size(multipartFile.getSize())
                    .bizType(bizType)
                    .suffix(suffix)
                    .fileType(FileTypeUtil.getFileType(multipartFile.getContentType()))
                    .build();
            uploadFile(file, multipartFile, bucket);

            // 计算文件md5值,支持大文件
            String tempFilePath = fileProperties.getLocal().getStoragePath() + "temp" + java.io.File.separator +
                    UidGeneratorUtil.getId() + "." + suffix;
            java.io.File tempFile = convert(multipartFile, tempFilePath);
            file.setFileMd5(MD5.asHex(MD5.getHash(tempFile)));

            Files.delete(Paths.get(tempFilePath));
            return file;
        }
        catch (Exception e)
        {
            log.error("ex", e);
            throw BizException.wrap(BASE_VALID_PARAM.build("文件上传失败"), e);
        }
    }

    /**
     * 将multipartFile 转换为 file
     *
     * @param multipartFile multipartFile
     * @param destinationPath file 路径
     * @return java.io.File File
     * @throws IOException IOException
     * <AUTHOR>
     * @date 2024/1/24 10:55
     * @update 2024/1/24 10:55
     * @since 1.0
     */
    private java.io.File convert(MultipartFile multipartFile, String destinationPath) throws IOException
    {
        java.io.File file = new java.io.File(destinationPath);

        // 判断文件存储的路径是否存在
        Path parentPath = Paths.get(file.getParent());
        if (!Files.exists(parentPath))
        {
            Files.createDirectories(parentPath);
        }
        // 创建输出流，将文件写入到本地磁盘
        Files.copy(multipartFile.getInputStream(), file.toPath());
        return file;
    }

    /**
     * 具体类型执行上传文件操作
     * @param file 文件信息
     * @param multipartFile 上传文件信息
     * @param bucket 桶
     * @throws Exception 上传文件异常
     * <AUTHOR>
     * @date 2022/4/18 10:12
     * @update zxy 2022/4/18 10:12
     * @since 1.0
     */
    protected abstract void uploadFile(File file, MultipartFile multipartFile, String bucket) throws Exception;

    /**
     * 获取年月日
     * @return String
     * <AUTHOR>
     * @date 2022/4/18 10:12
     * @update zxy 2022/4/18 10:12
     * @since 1.0
     */
    protected String getDateFolder()
    {
        return LocalDate.now().format(DateTimeFormatter.ofPattern(SLASH_DATE_FORMAT));
    }

    /**
     * 企业/年/月/日/业务类型/唯一文件名
     * @param bizType 业务类型
     * @param uniqueFileName 唯一文件名
     * @return String 文件路径
     * <AUTHOR>
     * @date 2022/4/18 10:11
     * @update zxy 2022/4/18 10:11
     * @since 1.0
     */
    protected String getPath(FileBizType bizType, String uniqueFileName)
    {
        return new StringJoiner(StrPool.SLASH).add(String.valueOf(ContextUtil.getTenant()))
                .add(bizType.getCode()).add(getDateFolder()).add(uniqueFileName).toString();
    }

    /**
     * 获取唯一文件名
     * @param file 文件信息
     * @return String 唯一文件名
     * <AUTHOR>
     * @date 2022/4/18 10:11
     * @update zxy 2022/4/18 10:11
     * @since 1.0
     */
    protected String getUniqueFileName(File file)
    {
        return new StringJoiner(StrPool.DOT)
                .add(UUID.randomUUID().toString().replace("-", ""))
                .add(file.getSuffix()).toString();
    }
}
