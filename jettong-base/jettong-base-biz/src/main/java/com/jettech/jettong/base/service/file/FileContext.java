package com.jettech.jettong.base.service.file;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import com.jettech.basic.exception.BizException;
import com.jettech.basic.utils.ArgumentAssert;
import com.jettech.basic.utils.CollHelper;
import com.jettech.basic.utils.StrPool;
import com.jettech.jettong.base.dao.file.FileMapper;
import com.jettech.jettong.base.domain.file.FileDeleteBO;
import com.jettech.jettong.base.entity.file.File;
import com.jettech.jettong.base.enumeration.file.FileStorageType;
import com.jettech.jettong.base.properties.file.FileServerProperties;
import com.jettech.jettong.base.utils.file.ZipUtils;
import com.jettech.jettong.base.vo.file.param.FileUploadVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import static com.jettech.basic.exception.code.ExceptionCode.DATA_UPDATE_ERROR;

/**
 * 文件操作类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 文件操作类
 * @projectName jettong
 * @package com.jettech.jettong.base.service.file
 * @className FileContext
 * @date 2021/9/15 9:43
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Slf4j
@Component
public class FileContext
{
    private final Map<String, FileStrategy> contextStrategyMap = new ConcurrentHashMap<>();
    private final FileServerProperties fileServerProperties;
    private final FileMapper fileMapper;

    public FileContext(Map<String, FileStrategy> map,
            FileServerProperties fileServerProperties,
            FileMapper fileMapper)
    {
        map.forEach(this.contextStrategyMap::put);
        this.fileServerProperties = fileServerProperties;
        this.fileMapper = fileMapper;
    }

    private static String buildNewFileName(String filename, Integer order)
    {
        return StrUtil.strBuilder(filename).insert(filename.lastIndexOf(StrPool.DOT), "(" + order + ")").toString();
    }

    /**
     * 文件上传
     *
     * @param file 文件
     * @param fileUploadVO 文件上传参数
     * @return 文件对象
     */
    public File upload(MultipartFile file, FileUploadVO fileUploadVO)
    {
        FileStrategy fileStrategy = getFileStrategy(fileUploadVO.getStorageType());
        return fileStrategy.upload(file, fileUploadVO.getBucket(), fileUploadVO.getBizType());
    }

    private FileStrategy getFileStrategy(FileStorageType storageType)
    {
        storageType = storageType == null ? fileServerProperties.getStorageType() : storageType;
        FileStrategy fileStrategy = contextStrategyMap.get(storageType.name());
        ArgumentAssert.notNull(fileStrategy, "请配置正确的文件存储类型");
        return fileStrategy;
    }

    /**
     * 修改文件内容
     *
     * @param file 文件
     * @param model 文件信息
     * @return {@link Boolean} 修改结果
     * <AUTHOR>
     * @date 2022/12/5 14:24
     * @update 2022/12/5 14:24
     * @since 1.0
     */
    public Boolean updateFile(java.io.File file, File model)
    {
        FileStrategy fileStrategy = getFileStrategy(model.getStorageType());
        try
        {
            return fileStrategy.updateFile(file, model);
        }
        catch (Exception e)
        {
            throw BizException.wrap(DATA_UPDATE_ERROR.build("修改文件内容失败"));
        }
    }

    /**
     * 删除源文件
     *
     * @param list 文件列表
     * @return boolean 是否成功
     * <AUTHOR>
     * @date 2022/4/18 10:29
     * @update zxy 2022/4/18 10:29
     * @since 1.0
     */
    public boolean delete(List<File> list)
    {
        if (!fileServerProperties.getDelFile())
        {
            return false;
        }

        list.forEach(item ->
        {
            FileDeleteBO fileDeleteBO = FileDeleteBO.builder()
                    .bucket(item.getBucket())
                    .path(item.getPath())
                    .storageType(item.getStorageType())
                    .build();
            FileStrategy fileStrategy = getFileStrategy(item.getStorageType());
            fileStrategy.delete(fileDeleteBO);
        });
        return true;
    }

    public void download(HttpServletRequest request, HttpServletResponse response, List<File> list) throws Exception
    {
        long fileSize = list.stream().mapToLong(file -> Convert.toLong(file.getSize(), 0L)).sum();
        String packageName = list.get(0).getOriginalFileName();
        if (list.size() > 1)
        {
            packageName = StrUtil.subBefore(packageName, ".", true) + "等.zip";
        }

        Map<String, String> map = new LinkedHashMap<>(CollHelper.initialCapacity(list.size()));
        Map<String, Integer> duplicateFile = new HashMap<>(map.size());
        //循环处理相同的文件名
        list.forEach(file ->
        {
            String originalFileName = file.getOriginalFileName();
            if (map.containsKey(originalFileName))
            {
                if (duplicateFile.containsKey(originalFileName))
                {
                    duplicateFile.put(originalFileName, duplicateFile.get(originalFileName) + 1);
                }
                else
                {
                    duplicateFile.put(originalFileName, 1);
                }
                originalFileName = buildNewFileName(originalFileName, duplicateFile.get(originalFileName));
            }
            FileStrategy fileStrategy = getFileStrategy(file.getStorageType());
            map.put(originalFileName, fileStrategy.findUrl(file));
        });


        ZipUtils.zipFilesByInputStream(map, fileSize, packageName, request, response);
    }

}
