package com.jettech.jettong.base.service.rbac.domain.org.model;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 同步OA系统组织机构返回值
 * <AUTHOR>
 * @version 1.0
 * @description 同步OA系统组织机构返回值
 * @projectName HBSC
 * @package com.jettech.jettong.base.service.rbac.domain.org.model
 * @className SyncOaUserResult
 * @date 2024/4/22 9:59
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@lombok.Data
public class SyncOaOrgResult
{

    /**
     * 返回成功
     */
    public static final String RESULT_S = "S";

    /**
     * 数据交互对象
     */
    @JsonProperty("ESB")
    private Esb esb;

    /**
     * 同步OA系统组织机构返回值,数据交互对象
     * <AUTHOR>
     * @version 1.0
     * @description 同步OA系统组织机构返回值,数据交互对象
     * @projectName HBSC
     * @package com.jettech.jettong.base.service.rbac.domain.org.model
     * @className SyncOaOrgResult.Esb
     * @date 2024/4/22 10:01
 * @copyright 2021 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
     */
    @lombok.Data
    public static class Esb
    {
        /**
         * 数据对象
         */
        @JsonProperty("DATA")
        private Data data;

        /**
         * S成功/E失败
         */
        @JsonProperty("RESULT")
        private String result;

        /**
         * 数据处理情况的描述
         */
        @JsonProperty("DESC")
        private String desc;

        /**
         * 同步OA系统组织机构返回值,数据对象
         * <AUTHOR>
         * @version 1.0
         * @description 同步OA系统组织机构返回值,数据对象
         * @projectName HBSC
         * @package com.jettech.jettong.base.service.rbac.domain.org.model
         * @className SyncOaOrgResult.Esb.Data
         * @date 2024/4/22 10:01
 * @copyright 2021 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
         */
        @lombok.Data
        public static class Data
        {
            /**
             * 主数据集合
             */
            @JsonProperty("DATAINFOS")
            private Datainfos datainfos;

            /**
             * 分页对象
             */
            @JsonProperty("SPLITPAGE")
            private SplitPage splitPage;

            /**
             * 同步OA系统组织机构返回值,主数据集合
             * <AUTHOR>
             * @version 1.0
             * @description 同步OA系统组织机构返回值,主数据集合
             * @projectName HBSC
             * @package com.jettech.jettong.base.service.rbac.domain.org.model
             * @className SyncOaOrgResult.Esb.Data.Datainfos
             * @date 2024/4/22 10:01
 * @copyright 2021 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
             */
            @lombok.Data
            public static class Datainfos
            {

                /**
                 * 数组对象
                 */
                @JsonProperty("DATAINFO")
                private List<Datainfo> datainfo;

                /**
                 * 批数据的UUID
                 */
                @JsonProperty("PUUID")
                private String puuid;

                /**
                 * 同步OA系统组织机构返回值,数组对象
                 * <AUTHOR>
                 * @version 1.0
                 * @description 同步OA系统组织机构返回值,数组对象
                 * @projectName HBSC
                 * @package com.jettech.jettong.base.service.rbac.domain.org.model
                 * @className SyncOaOrgResult.Esb.Data.Datainfos.Datainfo
                 * @date 2024/4/22 10:01
 * @copyright 2021 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
                 */
                @lombok.Data
                public static class Datainfo
                {
                    /**
                     * 主编码
                     */
                    @JsonProperty("CODE")
                    private String code;

                    /**
                     * 父节点编码
                     */
                    @JsonProperty("PARENTCODE")
                    private String parentCode;

                    /**
                     * 组织简称
                     */
                    @JsonProperty("DESC1")
                    private String name;

                    /**
                     * 组织全称
                     */
                    @JsonProperty("DESC14")
                    private String description;

                    /**
                     * 组织类型
                     * UN=标识单位
                     * UM=标识部门
                     */
                    @JsonProperty("DESC5")
                    private String type;

                    /**
                     * 组织状态
                     * 1=新增
                     * 2=更新
                     * 3=删除
                     */
                    @JsonProperty("DESC4")
                    private String state;

                    /**
                     * 最近更新时间
                     */
                    @JsonProperty("DESC8")
                    private LocalDateTime createTime;
                }
            }

            /**
             * 同步OA系统组织机构返回值,分页参数
             * <AUTHOR>
             * @version 1.0
             * @description 同步OA系统组织机构返回值,分页参数
             * @projectName HBSC
             * @package com.jettech.jettong.base.service.rbac.domain.org.model
             * @className SyncOaOrgResult.Esb.Data.SplitPage
             * @date 2024/4/22 10:01
 * @copyright 2021 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
             */
            @lombok.Data
            public static class SplitPage
            {

                /**
                 * 每页查询条数
                 * 默认200条
                 */
                @JsonProperty("COUNTPERPAGE")
                private Integer countPerPage;

                /**
                 * 当前页码
                 * 默认第1页
                 */
                @JsonProperty("CURRENTPAGE")
                private Integer currentPage;

                /**
                 * 总条数
                 */
                @JsonProperty("TOTALNUMBER")
                private Integer totalNumber;

                /**
                 * 总页数
                 */
                @JsonProperty("TOTALPAGES")
                private Integer totalPages;
            }
        }
    }
}
