package com.jettech.jettong.base.service.rbac.tenant.impl;

import cn.hutool.core.convert.Convert;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.jettech.basic.base.service.SuperCacheServiceImpl;
import com.jettech.basic.cache.model.CacheKey;
import com.jettech.basic.cache.model.CacheKeyBuilder;
import com.jettech.basic.database.mybatis.conditions.Wraps;
import com.jettech.basic.utils.ArgumentAssert;
import com.jettech.jettong.base.dao.rbac.tenant.TenantMapper;
import com.jettech.jettong.base.entity.rbac.tenant.Tenant;
import com.jettech.jettong.base.enumeration.rbac.TenantStatus;
import com.jettech.jettong.base.enumeration.rbac.TenantType;
import com.jettech.jettong.base.service.rbac.tenant.TenantService;
import com.jettech.jettong.common.cache.tenant.TenantCacheKeyBuilder;
import com.jettech.jettong.common.cache.tenant.TenantCodeCacheKeyBuilder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.function.Function;

/**
 * 租户业务处理层
 *
 * <AUTHOR>
 * @version 1.0
 * @description 租户业务处理层
 * @projectName jettong
 * @package com.jettech.jettong.base.service.rbac.tenant.impl
 * @className TenantServiceImpl
 * @date 2021/9/15 9:43
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Slf4j
@Service
@DS("master")
@RequiredArgsConstructor
public class TenantServiceImpl extends SuperCacheServiceImpl<TenantMapper, Tenant> implements TenantService
{

    @Override
    protected CacheKeyBuilder cacheKeyBuilder()
    {
        return new TenantCacheKeyBuilder();
    }


    @Override
    public Tenant getByCode(String tenant)
    {
        Function<CacheKey, Object> loader = (k) ->
                getObj(Wraps.<Tenant>lbQ().select(Tenant::getId).eq(Tenant::getCode, tenant), Convert::toLong);
        CacheKey cacheKey = new TenantCodeCacheKeyBuilder().key(tenant);
        return getByKey(cacheKey, loader);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveTenant(Tenant tenant)
    {
        ArgumentAssert.isFalse(check(tenant.getCode()), "编码重复，请重新输入");

        // 1， 保存租户 (默认库)
        tenant.setStatus(TenantStatus.WAIT_INIT);
        tenant.setType(TenantType.CREATE);

        super.save(tenant);

        CacheKey cacheKey = new TenantCodeCacheKeyBuilder().key(tenant.getCode());
        cacheOps.set(cacheKey, tenant.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateTenant(Tenant tenant)
    {
        super.updateById(tenant);
    }

    @Override
    public boolean check(String tenantCode)
    {
        return super.count(Wraps.<Tenant>lbQ().eq(Tenant::getCode, tenantCode)) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteTenant(List<Long> ids)
    {
        List<String> tenantCodeList =
                listObjs(Wraps.<Tenant>lbQ().select(Tenant::getCode).in(Tenant::getId, ids), Convert::toStr);
        if (tenantCodeList.isEmpty())
        {
            return;
        }
        super.removeByIds(ids);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteAll(List<Long> ids)
    {
        List<String> tenantCodeList =
                listObjs(Wraps.<Tenant>lbQ().select(Tenant::getCode).in(Tenant::getId, ids), Convert::toStr);
        if (tenantCodeList.isEmpty())
        {
            return;
        }
        removeByIds(ids);
        // TODO 删除租户数据库的数据

    }

    @Override
    public List<Tenant> find()
    {
        return list(Wraps.<Tenant>lbQ().eq(Tenant::getStatus, TenantStatus.NORMAL));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateStatus(List<Long> ids, TenantStatus status)
    {
        super.update(Wraps.<Tenant>lbU().set(Tenant::getStatus, status)
                .in(Tenant::getId, ids));

        delCache(ids);
    }
}
