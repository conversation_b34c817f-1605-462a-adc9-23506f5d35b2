package com.jettech.jettong.base.service.sys.dictionary.impl;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.jettech.basic.base.entity.SuperEntity;
import com.jettech.basic.base.service.SuperCacheServiceImpl;
import com.jettech.basic.cache.model.CacheHashKey;
import com.jettech.basic.cache.model.CacheKey;
import com.jettech.basic.cache.model.CacheKeyBuilder;
import com.jettech.basic.cache.repository.CachePlusOps;
import com.jettech.basic.database.mybatis.conditions.Wraps;
import com.jettech.basic.database.mybatis.conditions.query.LbqWrapper;
import com.jettech.basic.echo.properties.EchoProperties;
import com.jettech.basic.exception.BizException;
import com.jettech.basic.utils.ArgumentAssert;
import com.jettech.basic.utils.CollHelper;
import com.jettech.basic.uuid.UidGeneratorUtil;
import com.jettech.jettong.base.dao.sys.dictionary.DictionaryMapper;
import com.jettech.jettong.base.entity.sys.dictionary.Dictionary;
import com.jettech.jettong.base.entity.sys.dictionary.DictionaryExtended;
import com.jettech.jettong.base.service.sys.dictionary.DictionaryExtendedService;
import com.jettech.jettong.base.service.sys.dictionary.DictionaryService;
import com.jettech.jettong.common.cache.base.sys.dictionary.DictionaryCacheKeyBuilder;
import com.jettech.jettong.common.cache.base.sys.dictionary.DictionaryTypeCacheKeyBuilder;
import com.google.common.collect.Maps;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 数据字典信息表业务层
 *
 * <AUTHOR>
 * @version 1.0
 * @description 数据字典信息表业务层
 * @projectName jettong
 * @package com.jettech.jettong.base.service.sys.dashboard.impl
 * @className DictionaryServiceImpl
 * @date 2021-10-15
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Slf4j
@Service
@DS("#thread.tenant")
@RequiredArgsConstructor
public class DictionaryServiceImpl extends SuperCacheServiceImpl<DictionaryMapper, Dictionary>
        implements DictionaryService {
    private final DictionaryExtendedService dictionaryExtendedService;

    private final EchoProperties ips;
    private final CachePlusOps cachePlusOps;

    @Override
    protected CacheKeyBuilder cacheKeyBuilder() {
        return new DictionaryCacheKeyBuilder();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveDictionary(Dictionary dictionary) {
        if (check(dictionary.getCode(), dictionary.getType(), dictionary.getParentId())) {
            throw BizException.validFail("添加失败,原因:编码[{}]重复", dictionary.getCode());
        }
        super.save(dictionary);

        List<DictionaryExtended> dictionaryExtendeds = dictionary.getDictionaryExtendeds();

        if (!dictionaryExtendeds.isEmpty()) {
            dictionaryExtendeds.forEach(dictionaryExtended -> dictionaryExtended.setDictId(dictionary.getId()));
            // 添加扩展属性
            dictionaryExtendedService.saveBatch(dictionaryExtendeds, 100);
        }

        dictionary.setDictionaryExtendeds(dictionaryExtendeds);
        cacheOps.del(cacheKeyBuilder().key(dictionary.getId()));
        cacheOps.del(new DictionaryTypeCacheKeyBuilder().key(dictionary.getType()));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void remove(List<Long> ids) {
        if (ids.isEmpty()) {
            return;
        }
        // 判断是否有内置数据
        boolean hasReadonly =
                super.count(Wraps.<Dictionary>lbQ().eq(Dictionary::getReadonly, true).in(Dictionary::getId, ids)) > 0;
        if (hasReadonly) {
            throw BizException.validFail("删除失败，原因：内置数据不能删除");
        }

        // 删除扩展信息
        dictionaryExtendedService.remove(Wraps.<DictionaryExtended>lbQ().in(DictionaryExtended::getDictId, ids));

        List<String> types =
                super.listObjs(Wraps.<Dictionary>lbQ().select(Dictionary::getType).in(SuperEntity::getId, ids),
                        Convert::toStr);
        types.forEach(item -> cacheOps.del(new DictionaryTypeCacheKeyBuilder().key(item)));
        super.removeByIds(ids);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateDictionary(Dictionary dictionary) {
        Long id = dictionary.getId();

        Dictionary oldDictionary = super.getById(id);
        if (null == oldDictionary) {
            throw BizException.validFail("修改失败，原因：要修改的字典信息不存在");
        }

        // 删除扩展属性
        dictionaryExtendedService.remove(Wraps.<DictionaryExtended>lbQ().in(DictionaryExtended::getDictId, id));

        List<DictionaryExtended> dictionaryExtendeds = dictionary.getDictionaryExtendeds();

        for (DictionaryExtended dictionaryExtended : dictionaryExtendeds) {
            dictionaryExtended.setDictId(id);
        }
        dictionaryExtendedService.saveBatch(dictionaryExtendeds, 100);
        cacheOps.del(new DictionaryTypeCacheKeyBuilder().key(oldDictionary.getType()));
        delCache(id);
        super.updateById(dictionary);

    }

    @Override
    public Dictionary updateStateById(Long id, Boolean state) {
        Dictionary dictionary = getById(id);
        ArgumentAssert.notNull(dictionary, "字典不存在");
        super.update(Wraps.<Dictionary>lbU().set(Dictionary::getState, state).eq(Dictionary::getId, id));

        delCache(id);
        return getAllById(id);
    }

    /**
     * 重写根据id查询方法，将扩展信息查询出来
     *
     * @param id 字典id
     * @return Dictionary
     * <AUTHOR>
     * @date 2021/10/15 15:28
     * @update zxy 2021/10/15 15:28
     * @since 1.0
     */
    @Override
    public Dictionary getByIdCache(Long id) {
        CacheKey cacheKey = cacheKeyBuilder().key(id);
        return cacheOps.get(cacheKey, k ->
        {
            Dictionary dict = getAllById(id);
            cacheOps.set(cacheKey, dict, false);
            return dict;
        });
    }

    /**
     * 根据字典id查询字典信息，包含扩展信息
     *
     * @param id 字典id
     * @return Dictionary 字典信息
     * <AUTHOR>
     * @date 2021/10/15 15:39
     * @update zxy 2021/10/15 15:39
     * @since 1.0
     */
    private Dictionary getAllById(Long id) {
        Dictionary dictionary = baseMapper.selectById(id);
        if (null == dictionary) {
            return null;
        }
        dictionary.setDictionaryExtendeds(dictionaryExtendedService.findByDictId(id));

        return dictionary;
    }


    @Override
    public Map<Serializable, Object> findByIds(Set<Serializable> types) {
        if (types.isEmpty()) {
            return Collections.emptyMap();
        }

        Map<Serializable, Object> codeValueMap = Maps.newHashMapWithExpectedSize(7);
        types.forEach(type ->
        {
            Function<CacheHashKey, Map<String, Dictionary>> fun = (ck) ->
            {
                LbqWrapper<Dictionary> wrap =
                        Wraps.<Dictionary>lbQ().isNotNull(Dictionary::getCode).eq(Dictionary::getType, type);
                List<Dictionary> list = list(wrap);
                list.forEach(item -> item.setDictionaryExtendeds(dictionaryExtendedService.findByDictId(item.getId())));
                return CollHelper.uniqueIndex(list, Dictionary::getCode, dictionary -> dictionary);
            };
            Map<String, Dictionary> map = cachePlusOps.hGetAll(new DictionaryTypeCacheKeyBuilder().hashKey(type), fun);
            map.forEach((value, dict) -> codeValueMap.put(StrUtil.join(ips.getDictSeparator(), type, value), dict));
        });
        return codeValueMap;
    }

    @Override
    public List<Dictionary> findByParentKey(String dictionaryType, String parentKey) {
        Dictionary dictionary = super.getOne(
                Wraps.<Dictionary>lbQ().eq(Dictionary::getType, dictionaryType).eq(Dictionary::getCode, parentKey),
                false);
        if (null == dictionary) {
            return Collections.emptyList();
        }
        List<Dictionary> list = super.list(
                Wraps.<Dictionary>lbQ().isNotNull(Dictionary::getCode).eq(Dictionary::getParentId, dictionary.getId()));
        list.forEach(item -> item.setDictionaryExtendeds(dictionaryExtendedService.findByDictId(item.getId())));
        return list;
    }

    @Override
    public boolean check(String code, String type, Long parentId) {
        LbqWrapper<Dictionary> lbqWrapper =
                Wraps.<Dictionary>lbQ().eq(Dictionary::getCode, code).eq(Dictionary::getType, type);
        if (null != parentId) {
            lbqWrapper.eq(Dictionary::getParentId, parentId);
        }
        return super.count(lbqWrapper) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean addBatchDictionaryType(List<Dictionary> dictionarys) {
        // 将表中的数据全部查询出来
        List<Dictionary> list = super.list(Wraps.<Dictionary>lbQ()
                .select(
                        Dictionary::getId,
                        Dictionary::getType,
                        Dictionary::getCode
                ));
        List<Dictionary> addDictionaryList = new ArrayList<>();
        List<Dictionary> updateDictionaryList = new ArrayList<>();
        List<DictionaryExtended> dictionaryExtendedList = new ArrayList<>();
        dictionarys.forEach(dictionary -> {
            // 必填字段不能为空
            String type = dictionary.getType();
            String label = dictionary.getLabel();
            String name = dictionary.getName();
            String code = dictionary.getCode();
            ArgumentAssert.notNull(type, "字典类型不能为空");
            ArgumentAssert.notNull(label, "字典类型标签不能为空");
            ArgumentAssert.notNull(name, "字典名称不能为空");

            // 判断当前数据是修改还是添加啊
            Boolean isAdd = true;

            for (Dictionary item : list) {
                if (item.getCode().equals(code) && item.getType().equals(type)) {
                    dictionary.setId(item.getId());
                    isAdd = false;
                    break;
                } else {
                    dictionary.setId(UidGeneratorUtil.getId());
                }
            }
            // 给扩展属性添加id
            List<DictionaryExtended> dictionaryExtendeds = dictionary.getDictionaryExtendeds();
            if (!dictionaryExtendeds.isEmpty()) {
                for (DictionaryExtended dictionaryExtended : dictionaryExtendeds) {
                    dictionaryExtended.setDictId(dictionary.getId());
                    dictionaryExtendedList.add(dictionaryExtended);
                }
            }

            dictionary.setDictionaryExtendeds(dictionaryExtendeds);

            // 如果在库中有数据则修改，没有则添加
            if (isAdd) {
                addDictionaryList.add(dictionary);
            } else {
                updateDictionaryList.add(dictionary);
            }

            if (!isAdd) {
                cacheOps.del(cacheKeyBuilder().key(dictionary.getId()));
                cacheOps.del(new DictionaryTypeCacheKeyBuilder().key(dictionary.getType()));
            }

        });

        // 批量添加
        super.saveBatch(addDictionaryList);
        // 批量修改
        super.saveOrUpdateBatch(updateDictionaryList);
        // 批量删除扩展属性数据
        List<Long> ids = updateDictionaryList.stream().map(Dictionary::getId).collect(Collectors.toList());
        if (!ids.isEmpty() && ids.size() > 0) {
            dictionaryExtendedService.remove(Wraps.<DictionaryExtended>lbQ().in(DictionaryExtended::getDictId, ids));
        }
        // 批量添加扩展属性
        dictionaryExtendedService.saveBatch(dictionaryExtendedList, 100);
        return true;
    }
}
