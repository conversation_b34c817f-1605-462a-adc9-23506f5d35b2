package com.jettech.jettong.base.service.file;

import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

/**
 * 文件锁工具类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 文件锁工具类
 * @projectName jettong
 * @package com.jettech.jettong.base.service.file
 * @className FileLock
 * @date 2021/9/15 9:43
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Component
public final class FileLock
{
    private FileLock()
    {
    }

    private static final Map<String, Lock> LOCKS = new HashMap<>(16);

    /**
     * 获取锁
     *
     * @param key key
     * @return java.util.concurrent.locks.Lock
     * <AUTHOR>
     * @date 2019-06-14 11:30
     */
    public static synchronized Lock getLock(String key)
    {
        if (LOCKS.containsKey(key))
        {
            return LOCKS.get(key);
        }
        else
        {
            Lock one = new ReentrantLock();
            LOCKS.put(key, one);
            return one;
        }
    }

    /**
     * 删除锁
     *
     * @param key keu
     */
    public static synchronized void removeLock(String key)
    {
        LOCKS.remove(key);
    }
}
