package com.jettech.jettong.base.service.rbac.user.impl;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.crypto.symmetric.AES;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jettech.basic.base.entity.SuperEntity;
import com.jettech.basic.base.service.SuperCacheServiceImpl;
import com.jettech.basic.cache.model.CacheKey;
import com.jettech.basic.cache.model.CacheKeyBuilder;
import com.jettech.basic.cache.repository.CachePlusOps;
import com.jettech.basic.context.ContextUtil;
import com.jettech.basic.database.mybatis.auth.DataScope;
import com.jettech.basic.database.mybatis.conditions.Wraps;
import com.jettech.basic.database.mybatis.conditions.query.LbqWrapper;
import com.jettech.basic.exception.BizException;
import com.jettech.basic.security.config.EncryptionConfig;
import com.jettech.basic.utils.*;
import com.jettech.basic.uuid.UidGeneratorUtil;
import com.jettech.jettong.base.dao.rbac.org.OrgMapper;
import com.jettech.jettong.base.dao.rbac.role.RoleMapper;
import com.jettech.jettong.base.dao.rbac.user.UserMapper;
import com.jettech.jettong.base.dao.rbac.user.UserRoleMapper;
import com.jettech.jettong.base.dao.sys.personalized.PersonalizedTableViewDefaultMapper;
import com.jettech.jettong.base.dao.sys.personalized.PersonalizedTableViewMapper;
import com.jettech.jettong.base.dto.rbac.user.UserExportQuery;
import com.jettech.jettong.base.dto.rbac.user.UserResetPasswordDTO;
import com.jettech.jettong.base.dto.rbac.user.UserUpdatePasswordDTO;
import com.jettech.jettong.base.entity.file.File;
import com.jettech.jettong.base.entity.rbac.org.Org;
import com.jettech.jettong.base.entity.rbac.role.Role;
import com.jettech.jettong.base.entity.rbac.user.User;
import com.jettech.jettong.base.entity.rbac.user.UserRole;
import com.jettech.jettong.base.entity.sys.personalized.PersonalizedTableView;
import com.jettech.jettong.base.entity.sys.personalized.PersonalizedTableViewDefault;
import com.jettech.jettong.base.enumeration.file.FileBizType;
import com.jettech.jettong.base.service.file.FileService;
import com.jettech.jettong.base.service.rbac.domain.user.model.SyncOaUserParam;
import com.jettech.jettong.base.service.rbac.domain.user.model.SyncOaUserResult;
import com.jettech.jettong.base.service.rbac.domain.user.properties.JettongOaUserProperties;
import com.jettech.jettong.base.service.rbac.user.UserService;
import com.jettech.jettong.common.cache.BaseCacheKeyDefinition;
import com.jettech.jettong.common.cache.base.rbac.user.*;
import com.jettech.jettong.common.cache.common.OnlineCacheKeyBuilder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.jettech.jettong.common.constant.BizConstant.DEF_PASSWORD;

/**
 * 用户业务处理层
 *
 * <AUTHOR>
 * @version 1.0
 * @description 用户业务处理层
 * @projectName jettong
 * @package com.jettech.jettong.base.service.rbac.user.impl
 * @className UserServiceImpl
 * @date 2021/9/15 9:43
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Slf4j
@Service
@DS("#thread.tenant")
@RequiredArgsConstructor
public class UserServiceImpl extends SuperCacheServiceImpl<UserMapper, User> implements UserService
{

    /**
     * 同步OA系统用户状态
     * true:表示正在同步
     * false:表示没有在同步
     */
    public static boolean syncOAUserState = false;

    private final UserRoleMapper userRoleMapper;
    private final PersonalizedTableViewDefaultMapper personalizedTableViewDefaultMapper;
    private final PersonalizedTableViewMapper personalizedTableViewMapper;
    private final FileService fileService;
    private final CachePlusOps cachePlusOps;
    private final JettongOaUserProperties jettongOaUserProperties;
    private final OrgMapper orgMapper;
    private final RoleMapper roleMapper;
    private final EncryptionConfig encryptionConfig;

    @Override
    protected CacheKeyBuilder cacheKeyBuilder()
    {
        return new UserCacheKeyBuilder();
    }

    @Override
    public IPage<User> findPage(IPage<User> page, LbqWrapper<User> wrapper)
    {
        return baseMapper.findPage(page, wrapper, new DataScope());
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updatePassword(UserUpdatePasswordDTO data)
    {
        User user = getById(data.getId());
        ArgumentAssert.notNull(user, "用户不存在");
        ArgumentAssert.equals(user.getId(), ContextUtil.getUserId(), "只能修改自己的密码");
//        String oldPassword = SecureUtil.sha256(data.getOldPassword() + user.getSalt());
        String oldPassword = null;
        try {
            oldPassword = encryptionConfig.encryptionService().encrypt(data.getOldPassword(), user.getSalt());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        ArgumentAssert.equals(user.getPassword(), oldPassword, "旧密码错误");

        return reset(BeanPlusUtil.toBean(data, UserResetPasswordDTO.class));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean reset(UserResetPasswordDTO data)
    {
        ArgumentAssert.equals(data.getConfirmPassword(), data.getPassword(), "密码和重复密码不一致");
        User user = getById(data.getId());
        ArgumentAssert.notNull(user, "用户不存在");
//        String defPassword = SecureUtil.sha256(data.getPassword() + user.getSalt());
        String defPassword = null;
        try {
            defPassword = encryptionConfig.encryptionService().encrypt(data.getPassword(), user.getSalt());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        super.update(Wraps.<User>lbU()
                .set(User::getPassword, defPassword)
                .set(User::getPasswordErrorNum, 0L)
                // 置空
                .set(User::getPasswordErrorLastTime, null)
                .eq(User::getId, data.getId())
        );
        delCache(data.getId());
        cacheOps.del(new UserRoleCacheKeyBuilder().key(user.getId()));
        cacheOps.del(new UserMenuCacheKeyBuilder().key(user.getId()));
        cacheOps.del(new UserFunctionCacheKeyBuilder().key(user.getId()));
        cacheOps.del(new UserAccountCacheKeyBuilder().key(user.getAccount()));
        return true;
    }

    @Override
    public User updateStateById(Long userId, Boolean state)
    {
        User user = getById(userId);
        ArgumentAssert.notNull(user, "用户不存在");
        super.update(Wraps.<User>lbU().set(User::getState, state).eq(User::getId, userId));

        delCache(userId);
        return getById(userId);
    }

    @Override
    public User getByAccount(String account)
    {
        return getByKey(new UserAccountCacheKeyBuilder().key(account),
                k -> getObj(Wraps.<User>lbQ().select(User::getId).eq(User::getAccount, account), Convert::toLong));
    }

    @Override
    public List<User> findUserByRoleId(Long roleId, String keyword)
    {
        return baseMapper.findUserByRoleId(roleId, keyword);
    }

    @Override
    public boolean check(Long id, String account)
    {
        //这里不能用缓存，否则会导致用户无法登录
        return count(Wraps.<User>lbQ().eq(User::getAccount, account).ne(User::getId, id)) > 0;
    }

    private boolean checkMobile(Long id, String mobile)
    {
        return count(Wraps.<User>lbQ().eq(User::getMobile, mobile).ne(User::getId, id)) > 0;
    }

    private boolean checkIdCard(Long id, String idCard)
    {
        return count(Wraps.<User>lbQ().eq(User::getIdCard, idCard).ne(User::getId, id)) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public User saveUser(User user)
    {
        ArgumentAssert.isFalse(check(null, user.getAccount()), "账号{}已经存在", user.getAccount());
        ArgumentAssert.isFalse(checkMobile(null, user.getMobile()), "手机号{}已经存在", user.getMobile());
        Long userId = UidGeneratorUtil.getId();
        user.setSalt(RandomUtil.randomString(20));
        if (StrUtil.isEmpty(user.getPassword()))
        {
            user.setPassword(DEF_PASSWORD);
        }
        user.setId(userId);
//        user.setPassword(SecureUtil.sha256(user.getPassword() + user.getSalt()));
        String password = null;
        try {
            password = encryptionConfig.encryptionService().encrypt(user.getPassword(), user.getSalt());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        user.setPassword(password);
        user.setPasswordErrorNum(0);

        baseMapper.insert(user);

        // 保存用户角色信息
        List<UserRole> userRoles = user.getUserRoles();
        if (null != userRoles && !userRoles.isEmpty())
        {
            userRoles.forEach(item -> item.setUserId(userId));
            userRoleMapper.insertBatchSomeColumn(userRoles);
        }

        cacheOps.del(new UserRoleCacheKeyBuilder().key(user.getId()));
        cacheOps.del(new UserMenuCacheKeyBuilder().key(user.getId()));
        cacheOps.del(new UserFunctionCacheKeyBuilder().key(user.getId()));
        cacheOps.del(new UserAccountCacheKeyBuilder().key(user.getAccount()));

        return user;
    }

    @Override
    public void saveBatchUser(List<User> users)
    {
        // 添加用户基本信息
        baseMapper.insertBatchSomeColumn(users);

        // 添加用户角色信息
        List<UserRole> userRoles = new ArrayList<>();
        users.forEach(item ->
        {
            List<UserRole> itemUserRoles = item.getUserRoles();
            if (!itemUserRoles.isEmpty())
            {
                itemUserRoles.forEach(userRole -> userRole.setUserId(item.getId()));
                userRoles.addAll(itemUserRoles);
            }
        });
        if (!userRoles.isEmpty())
        {
            userRoleMapper.insertBatchSomeColumn(userRoles);
        }


        cacheOps.del(
                users.stream().map(item -> new UserRoleCacheKeyBuilder().key(item.getId())).toArray(CacheKey[]::new));
        cacheOps.del(
                users.stream().map(item -> new UserMenuCacheKeyBuilder().key(item.getId())).toArray(CacheKey[]::new));
        cacheOps.del(users.stream().map(item -> new UserFunctionCacheKeyBuilder().key(item.getId()))
                .toArray(CacheKey[]::new));
        cacheOps.del(users.stream().map(item -> new UserAccountCacheKeyBuilder().key(item.getAccount()))
                .toArray(CacheKey[]::new));

        // 清理用户缓存
        delCache(users.stream().map(User::getId).collect(Collectors.toList()));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public User updateUser(User user)
    {
        Long userId = user.getId();

        ArgumentAssert.isFalse(checkMobile(userId, user.getMobile()), "手机号{}已经存在", user.getMobile());

        User oldUser = super.getById(userId);
        if (oldUser == null)
        {
            throw BizException.validFail("修改失败,原因:未查询到用户信息");
        }
        // 不允许修改用户信息时修改密码，请单独调用修改密码接口
        user.setPassword(null);
        user.setSalt(null);

        // 保存用户角色信息
        userRoleMapper.delete(Wraps.<UserRole>lbQ().eq(UserRole::getUserId, userId));

        List<UserRole> userRoles = user.getUserRoles();
        if (null != userRoles && !userRoles.isEmpty())
        {
            userRoles.forEach(item -> item.setUserId(userId));
            userRoleMapper.insertBatchSomeColumn(userRoles);
        }

        // 修改头像信息
        if (!user.getAvatarType() && user.getAvatar() != null)
        {
            if (oldUser.getAvatar() == null || !oldUser.getAvatar().equals(user.getAvatar()))
            {
                fileService.updateById(
                        File.builder().id(user.getAvatar()).bizId(userId).bizType(FileBizType.USER_AVATAR_UPLOAD)
                                .build());
            }
            else
            {
                List<Long> ids = new ArrayList<>();
                ids.add(oldUser.getAvatar());
                fileService.removeByIds(ids);
            }
        }

        cacheOps.del(new UserRoleCacheKeyBuilder().key(userId));
        cacheOps.del(new UserMenuCacheKeyBuilder().key(userId));
        cacheOps.del(new UserFunctionCacheKeyBuilder().key(userId));
        cacheOps.del(new UserAccountCacheKeyBuilder().key(oldUser.getAccount()));

        baseMapper.updateById(user);
        delCache(userId);

        return user;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean remove(List<Long> ids)
    {
        if (ids.isEmpty())
        {
            return true;
        }

        // 判断删除用户不能是当前登录用户
        Long sessionUserId = ContextUtil.getUserId();

        if (sessionUserId != null && ids.contains(sessionUserId))
        {
            throw BizException.validFail("删除失败，原因：要删除的用户不能为当前登录用户");
        }

        userRoleMapper.delete(Wraps.<UserRole>lbQ().in(UserRole::getUserId, ids));

        personalizedTableViewDefaultMapper.delete(
                Wraps.<PersonalizedTableViewDefault>lbQ().in(PersonalizedTableViewDefault::getUserId, ids));
        personalizedTableViewMapper.delete(
                Wraps.<PersonalizedTableView>lbQ().in(PersonalizedTableView::getUserId, ids));

        List<Long> fileIds =
                fileService.listObjs(Wraps.<File>lbQ().select(File::getId).in(File::getBizId, ids).eq(File::getBizType,
                        FileBizType.USER_AVATAR_UPLOAD), Convert::toLong);
        fileService.removeByIds(fileIds);

        cacheOps.del(ids.stream().map(new UserRoleCacheKeyBuilder()::key).toArray(CacheKey[]::new));
        cacheOps.del(ids.stream().map(new UserMenuCacheKeyBuilder()::key).toArray(CacheKey[]::new));
        cacheOps.del(ids.stream().map(new UserFunctionCacheKeyBuilder()::key).toArray(CacheKey[]::new));

        // 根据id集合获取账号信息
        List<String> accounts =
                super.listObjs(Wraps.<User>lbQ().select(User::getAccount).in(SuperEntity::getId, ids), Convert::toStr);
        if (!accounts.isEmpty())
        {
            cacheOps.del(accounts.stream().map(new UserAccountCacheKeyBuilder()::key).toArray(CacheKey[]::new));
        }
        // 清理在线用户信息
        cacheOps.del(ids.stream().map(new OnlineCacheKeyBuilder()::key).toArray(CacheKey[]::new));

        // 清理已登录的token 信息
        List<String> tokenKeys = cachePlusOps.scan(BaseCacheKeyDefinition.OAUTH_TOKEN_USER_ID + "*");
        if (!tokenKeys.isEmpty())
        {
            for (String key : tokenKeys)
            {
                Long userId = cacheOps.get(key, false);
                if (userId == null || ids.contains(userId))
                {
                    cacheOps.del(key);
                }
            }
        }

        return removeByIds(ids);
    }

    @Override
    public Map<Serializable, Object> findByIds(Set<Serializable> ids)
    {
        return CollHelper.uniqueIndex(findUser(ids), User::getId, user -> user);
    }

    @Override
    public List<User> findUser(Set<Serializable> ids)
    {
        // 强转， 防止数据库隐式转换，  若你的id 是string类型，请勿强转
        return findByIds(ids,
                missIds -> missIds.stream().map(this::getByIdCache).collect(Collectors.toList())
        );
    }

    @Override
    public List<User> findUserById(List<Long> ids)
    {
        return findUser(new HashSet<>(ids));
    }

    @Override
    public List<Long> findAllUserId()
    {
        return super.listObjs(Wraps.<User>lbQ().select(User::getId), Convert::toLong);
    }

    @Override
    public User getByIdCache(Serializable id)
    {
        CacheKey cacheKey = cacheKeyBuilder().key(id);
        return cacheOps.get(cacheKey, k ->
        {
            User user = super.getById(id);
            if (null != user)
            {
                user.setUserRoles(userRoleMapper.selectList(Wraps.<UserRole>lbQ().eq(UserRole::getUserId, id)));
            }
            cacheOps.set(cacheKey, user, false);
            return user;
        });
    }

    @Override
    public List<Map<String, Object>> findUserByUserExportQuery(UserExportQuery model)
    {
        return baseMapper.findUserByUserExportQuery(model);
    }

    @Override
    public List<User> findByOrgIds(List<Long> orgIds) {
        return baseMapper.selectList(Wraps.<User>lbQ().in(User::getOrgId,orgIds));
    }

    @Override
    public boolean syncOaUser()
    {

        // 批处理UUID
        String puuid = UUID.randomUUID().toString().replaceAll("-", "").toLowerCase();

        // 查询时间
        // 获取当前时间yyyy-MM-dd HH:mm:ss格式
        String nowDateTime = DateUtil.now();

        String  lastModifyRecordTime = "1970-01-01 00:00:00~" + nowDateTime;
        
        List<SyncOaUserParam.Esb.Data.Datainfos.Datainfo> datainfoList = new ArrayList<>();
        datainfoList.add(SyncOaUserParam.Esb.Data.Datainfos.Datainfo.builder().lastModifyRecordTime(lastModifyRecordTime)
                .build());

        // 拼接查询参数
        SyncOaUserParam syncOaUserParam = SyncOaUserParam.builder()
                .esb(SyncOaUserParam.Esb.builder()
                        .data(SyncOaUserParam.Esb.Data.builder()
                                .datainfos(SyncOaUserParam.Esb.Data.Datainfos.builder()
                                        .datainfo(datainfoList)
                                        .puuid(puuid)
                                        .build())
                                .splitPage(SyncOaUserParam.Esb.Data.SplitPage.builder()
                                        .countPerPage(200)
                                        .currentPage(1)
                                        .build())
                                .build())
                        .build())
                .build();


        // 开启新线程
        ExecutorService executorService =
                new ThreadPoolExecutor(1, 1, 0L, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>());

        executorService.execute(() ->
        {
            // 开始同步
            syncOAUserState = true;
            try
            {
                log.info("开始同步OA系统用户信息");
                // 开始时间毫秒数
                long beginMillis = System.currentTimeMillis();
                // 递归查询OA系统用户信息
                List<SyncOaUserResult.Esb.Data.Datainfos.Datainfo> datainfoSet =
                        recursiveQueryOaUser(new ArrayList<>(), syncOaUserParam);
                log.info("共查询到{}条数据", datainfoSet.size());

                log.info("去重OA系统返回的数据");
                // 根据手机号去重
                List<SyncOaUserResult.Esb.Data.Datainfos.Datainfo> newDatainfoSet = datainfoSet.stream().collect(
                        Collectors.collectingAndThen(
                                Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(
                                        SyncOaUserResult.Esb.Data.Datainfos.Datainfo::getMobile))), ArrayList::new));

                log.info("去重后有效数据{}条", newDatainfoSet.size());

                log.info("数据解析入库");
                // 筛选出要同步的机构下的用户，其他舍弃
                List<Org> orgs = orgMapper.selectList(Wraps.<Org>lbQ().select(Org::getId, Org::getCode).eq(Org::getType, "ORG_TYPE"));

                // 转换为Map, key为code, value为id
                Map<String, Long> codeIdMap = orgs.stream().collect(Collectors.toMap(Org::getCode, Org::getId));

                // 转换为平台用户对象
                List<User> users = new ArrayList<>(newDatainfoSet.size());

                for (SyncOaUserResult.Esb.Data.Datainfos.Datainfo datainfo : newDatainfoSet)
                {
                    if (codeIdMap.containsKey(datainfo.getOrgCode()))
                    {
                        User user = BeanPlusUtil.toBean(datainfo, User.class);
                        user.setState(true);
                        user.setOrgId(codeIdMap.get(datainfo.getOrgCode()));
                        user.setAccount(user.getMobile());
                        user.setType("LOCAL_TYPE");
                        user.setAvatarType(true);
                        user.setAvatarPath("avatar16");
                        user.setReportWork(false);

                        users.add(user);
                    }

                }

                log.info("解析后有效数据{}条", users.size());
                if (!users.isEmpty())
                {
                    // 查询所有集团用户信息
                    List<User> allUsers = super.list(Wraps.<User>lbQ().eq(User::getType, "LOCAL_TYPE"));

                    // 转换为Map, key为手机号,value为user
                    Map<String, User> mobileMap = allUsers.stream().collect(Collectors.toMap(User::getMobile, user -> user));

                    // 筛选出要添加的数据
                    List<User> insertUsers = users.stream().filter(item -> !mobileMap.containsKey(item.getMobile()))
                            .collect(Collectors.toList());

                    // 筛选出要更新的数据
                    List<User> updateUsers = users.stream().filter(item -> mobileMap.containsKey(item.getMobile()))
                            .collect(Collectors.toList());

                    log.info("解析出{}条需要添加的数据", insertUsers.size());
                    log.info("解析出{}条需要更新的数据", updateUsers.size());
                    if (!insertUsers.isEmpty())
                    {

                        List<UserRole> userRoles = new ArrayList<>();
                        String defRole = jettongOaUserProperties.getDefrole();
                        if (!StrUtil.isEmpty(defRole))
                        {
                            log.info("开始组装要添加的数据的默认角色信息");
                            // 按英文逗号拆分并转换为数组
                            List<String> roleCodes = Arrays.asList(defRole.split(","));

                            List<Role> roles = roleMapper.selectList(Wraps.<Role>lbQ().in(Role::getCode, roleCodes));

                            if (!roles.isEmpty())
                            {
                                for (User user : insertUsers)
                                {
                                    user.setId(UidGeneratorUtil.getId());
                                    // 添加默认登录密码
                                    String salt = RandomUtil.randomString(20);
                                    user.setSalt(salt);
//                                    String password = SecureUtil.sha256(jettongOaUserProperties.getDefpassword() + user.getSalt());
                                    String password = encryptionConfig.encryptionService().encrypt(jettongOaUserProperties.getDefpassword(), user.getSalt());
                                    user.setPassword(password);
                                    user.setPasswordErrorNum(0);
                                    user.setReadonly(false);

                                    for (Role role : roles)
                                    {
                                        userRoles.add(UserRole.builder().userId(user.getId()).roleId(role.getId()).build());
                                    }
                                }
                            }
                        }
                        log.info("组装完成要添加的数据，开始入库");
                        super.saveBatch(insertUsers, 20);

                        cacheOps.del(
                                insertUsers.stream().map(item -> new UserRoleCacheKeyBuilder().key(item.getId())).toArray(CacheKey[]::new));
                        cacheOps.del(
                                insertUsers.stream().map(item -> new UserMenuCacheKeyBuilder().key(item.getId())).toArray(CacheKey[]::new));
                        cacheOps.del(insertUsers.stream().map(item -> new UserFunctionCacheKeyBuilder().key(item.getId()))
                                .toArray(CacheKey[]::new));
                        cacheOps.del(insertUsers.stream().map(item -> new UserAccountCacheKeyBuilder().key(item.getAccount()))
                                .toArray(CacheKey[]::new));
                        log.info("要添加的数据，入库完成");

                        if (!userRoles.isEmpty())
                        {
                            userRoleMapper.insertBatchSomeColumn(userRoles);
                        }
                    }

                    // 需要更新的数据
                    if (!updateUsers.isEmpty())
                    {

                        log.info("开始比对数据差异");
                        List<User> updateUserDatas = new ArrayList<>();
                        for (User user : updateUsers)
                        {
                            User oldUser = mobileMap.get(user.getMobile());
                            if (!user.getName().equals(oldUser.getName()) ||
                                    !user.getMobile().equals(oldUser.getMobile()) ||
                                    !user.getEmail().equals(oldUser.getEmail()) ||
                                    !user.getIdCard().equals(oldUser.getIdCard()))
                            {
                                // 名称
                                oldUser.setName(user.getName());
                                // 邮箱
                                oldUser.setEmail(user.getEmail());
                                // 手机号和账号
                                oldUser.setMobile(user.getMobile());
                                oldUser.setAccount(user.getMobile());
                                oldUser.setIdCard(user.getIdCard());

                                updateUserDatas.add(oldUser);
                            }
                        }

                        log.info("数据差异比对完成，共{}条数据有差异", updateUserDatas.size());
                        if (!updateUserDatas.isEmpty())
                        {
                            log.info("需要更新的数据开始入库");
                            super.updateBatchById(updateUserDatas, 20);


                            cacheOps.del(
                                    updateUserDatas.stream().map(item -> new UserRoleCacheKeyBuilder().key(item.getId())).toArray(CacheKey[]::new));
                            cacheOps.del(
                                    updateUserDatas.stream().map(item -> new UserMenuCacheKeyBuilder().key(item.getId())).toArray(CacheKey[]::new));
                            cacheOps.del(updateUserDatas.stream().map(item -> new UserFunctionCacheKeyBuilder().key(item.getId()))
                                    .toArray(CacheKey[]::new));
                            cacheOps.del(updateUserDatas.stream().map(item -> new UserAccountCacheKeyBuilder().key(item.getAccount()))
                                    .toArray(CacheKey[]::new));

                            log.info("需要更新的数据入库完成");
                        }
                    }
                }

                log.info("同步OA系统用户信息结束，共耗时：{}毫秒", System.currentTimeMillis() - beginMillis);
            }
            catch (Exception e)
            {
                log.error("同步OA系统用户信息失败，原因：{}", e.getMessage(), e);
            }
            finally
            {
                syncOAUserState = false;
                // 关闭线程池
                executorService.shutdown();
            }
        });

        return true;
    }

    /**
     * 递归查询OA系统用户信息
     *
     * @param datainfoSet OA系统用户信息
     * @param syncOaOrgParam 查询参数
     * @return {@link List< SyncOaUserResult.Esb.Data.Datainfos.Datainfo>} OA系统用户信息
     * <AUTHOR>
     * @date 2024/4/22 10:55
     * @update 2024/4/22 10:55
     * @since 1.0
     */
    private List<SyncOaUserResult.Esb.Data.Datainfos.Datainfo> recursiveQueryOaUser(
            List<SyncOaUserResult.Esb.Data.Datainfos.Datainfo> datainfoSet, SyncOaUserParam syncOaOrgParam) throws Exception
    {
        log.info("----------------------------------------------");
        log.info("请求OA系统接口");

        log.info("接口地址：{}", jettongOaUserProperties.getSyncurl());
        log.info("Usercode：{}", jettongOaUserProperties.getUsercode());
        log.info("Password：{}", jettongOaUserProperties.getPassword());
        log.info("lastModifyRecordTime：{}",
                syncOaOrgParam.getEsb().getData().getDatainfos().getDatainfo().get(0).getLastModifyRecordTime());
        log.info("CurrentPage：{}", syncOaOrgParam.getEsb().getData().getSplitPage().getCurrentPage());
        log.info("CountPerPage：{}", syncOaOrgParam.getEsb().getData().getSplitPage().getCountPerPage());

        HttpRequest request = HttpUtil.createPost(jettongOaUserProperties.getSyncurl());

        request.header(SyncOaUserParam.USER_CODE, jettongOaUserProperties.getUsercode());
        request.header(SyncOaUserParam.PASSWORD, jettongOaUserProperties.getPassword());
        request.header("Content-type", StrPool.CONTENT_TYPE);

        // 查询参数
        String param = JSONObject.toJSONString(syncOaOrgParam);
        AES aes = null;
        // 判断是否需要加密
        if (jettongOaUserProperties.getIsencry())
        {
            aes = new AES(jettongOaUserProperties.getEncrykey().getBytes());
            param = aes.encryptHex(param);
        }

        request.body(param);

        HttpResponse response = request.execute();

        if (response.isOk())
        {

            log.info("原始返回结果：{}", response.body());
            SyncOaUserResult syncOaUserResult;
            // 判断是否需要解密
            if (jettongOaUserProperties.getIsencry())
            {
                // 解密并转换为同步OA系统用户返回值对象
                syncOaUserResult = JSON.parseObject(aes.decryptStr(response.body()), SyncOaUserResult.class);

                log.info("解密后原始返回结果：{}", aes.decryptStr(response.body()));
            }
            else
            {
                // 转换为同步OA系统用户返回值对象
                syncOaUserResult = JSON.parseObject(response.body(), SyncOaUserResult.class);
            }
            // 判断是否成功
            if (SyncOaUserResult.RESULT_S.equals(syncOaUserResult.getEsb().getResult()))
            {
                // 当前页数
                int currentPage = syncOaOrgParam.getEsb().getData().getSplitPage().getCurrentPage();

                // 总页数
                int totalPages = syncOaUserResult.getEsb().getData().getSplitPage().getTotalPages();

                datainfoSet.addAll(syncOaUserResult.getEsb().getData().getDatainfos().getDatainfo());

                // 如果当前页数小于总页数，递归继续执行
                if (currentPage < totalPages)
                {
                    syncOaOrgParam.getEsb().getData().getSplitPage().setCurrentPage(currentPage + 1);
                    return recursiveQueryOaUser(datainfoSet, syncOaOrgParam);
                }
            }
            else
            {
                log.error("同步OA系统用户失败，原因：{}", syncOaUserResult.getEsb().getDesc());
            }

        }

        return datainfoSet;
    }
}
