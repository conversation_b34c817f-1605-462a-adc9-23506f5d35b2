package com.jettech.jettong.base.dao.msg;

import com.jettech.basic.base.mapper.SuperMapper;
import com.jettech.basic.database.mybatis.conditions.Wraps;
import com.jettech.basic.database.mybatis.conditions.query.LbqWrapper;
import com.jettech.jettong.base.entity.msg.engine.MsgEngineExtended;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 消息引擎拓展信息mapper接口
 *
 * <AUTHOR>
 * @version 1.0
 * @description
 * @projectName jettong-base-cloud
 * @package com.jettech.jettong.base.dao.msg
 * @className MsgEngineExtendedMapper
 * @date 2023/5/16 14:20
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Repository
public interface MsgEngineExtendedMapper extends SuperMapper<MsgEngineExtended> {

    /**
     * 根据消息引擎id 获取拓展信息列表
     */
    default List<MsgEngineExtended> selectByEngineId(Long engineId) {
        LbqWrapper<MsgEngineExtended> wrapper = Wraps.lbQ();
        wrapper.eq(MsgEngineExtended::getEngineId, engineId);
        return this.selectList(wrapper);
    }

    /**
     * 根据消息引擎id 删除拓展信息列表
     */
    default int deleteByEngineId(Long engineId) {
        LbqWrapper<MsgEngineExtended> wrapper = Wraps.lbQ();
        wrapper.eq(MsgEngineExtended::getEngineId, engineId);
        return this.delete(wrapper);
    }

}
