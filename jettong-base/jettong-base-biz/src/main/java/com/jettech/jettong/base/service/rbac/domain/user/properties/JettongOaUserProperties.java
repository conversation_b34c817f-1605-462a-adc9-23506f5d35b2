package com.jettech.jettong.base.service.rbac.domain.user.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 同步OA系统用户信息配置
 * <AUTHOR>
 * @version 1.0
 * @description 同步OA系统用户信息配置
 * @projectName jettong
 * @package com.jettech.jettong.base.service.rbac.domain.user.properties
 * @className JettongOaUserProperties
 * @date 2023/8/2 11:19
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@Configuration
@ConfigurationProperties(JettongOaUserProperties.PREFIX)
public class JettongOaUserProperties
{
    public static final String PREFIX = "jettong.oa.user";

    /**
     * 同步OA机构请求地址
     */
    private String syncurl;

    /**
     * 同步OA机构Usercode
     */
    private String usercode;

    /**
     * 同步OA机构Password
     */
    private String password;

    /**
     * 同步OA用户新增的用户授予的默认角色, 多个角色code以英文逗号隔开
     */
    private String defrole;

    /**
     * 同步OA用户新增的用户默认登录密码
     */
    private String defpassword;

    /**
     * 是否启用加密传输
     */
    private Boolean isencry;

    /**
     * 解密密钥
     */
    private String encrykey;
}
