package com.jettech.jettong.base.dao.msg;

import com.jettech.basic.base.mapper.SuperMapper;
import com.jettech.basic.database.mybatis.conditions.Wraps;
import com.jettech.basic.database.mybatis.conditions.query.LbqWrapper;
import com.jettech.jettong.base.entity.msg.MsgTemplateEvent;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;

/**
 * 消息通知模板适用事件信息Mapper 接口
 * <AUTHOR>
 * @version 1.0
 * @description 消息通知模板适用事件信息Mapper 接口
 * @projectName jettong
 * @package com.jettech.jettong.base.base.dao.msg
 * @className MsgTemplateEventMapper
 * @date 2023-05-22
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Repository
public interface MsgTemplateEventMapper extends SuperMapper<MsgTemplateEvent>
{

    default List<MsgTemplateEvent> selectByTemplateIds(Collection<Long> templateIds) {
        LbqWrapper<MsgTemplateEvent> wrapper = Wraps.lbQ();
        wrapper.in(MsgTemplateEvent::getTemplateId, templateIds);
        return this.selectList(wrapper);
    }

    default void deleteByTemplateId(Long templateId) {
        LbqWrapper<MsgTemplateEvent> wrapper = Wraps.lbQ();
        wrapper.eq(MsgTemplateEvent::getTemplateId, templateId);
        this.delete(wrapper);
    }
}
