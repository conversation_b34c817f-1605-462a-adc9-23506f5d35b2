package com.jettech.jettong.base.service.rbac.team;

import com.jettech.basic.base.service.SuperService;
import com.jettech.basic.model.LoadService;
import com.jettech.jettong.base.entity.rbac.team.SysTeam;

/**
 * 团队信息表业务接口
 * <AUTHOR>
 * @version 1.0
 * @description 团队信息表业务接口
 * @projectName jettong
 * @package com.jettech.jettong.base.base.service
 * @className SysTeamService
 * @date 2023-03-13
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
public interface SysTeamService extends SuperService<SysTeam>, LoadService
{
    Boolean checkNameUnique(Long id, String name);
}
