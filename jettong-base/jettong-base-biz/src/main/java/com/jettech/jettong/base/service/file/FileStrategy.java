package com.jettech.jettong.base.service.file;

import com.jettech.jettong.base.domain.file.FileDeleteBO;
import com.jettech.jettong.base.entity.file.File;
import com.jettech.jettong.base.enumeration.file.FileBizType;
import org.springframework.web.multipart.MultipartFile;

/**
 * 文件上传策略接口
 *
 * <AUTHOR>
 * @version 1.0
 * @description 文件上传策略接口
 * @projectName jettong
 * @package com.jettech.jettong.base.service.file
 * @className FileStrategy
 * @date 2021/9/15 9:43
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
public interface FileStrategy
{
    /**
     * 文件上传
     *
     * @param file 文件
     * @param bucket 桶
     * @param bizType 业务类型
     * @return 文件对象
     */
    File upload(MultipartFile file, String bucket, FileBizType bizType);

    /**
     * 删除源文件
     *
     * @param fileDeleteBO 待删除文件
     * @return 是否成功
     */
    boolean delete(FileDeleteBO fileDeleteBO);

    /**
     * 获取文件访问路径
     * @param file 文件信息
     * @return String 文件访问路径
     * @throws
     * <AUTHOR>
     * @date 2022/4/18 10:38
     * @update zxy 2022/4/18 10:38
     * @since 1.0
     */
    String findUrl(File file);

    /**
     * 修改文件内容
     *
     * @param file 文件
     * @param model 文件信息
     * @return {@link Boolean} 修改结果
     * <AUTHOR>
     * @date 2022/12/5 14:29
     * @update 2022/12/5 14:29
     * @since 1.0
     */
    Boolean updateFile(java.io.File file, File model) throws Exception;

}
