package com.jettech.jettong.base.dao.rbac.tenant;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.jettech.basic.base.mapper.SuperMapper;
import com.jettech.jettong.base.entity.rbac.tenant.Tenant;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

/**
 * 菜单功能持久化层接口
 *
 * <AUTHOR>
 * @version 1.0
 * @description 菜单功能持久化层接口
 * @projectName jettong
 * @package com.jettech.jettong.base.dao.rbac.role
 * @className FunctionMapper
 * @date 2021/9/15 9:43
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Repository
@InterceptorIgnore(tenantLine = "true", dynamicTableName = "true")
public interface TenantMapper extends SuperMapper<Tenant>
{

    /**
     * 根据租户编码查询租户信息
     *
     * @param code 租户编码
     * @return Tenant 租户信息
     * <AUTHOR>
     * @date 2021/11/5 15:45
     * @update zxy 2021/11/5 15:45
     * @since 1.0
     */
    Tenant getByCode(@Param("code") String code);
}
