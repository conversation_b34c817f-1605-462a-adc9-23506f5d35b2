package com.jettech.jettong.base.dao.rbac.team;

import com.jettech.basic.base.mapper.SuperMapper;
import com.jettech.jettong.base.entity.rbac.team.SysTeamUser;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.io.Serializable;
import java.util.Collection;

/**
 * 团队成员表Mapper 接口
 *
 * <AUTHOR>
 * @version 1.0
 * @description 团队成员表Mapper 接口
 * @projectName jettong
 * @package com.jettech.jettong.base.base.dao
 * @className SysTeamUserMapper
 * @date 2023-03-13
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Repository
public interface SysTeamUserMapper extends SuperMapper<SysTeamUser>
{
    /**
     * 根据团队id和用户id查询项目下使用数量
     *
     * @param teamId 团队id
     * @param userIds 用户id
     * @return {@link long} 使用数量
     * <AUTHOR>
     * @date 2023/4/18 11:32
     * @update 2023/4/18 11:32
     * @since 1.0
     */
    long getProjectTeamUserCount(@Param("teamId") Long teamId, @Param("userIds")
            Collection<? extends Serializable> userIds);
}
