package com.jettech.jettong.base.service.file;

import com.jettech.basic.base.R;
import com.jettech.jettong.base.dto.file.chunk.FileChunksMergeDTO;
import com.jettech.jettong.base.entity.file.File;

/**
 * 文件分片处理策略类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 文件分片处理策略类
 * @projectName jettong
 * @package com.jettech.jettong.base.service.file
 * @className FileService
 * @date 2021/9/15 9:43
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
public interface FileChunkStrategy
{

    /**
     * 根据md5检测文件
     *
     * @param md5    md5
     * @param userId 用户id
     * @return 附件
     */
    File md5Check(String md5, Long userId);

    /**
     * 合并文件
     *
     * @param merge 合并参数
     * @return 附件
     */
    R<File> chunksMerge(FileChunksMergeDTO merge);
}
