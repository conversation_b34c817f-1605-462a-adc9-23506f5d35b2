package com.jettech.jettong.base.service.sys.log;

import com.jettech.basic.base.service.SuperService;
import com.jettech.jettong.base.dto.sys.log.LoginLogAndOptLogLineStackResult;
import com.jettech.jettong.base.entity.sys.log.LoginLog;

import java.time.LocalDateTime;

/**
 * 登录日志业务处理层接口
 *
 * <AUTHOR>
 * @version 1.0
 * @description 登录日志业务处理层接口
 * @projectName jettong
 * @package com.jettech.jettong.base.service.sys.log
 * @className LoginLogService
 * @date 2021/9/15 9:43
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
public interface LoginLogService extends SuperService<LoginLog>
{

    /**
     * 记录登录日志
     *
     * @param userId 用户id
     * @param account 账号
     * @param ua 浏览器
     * @param ip 客户端IP
     * @param location 客户端地址
     * @param description 登陆描述消息
     * @return 登录日志
     */
    LoginLog save(Long userId, String account, String ua, String ip, String location, String description);

    /**
     * 清理日志
     *
     * @param clearBeforeTime 多久之前的
     * @param clearBeforeNum 多少条
     * @return 是否成功
     */
    boolean clearLog(LocalDateTime clearBeforeTime, Integer clearBeforeNum);

    /**
     * 查询近一个月的登录日志和操作日志统计
     * @return {@link LoginLogAndOptLogLineStackResult} 近一个月的登录日志和操作日志统计
     * <AUTHOR>
     * @date 2023/5/15 16:15
     * @update 2023/5/15 16:15
     * @since 1.0
     */
    LoginLogAndOptLogLineStackResult getLastMonthLoginLogAndOptLogLineStack();
}
