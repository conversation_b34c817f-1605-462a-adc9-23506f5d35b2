package com.jettech.jettong.base.service.sys.log.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.jettech.basic.base.service.SuperServiceImpl;
import com.jettech.basic.utils.DateUtils;
import com.jettech.jettong.base.dao.sys.log.LoginLogMapper;
import com.jettech.jettong.base.dao.sys.log.OptLogMapper;
import com.jettech.jettong.base.dto.sys.log.LoginLogAndOptLogLineStackResult;
import com.jettech.jettong.base.entity.rbac.user.User;
import com.jettech.jettong.base.entity.sys.log.LoginLog;
import com.jettech.jettong.base.service.rbac.user.UserService;
import com.jettech.jettong.base.service.sys.log.LoginLogService;
import com.google.common.collect.Maps;
import eu.bitwalker.useragentutils.Browser;
import eu.bitwalker.useragentutils.OperatingSystem;
import eu.bitwalker.useragentutils.UserAgent;
import eu.bitwalker.useragentutils.Version;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.function.Supplier;
import java.util.stream.Stream;

/**
 * 登录日志业务处理层
 *
 * <AUTHOR>
 * @version 1.0
 * @description 登录日志业务处理层
 * @projectName jettong
 * @package com.jettech.jettong.base.service.sys.log.impl
 * @className LoginLogServiceImpl
 * @date 2021/9/15 9:43
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Slf4j
@Service
@DS("#thread.tenant")
@RequiredArgsConstructor
public class LoginLogServiceImpl extends SuperServiceImpl<LoginLogMapper, LoginLog> implements LoginLogService
{
    private final UserService userService;
    private final OptLogMapper optLogMapper;

    private static final Supplier<Stream<String>> BROWSER = () -> Stream.of(
            "Chrome", "Firefox", "Microsoft Edge", "Safari", "Opera"
    );
    private static final Supplier<Stream<String>> OPERATING_SYSTEM = () -> Stream.of(
            "Android", "Linux", "Mac OS X", "Ubuntu", "Windows 10", "Windows 8", "Windows 7", "Windows XP",
            "Windows Vista"
    );

    private static String simplifyOperatingSystem(String operatingSystem)
    {
        return OPERATING_SYSTEM.get().parallel().filter(b -> StrUtil.containsIgnoreCase(operatingSystem, b)).findAny()
                .orElse(operatingSystem);
    }

    private static String simplifyBrowser(String browser)
    {
        return BROWSER.get().parallel().filter(b -> StrUtil.containsIgnoreCase(browser, b)).findAny().orElse(browser);
    }

    @Override
    public LoginLog save(Long userId, String account, String ua, String ip, String location, String description)
    {
        User user;
        if (userId != null)
        {
            user = this.userService.getByIdCache(userId);
        }
        else
        {
            user = this.userService.getByAccount(account);
        }

        LoginLog loginLog = LoginLog.builder()
                .location(location)
                .loginDate(DateUtils.formatAsDate(LocalDateTime.now()))
                .description(description)
                .requestIp(ip).ua(ua)
                .build();

        UserAgent userAgent = UserAgent.parseUserAgentString(ua);
        Browser browser = userAgent.getBrowser();
        OperatingSystem operatingSystem = userAgent.getOperatingSystem();
        Version browserVersion = userAgent.getBrowserVersion();
        if (browser != null)
        {
            loginLog.setBrowser(simplifyBrowser(browser.getName()));
        }
        if (browserVersion != null)
        {
            loginLog.setBrowserVersion(browserVersion.getVersion());
        }
        if (operatingSystem != null)
        {
            loginLog.setOperatingSystem(simplifyOperatingSystem(operatingSystem.getName()));
        }
        if (user != null)
        {
            loginLog.setAccount(user.getAccount()).setUserId(user.getId()).setUserName(user.getName())
                    .setCreatedBy(user.getId());
        }

        super.save(loginLog);

        return loginLog;
    }

    @Override
    public boolean clearLog(LocalDateTime clearBeforeTime, Integer clearBeforeNum)
    {
        return baseMapper.clearLog(clearBeforeTime, clearBeforeNum) > 0;
    }

    @Override
    public LoginLogAndOptLogLineStackResult getLastMonthLoginLogAndOptLogLineStack()
    {
        // 获取当前日期
        LocalDate endDate = LocalDate.now();
        // 获取一个月前的日期
        LocalDate startDate = endDate.minusMonths(1);

        // 获取登录次数统计
        List<Map<String, Object>> loginCounts = baseMapper.getLoginCountByDate(startDate, endDate);

        // 将登录次数统计集合转换为Map
        Map<String, Integer> loginCountMap = Maps.newHashMapWithExpectedSize(loginCounts.size());
        loginCounts.forEach(map -> loginCountMap.put(map.get("loginDate").toString(), Integer.parseInt(map.get("loginCount").toString())));

        // 获取接口访问次数统计
        List<Map<String, Object>> optCounts = optLogMapper.getOptCountByDate(startDate, endDate.plusDays(1));

        // 将接口访问次数统计集合转换为Map
        Map<String, Integer> optCountMap = Maps.newHashMapWithExpectedSize(optCounts.size());
        optCounts.forEach(map -> optCountMap.put(map.get("optDate").toString(), Integer.parseInt(map.get("optCount").toString())));

        // 获取所有时间
        String[] date = DateUtils.getBetweenDay(startDate, endDate).toArray(new String[0]);

        Integer [] loginCount = new Integer[date.length];
        Integer [] optCount = new Integer[date.length];

        for (int i = 0; i < date.length; i++)
        {
            loginCount[i] = loginCountMap.getOrDefault(date[i], 0);
            optCount[i] = optCountMap.getOrDefault(date[i], 0);
        }

        return LoginLogAndOptLogLineStackResult.builder().date(date).loginCount(loginCount).optCount(optCount).build();
    }
}
