package com.jettech.jettong.base.service.rbac.user;

import com.jettech.basic.base.service.SuperService;
import com.jettech.jettong.base.entity.rbac.user.UserRole;

/**
 * 用户角色业务处理层接口
 *
 * <AUTHOR>
 * @version 1.0
 * @description 用户角色业务处理层接口
 * @projectName jettong
 * @package com.jettech.jettong.base.service.rbac.user
 * @className UserRoleService
 * @date 2021/9/15 9:43
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
public interface UserRoleService extends SuperService<UserRole>
{
    /**
     * 初始化超级管理员角色 权限
     *
     * @param userId 用户id
     * @return 是否正确
     */
    boolean initAdmin(Long userId);
}
