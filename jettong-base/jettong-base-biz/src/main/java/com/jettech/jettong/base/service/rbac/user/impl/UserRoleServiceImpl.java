package com.jettech.jettong.base.service.rbac.user.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.jettech.basic.base.service.SuperServiceImpl;
import com.jettech.basic.database.mybatis.conditions.Wraps;
import com.jettech.basic.exception.BizException;
import com.jettech.jettong.base.dao.rbac.role.RoleMapper;
import com.jettech.jettong.base.dao.rbac.user.UserRoleMapper;
import com.jettech.jettong.base.entity.rbac.role.Role;
import com.jettech.jettong.base.entity.rbac.user.UserRole;
import com.jettech.jettong.base.service.rbac.user.UserRoleService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import static com.jettech.jettong.common.constant.BizConstant.INIT_ROLE_CODE;

/**
 * <p>
 * 业务实现类
 * 角色分配
 * 账号角色绑定
 * </p>
 *
 * <AUTHOR>
 * @date 2019-07-03
 */
@Slf4j
@Service
@DS("#thread.tenant")
@RequiredArgsConstructor
public class UserRoleServiceImpl extends SuperServiceImpl<UserRoleMapper, UserRole> implements UserRoleService
{

    private final RoleMapper roleMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean initAdmin(Long userId)
    {
        Role role = roleMapper.selectOne(Wraps.<Role>lbQ().eq(Role::getCode, INIT_ROLE_CODE).last(" limit 1"));
        if (role == null)
        {
            throw BizException.wrap("初始化用户角色失败, 无法查询到内置角色:%s", INIT_ROLE_CODE);
        }
        UserRole userRole = UserRole.builder()
                .userId(userId).roleId(role.getId())
                .build();

        return super.save(userRole);
    }
}
