package com.jettech.jettong.base.service.file.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.crypto.digest.DigestUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.jettech.basic.base.service.SuperServiceImpl;
import com.jettech.basic.database.mybatis.conditions.Wraps;
import com.jettech.basic.utils.ArgumentAssert;
import com.jettech.basic.utils.BeanPlusUtil;
import com.jettech.basic.utils.CollHelper;
import com.jettech.jettong.base.dao.file.FileMapper;
import com.jettech.jettong.base.entity.file.File;
import com.jettech.jettong.base.enumeration.file.FileBizType;
import com.jettech.jettong.base.service.file.FileContext;
import com.jettech.jettong.base.service.file.FileService;
import com.jettech.jettong.base.vo.file.param.FileUploadVO;
import com.jettech.jettong.base.vo.file.result.FileResultVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 文件上传业务处理层
 *
 * <AUTHOR>
 * @version 1.0
 * @description 文件上传业务处理层
 * @projectName jettong
 * @package com.jettech.jettong.base.service.file.impl
 * @className FileServiceImpl
 * @date 2021/9/15 9:43
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Slf4j
@Service
@DS("#thread.tenant")
public class FileServiceImpl extends SuperServiceImpl<FileMapper, File> implements FileService
{
    @Resource
    private FileContext fileContext;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public FileResultVO upload(MultipartFile file, FileUploadVO fileUploadVO)
    {
        File fileFile = fileContext.upload(file, fileUploadVO);
        // 制品库上传和脚本上传不存储
        if (!FileBizType.PACK_UPLOAD.eq(fileUploadVO.getBizType()) &&
                !FileBizType.PIPELINE_SCRIPT_UPLOAD.eq(fileUploadVO.getBizType()) &&
                !FileBizType.CMDB_APPLICATION_BUILD_FILE_UPLOAD.eq(fileUploadVO.getBizType()))
        {
            save(fileFile);
        }
        return BeanPlusUtil.toBean(fileFile, FileResultVO.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeFileByIds(List<Long> ids)
    {
        if (CollUtil.isEmpty(ids))
        {
            return false;
        }
        List<File> list = list(Wraps.<File>lbQ().in(File::getId, ids));
        if (list.isEmpty())
        {
            return false;
        }
        super.removeByIds(ids);
        return fileContext.delete(list);
    }

    @Override
    public void download(HttpServletRequest request, HttpServletResponse response, List<Long> ids) throws Exception
    {
        List<File> list = listByIds(ids);
        ArgumentAssert.notEmpty(list, "未获取到要下载的文件");

        fileContext.download(request, response, list);
    }

    @Override
    public void updateFile(Long id, Long sessionUserId, java.io.File file)
    {
        // 根据文件id查询文件信息
        File oldFile = super.getById(id);
        Boolean updateBool = fileContext.updateFile(file, oldFile);
        if (updateBool)
        {
            File newFile = File.builder()
                    .id(id)
                    .size(file.length())
                    .fileMd5(DigestUtil.md5Hex(file))
                    .updatedBy(sessionUserId)
                    .build();

            this.updateById(newFile);
        }

    }

    @Override
    public Map<Serializable, Object> findByIds(Set<Serializable> ids)
    {
        List<File> files = super.listByIds(
                ids.stream().filter(Objects::nonNull).map(Convert::toLong).collect(Collectors.toList()));

        return CollHelper.uniqueIndex(files, File::getId, file -> file);
    }
}
