package com.jettech.jettong.base.service.rbac.tenant;

import com.jettech.basic.base.service.SuperService;
import com.jettech.jettong.base.entity.rbac.tenant.TenantPlatformConfig;

import java.util.List;

/**
 * 平台配置信息（如：logo，名称，版权信息等）业务接口
 *
 * <AUTHOR>
 * @version 1.0
 * @description 平台配置信息（如：logo，名称，版权信息等）业务接口
 * @projectName jettong
 * @package com.jettech.jettong.base.service.rbac.tenant
 * @className TenantPlatformConfigService
 * @date 2021-11-20
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
public interface TenantPlatformConfigService extends SuperService<TenantPlatformConfig>
{

    /**
     * 批量添加平台配置信息
     *
     * @param tenantPlatformConfigs 平台配置信息
     * @return List<TenantPlatformConfig> 平台配置信息
     * <AUTHOR>
     * @date 2021/12/2 17:30
     * @update zxy 2021/12/2 17:30
     * @since 1.0
     */
    List<TenantPlatformConfig> saveBatch(List<TenantPlatformConfig> tenantPlatformConfigs);

    /**
     * 批量修改平台配置信息
     *
     * @param tenantPlatformConfigs 平台配置信息
     * @return List<TenantPlatformConfig> 平台配置信息
     * <AUTHOR>
     * @date 2021/12/2 17:31
     * @update zxy 2021/12/2 17:31
     * @since 1.0
     */
    List<TenantPlatformConfig> updateBatch(List<TenantPlatformConfig> tenantPlatformConfigs);

}
