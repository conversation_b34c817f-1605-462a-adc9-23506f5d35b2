package com.jettech.jettong.base.service.sys.personalized.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.jettech.basic.base.service.SuperServiceImpl;
import com.jettech.basic.database.mybatis.conditions.Wraps;
import com.jettech.jettong.base.dao.sys.personalized.PersonalizedFixedMenuMapper;
import com.jettech.jettong.base.entity.sys.personalized.PersonalizedFixedMenu;
import com.jettech.jettong.base.service.sys.personalized.PersonalizedFixedMenuService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 用户自定义固定菜单信息业务层
 *
 * <AUTHOR>
 * @version 1.0
 * @description 用户自定义固定菜单信息业务层
 * @projectName jettong
 * @package com.jettech.jettong.base.service.sys.personalized.impl
 * @className PersonalizedFixedMenuServiceImpl
 * @date 2021-11-29
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Slf4j
@Service
@DS("#thread.tenant")
public class PersonalizedFixedMenuServiceImpl
        extends SuperServiceImpl<PersonalizedFixedMenuMapper, PersonalizedFixedMenu>
        implements PersonalizedFixedMenuService
{

    @Override
    public void batchUpdate(List<PersonalizedFixedMenu> personalizedFixedMenus, Long userId)
    {
        if (null == userId)
        {
            return;
        }
        super.remove(Wraps.<PersonalizedFixedMenu>lbQ().eq(PersonalizedFixedMenu::getUserId, userId));
        personalizedFixedMenus.forEach(item -> item.setUserId(userId));
        super.saveBatch(personalizedFixedMenus);
    }
}
