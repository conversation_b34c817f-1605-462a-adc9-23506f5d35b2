package com.jettech.jettong.base.service.sys.personalized.impl;

import com.baomidou.dynamic.datasource.annotation.DS;

import com.jettech.basic.base.service.SuperServiceImpl;
import com.jettech.basic.context.ContextUtil;
import com.jettech.basic.database.mybatis.conditions.Wraps;
import com.jettech.basic.database.mybatis.conditions.query.LbqWrapper;
import com.jettech.basic.utils.ArgumentAssert;
import com.jettech.jettong.base.dao.sys.personalized.TableViewCollectionMapper;
import com.jettech.jettong.base.entity.sys.personalized.TableView;
import com.jettech.jettong.base.entity.sys.personalized.TableViewCollection;
import com.jettech.jettong.base.service.sys.personalized.TableViewCollectionService;
import com.jettech.jettong.base.service.sys.personalized.TableViewService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 筛选器收藏夹业务层
 * <AUTHOR>
 * @version 1.0
 * @description 筛选器收藏夹业务层
 * @projectName jettong
 * @package com.jettech.jettong.base.base.service.sys.impl
 * @className TableViewCollectionServiceImpl
 * @date 2023-03-23
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Slf4j
@Service
@DS("#thread.tenant")
@RequiredArgsConstructor
public class TableViewCollectionServiceImpl extends SuperServiceImpl<TableViewCollectionMapper, TableViewCollection>
        implements TableViewCollectionService {

    private final TableViewService tableViewService;

    @Override
    public List<Long> getCollectViewIds(Long userId) {

        LbqWrapper<TableViewCollection> wrapper = Wraps.lbQ();
        wrapper.eq(TableViewCollection::getUserId, userId);
        List<TableViewCollection> list = this.list(wrapper);
        return list.stream()
                .map(TableViewCollection::getViewId)
                .distinct()
                .collect(Collectors.toList());
    }

    @Override
    public List<TableView> getCollectViewList(Long userId) {
        return tableViewService.listByIds(getCollectViewIds(userId));
    }

    @Override
    public void collect(Long viewId) {
        Long userId = ContextUtil.getUserId();
        ArgumentAssert.notNull(userId, "清线登录");

        TableViewCollection model = TableViewCollection.builder()
                .userId(userId)
                .viewId(viewId)
                .build();
        this.save(model);
    }

    @Override
    public void uncollect(Long viewId) {
        Long userId = ContextUtil.getUserId();
        ArgumentAssert.notNull(userId, "清线登录");

        LbqWrapper<TableViewCollection> wrapper = Wraps.lbQ();
        wrapper.eq(TableViewCollection::getViewId, viewId)
                .eq(TableViewCollection::getUserId, userId);
        this.remove(wrapper);
    }
}
