package com.jettech.jettong.base.service.rbac.tenant;

import com.jettech.basic.base.service.SuperCacheService;
import com.jettech.jettong.base.entity.rbac.tenant.Tenant;
import com.jettech.jettong.base.enumeration.rbac.TenantStatus;

import java.util.List;

/**
 * 租户业务处理层接口
 *
 * <AUTHOR>
 * @version 1.0
 * @description 租户业务处理层接口
 * @projectName jettong
 * @package com.jettech.jettong.base.service.rbac.tenant
 * @className TenantService
 * @date 2021/9/15 9:43
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
public interface TenantService extends SuperCacheService<Tenant>
{

    /**
     * 检测 租户编码是否存在
     *
     * @param tenantCode 租户编码
     * @return boolean 是否存在
     * <AUTHOR>
     * @date 2021/11/5 15:47
     * @update zxy 2021/11/5 15:47
     * @since 1.0
     */
    boolean check(String tenantCode);

    /**
     * 保存租户信息
     *
     * @param tenant 租户信息
     * <AUTHOR>
     * @date 2021/11/5 15:47
     * @update zxy 2021/11/5 15:47
     * @since 1.0
     */
    void saveTenant(Tenant tenant);

    /**
     * 修改租户信息
     *
     * @param tenant 租户信息
     * <AUTHOR>
     * @date 2021/11/5 15:48
     * @update zxy 2021/11/5 15:48
     * @since 1.0
     */
    void updateTenant(Tenant tenant);

    /**
     * 根据编码获取租户信息
     *
     * @param code 租户编码
     * @return Tenant 租户信息
     * <AUTHOR>
     * @date 2021/11/5 15:49
     * @update zxy 2021/11/5 15:49
     * @since 1.0
     */
    Tenant getByCode(String code);

    /**
     * 根据租户id删除租户数据
     *
     * @param ids 租户id
     * <AUTHOR>
     * @date 2021/11/5 15:49
     * @update zxy 2021/11/5 15:49
     * @since 1.0
     */
    void deleteTenant(List<Long> ids);

    /**
     * 删除租户和基础数据
     *
     * @param ids 租户id
     * <AUTHOR>
     * @date 2021/11/5 15:50
     * @update zxy 2021/11/5 15:50
     * @since 1.0
     */
    void deleteAll(List<Long> ids);

    /**
     * 查询所有可用的租户
     *
     * @return List<Tenant> 所有可用的租户
     * <AUTHOR>
     * @date 2021/11/5 15:50
     * @update zxy 2021/11/5 15:50
     * @since 1.0
     */
    List<Tenant> find();

    /**
     * 修改租户状态
     *
     * @param ids 租户id集合
     * @param status 状态
     * <AUTHOR>
     * @date 2021/11/5 15:51
     * @update zxy 2021/11/5 15:51
     * @since 1.0
     */
    void updateStatus(List<Long> ids, TenantStatus status);
}
