package com.jettech.jettong.base.service.file.impl.minio;

import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.jettech.basic.utils.StrPool;
import com.jettech.jettong.base.dao.file.FileMapper;
import com.jettech.jettong.base.domain.file.FileDeleteBO;
import com.jettech.jettong.base.entity.file.File;
import com.jettech.jettong.base.enumeration.file.FileStorageType;
import com.jettech.jettong.base.properties.file.FileServerProperties;
import com.jettech.jettong.base.service.file.impl.AbstractFileStrategy;
import io.minio.*;
import io.minio.http.Method;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.FileInputStream;
import java.io.InputStream;


/**
 * Minio上传策略处理类
 *
 * <AUTHOR>
 * @version 1.0
 * @description Minio上传策略处理类
 * @projectName jettong
 * @package com.jettech.jettong.base.service.file.impl.minio
 * @className MinIoFileStrategyImpl
 * @date 2021/9/15 9:43
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Slf4j
@Component("MIN_IO")
@DS("#thread.tenant")
public class MinIoFileStrategyImpl extends AbstractFileStrategy
{
    private final MinioClient minioClient;

    /**
     * 桶占位符
     */
    private static final String BUCKET_PARAM = "${bucket}";

    public MinIoFileStrategyImpl(FileServerProperties fileProperties, MinioClient minioClient,
            FileMapper fileMapper)
    {
        super(fileProperties, fileMapper);
        this.minioClient = minioClient;
    }

    @Override
    protected void uploadFile(File file, MultipartFile multipartFile, String bucket) throws Exception
    {
        //生成文件名
        String uniqueFileName = getUniqueFileName(file);
        // 企业id/功能点/年/月/日/file
        String path = getPath(file.getBizType(), uniqueFileName);

        bucket = StrUtil.isEmpty(bucket) ? fileProperties.getMinIo().getBucket() : bucket;
        boolean exists = minioClient.bucketExists(BucketExistsArgs.builder().bucket(bucket).build());
        if (!exists)
        {
            minioClient.makeBucket(MakeBucketArgs.builder().bucket(bucket).build());
        }

        minioClient.putObject(PutObjectArgs.builder()
                .stream(multipartFile.getInputStream(), multipartFile.getSize(), PutObjectArgs.MIN_MULTIPART_SIZE)
                .object(path)
                .contentType(multipartFile.getContentType())
                .bucket(bucket)
                .build());

        file.setBucket(bucket);
        file.setPath(path);
        file.setUniqueFileName(uniqueFileName);
        file.setStorageType(FileStorageType.MIN_IO);
    }

    @Override
    @SneakyThrows
    public boolean delete(FileDeleteBO file)
    {
        String bucket = StrUtil.isEmpty(file.getBucket()) ? fileProperties.getMinIo().getBucket() : file.getBucket();
        minioClient.removeObject(RemoveObjectArgs.builder().bucket(bucket).object(file.getPath()).build());
        return true;
    }

    @Override
    @SneakyThrows
    public String findUrl(File file)
    {
        FileServerProperties.MinIo minIo = fileProperties.getMinIo();

        String bucket = StrUtil.isEmpty(file.getBucket()) ? minIo.getBucket() : file.getBucket();
        try
        {

                Integer expiry = minIo.getExpiry();
                return minioClient.getPresignedObjectUrl(GetPresignedObjectUrlArgs.builder()
                        .bucket(bucket).object(file.getPath()).method(Method.GET).expiry(expiry).build());
        }
        catch (Exception e)
        {
            log.warn("加载文件url地址失败，请确保yml中第三方存储参数配置正确. bucket={}, , 文件名={} path={}", bucket,
                    file.getOriginalFileName(), file.getPath(), e);
            return StrPool.EMPTY;
        }

    }

    @Override
    public Boolean updateFile(java.io.File file, File model) throws Exception
    {
        try (InputStream inputStream = new FileInputStream(file))
        {
            minioClient.putObject(PutObjectArgs.builder()
                    .stream(inputStream, file.length(), PutObjectArgs.MIN_MULTIPART_SIZE)
                    .object(model.getPath())
                    .contentType(model.getContentType())
                    .bucket(model.getBucket())
                    .build());
        }
        catch (Exception e)
        {
            log.error("修改文件失败，原因：{}", e.getMessage(), e);
            throw e;
        }
        return true;
    }
}
