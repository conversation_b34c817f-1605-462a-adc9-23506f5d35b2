package com.jettech.jettong.base.service.sys.dictionary.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.jettech.basic.base.service.SuperServiceImpl;
import com.jettech.basic.database.mybatis.conditions.Wraps;
import com.jettech.jettong.base.dao.sys.dictionary.DictionaryExtendedMapper;
import com.jettech.jettong.base.entity.sys.dictionary.DictionaryExtended;
import com.jettech.jettong.base.service.sys.dictionary.DictionaryExtendedService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 数据字典扩展信息业务层
 *
 * <AUTHOR>
 * @version 1.0
 * @description 数据字典扩展信息业务层
 * @projectName jettong
 * @package com.jettech.jettong.base.service.sys.dashboard.impl
 * @className DictionaryExtendedServiceImpl
 * @date 2021-10-15
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Slf4j
@Service
@DS("#thread.tenant")
@RequiredArgsConstructor
public class DictionaryExtendedServiceImpl extends SuperServiceImpl<DictionaryExtendedMapper, DictionaryExtended>
        implements DictionaryExtendedService
{
    @Override
    public List<DictionaryExtended> findByDictId(Long dictId)
    {
        return super.list(Wraps.<DictionaryExtended>lbQ().eq(DictionaryExtended::getDictId, dictId));
    }
}
