package com.jettech.jettong.base.dao.rbac.role;

import com.jettech.basic.base.mapper.SuperMapper;
import com.jettech.jettong.base.dto.rbac.role.FunctionQueryDTO;
import com.jettech.jettong.base.entity.rbac.role.Function;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 菜单功能持久化层接口
 *
 * <AUTHOR>
 * @version 1.0
 * @description 菜单功能持久化层接口
 * @projectName jettong
 * @package com.jettech.jettong.base.dao.rbac.role
 * @className FunctionMapper
 * @date 2021/9/15 9:43
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Repository
public interface FunctionMapper extends SuperMapper<Function>
{

    /**
     * 根据条件查询拥有的功能信息
     *
     * @param functionQueryDTO 查询条件
     * @return List<Function> 拥有的功能信息
     * <AUTHOR>
     * @date 2021/10/23 16:21
     * @update zxy 2021/10/23 16:21
     * @since 1.0
     */
    List<Function> findVisibleResource(FunctionQueryDTO functionQueryDTO);

    /**
     * 根据功能id查询菜单id
     *
     * @param functionIds 功能id集合
     * @return List<Long> 菜单id集合
     * <AUTHOR>
     * @date 2021/10/23 16:27
     * @update zxy 2021/10/23 16:27
     * @since 1.0
     */
    List<Long> findMenuIdByFunctionId(@Param("functionIds") List<Long> functionIds);
}
