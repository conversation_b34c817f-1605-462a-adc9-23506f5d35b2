package com.jettech.jettong.base.dao.sys.personalized;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.jettech.basic.base.mapper.SuperMapper;
import com.jettech.jettong.base.entity.sys.personalized.TableView;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import javax.annotation.Nullable;
import java.util.List;

/**
 * 筛选器信息Mapper 接口
 *
 * <AUTHOR>
 * @version 1.0
 * @description 筛选器信息Mapper 接口
 * @projectName jettong
 * @package com.jettech.jettong.base.base.dao.sys
 * @className TableViewMapper
 * @date 2023-03-23
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Repository
public interface TableViewMapper extends SuperMapper<TableView> {

    IPage<TableView> selectMyTableViews(@Param("userId") Long userId,
        IPage<TableView> page, @Param(Constants.WRAPPER) Wrapper<TableView> wrapper);

    /**
     * 查询 用户 项目下（可选）适用于table的筛选器
     * @param table     对应的表
     * @param userId    用户ID
     * @param projectId 项目ID
     * @return          可用的筛选器
     */
    List<TableView> selectEnableTableViews(@Param("table") String table,
            @Param("userId") Long userId, @Nullable @Param("projectId") Long projectId);
}
