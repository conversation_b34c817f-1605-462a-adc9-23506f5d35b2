package com.jettech.jettong.base.service.rbac.domain.user.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;

import java.util.List;

/**
 * 同步OA系统用户返回值
 * <AUTHOR>
 * @version 1.0
 * @description 同步OA系统用户返回值
 * @projectName HBSC
 * @package com.jettech.jettong.base.service.rbac.domain.user.model
 * @className SyncOaUserResult
 * @date 2024/4/22 9:59
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@lombok.Data
@Builder
public class SyncOaUserResult
{

    /**
     * 返回成功
     */
    public static final String RESULT_S = "S";

    /**
     * 数据交互对象
     */
    @JsonProperty("ESB")
    private SyncOaUserResult.Esb esb;

    /**
     * 同步OA系统用户返回值,数据交互对象
     * <AUTHOR>
     * @version 1.0
     * @description 同步OA系统用户返回值,数据交互对象
     * @projectName HBSC
     * @package com.jettech.jettong.base.service.rbac.domain.user.model
     * @className SyncOaUserResult.Esb
     * @date 2024/4/22 10:01
 * @copyright 2021 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
     */
    @lombok.Data
    public static class Esb
    {
        /**
         * 数据对象
         */
        @JsonProperty("DATA")
        private SyncOaUserResult.Esb.Data data;

        /**
         * S成功/E失败
         */
        @JsonProperty("RESULT")
        private String result;

        /**
         * 数据处理情况的描述
         */
        @JsonProperty("DESC")
        private String desc;

        /**
         * 同步OA系统用户返回值,数据对象
         * <AUTHOR>
         * @version 1.0
         * @description 同步OA系统用户返回值,数据对象
         * @projectName HBSC
         * @package com.jettech.jettong.base.service.rbac.domain.user.model
         * @className SyncOaUserResult.Esb.Data
         * @date 2024/4/22 10:01
 * @copyright 2021 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
         */
        @lombok.Data
        public static class Data
        {
            /**
             * 主数据集合
             */
            @JsonProperty("DATAINFOS")
            private SyncOaUserResult.Esb.Data.Datainfos datainfos;

            /**
             * 分页对象
             */
            @JsonProperty("SPLITPAGE")
            private SyncOaUserResult.Esb.Data.SplitPage splitPage;

            /**
             * 同步OA系统用户返回值,主数据集合
             * <AUTHOR>
             * @version 1.0
             * @description 同步OA系统用户返回值,主数据集合
             * @projectName HBSC
             * @package com.jettech.jettong.base.service.rbac.domain.user.model
             * @className SyncOaUserResult.Esb.Data.Datainfos
             * @date 2024/4/22 10:01
 * @copyright 2021 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
             */
            @lombok.Data
            public static class Datainfos
            {

                /**
                 * 数组对象
                 */
                @JsonProperty("DATAINFO")
                private List<SyncOaUserResult.Esb.Data.Datainfos.Datainfo> datainfo;

                /**
                 * 批数据的UUID
                 */
                @JsonProperty("PUUID")
                private String puuid;

                /**
                 * 同步OA系统用户返回值,数组对象
                 * <AUTHOR>
                 * @version 1.0
                 * @description 同步OA系统用户返回值,数组对象
                 * @projectName HBSC
                 * @package com.jettech.jettong.base.service.rbac.domain.user.model
                 * @className SyncOaUserResult.Esb.Data.Datainfos.Datainfo
                 * @date 2024/4/22 10:01
 * @copyright 2021 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
                 */
                @lombok.Data
                public static class Datainfo
                {
                    /**
                     * 集团工号
                     */
                    @JsonProperty("DESC3")
                    private String idCard;

                    /**
                     * 用户姓名
                     */
                    @JsonProperty("DESC1")
                    private String name;

                    /**
                     * 用户手机号
                     */
                    @JsonProperty("DESC10")
                    private String mobile;

                    /**
                     * 电子邮箱
                     */
                    @JsonProperty("DESC13")
                    private String email;

                    /**
                     * 所在部门编码
                     */
                    @JsonProperty("DESC31")
                    private String orgCode;
                }
            }

            /**
             * 同步OA系统用户返回值,分页参数
             * <AUTHOR>
             * @version 1.0
             * @description 同步OA系统用户返回值,分页参数
             * @projectName HBSC
             * @package com.jettech.jettong.base.service.rbac.domain.user.model
             * @className SyncOaUserResult.Esb.Data.SplitPage
             * @date 2024/4/22 10:01
 * @copyright 2021 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
             */
            @lombok.Data
            public static class SplitPage
            {

                /**
                 * 每页查询条数
                 * 默认200条
                 */
                @JsonProperty("COUNTPERPAGE")
                private Integer countPerPage;

                /**
                 * 当前页码
                 * 默认第1页
                 */
                @JsonProperty("CURRENTPAGE")
                private Integer currentPage;

                /**
                 * 总条数
                 */
                @JsonProperty("TOTALNUMBER")
                private Integer totalNumber;

                /**
                 * 总页数
                 */
                @JsonProperty("TOTALPAGES")
                private Integer totalPages;
            }
        }
    }
}
