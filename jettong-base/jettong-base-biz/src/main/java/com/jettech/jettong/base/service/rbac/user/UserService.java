package com.jettech.jettong.base.service.rbac.user;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jettech.basic.base.service.SuperCacheService;
import com.jettech.basic.database.mybatis.conditions.query.LbqWrapper;
import com.jettech.basic.model.LoadService;
import com.jettech.jettong.base.dto.rbac.user.UserExportQuery;
import com.jettech.jettong.base.dto.rbac.user.UserResetPasswordDTO;
import com.jettech.jettong.base.dto.rbac.user.UserUpdatePasswordDTO;
import com.jettech.jettong.base.entity.rbac.user.User;

import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 用户业务处理层接口
 *
 * <AUTHOR>
 * @version 1.0
 * @description 用户业务处理层接口
 * @projectName jettong
 * @package com.jettech.jettong.base.service.rbac.user
 * @className UserService
 * @date 2021/9/15 9:43
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
public interface UserService extends SuperCacheService<User>, LoadService
{

    /**
     * 批量新增用户信息
     *
     * @param users 用户信息
     * <AUTHOR>
     * @date 2021/12/13 14:24
     * @update zxy 2021/12/13 14:24
     * @since 1.0
     */
    void saveBatchUser(List<User> users);

    /**
     * 根据角色id 和 账号或名称 查询角色关联的用户
     * 注意，该接口只返回 id，账号，姓名，手机，性别
     *
     * @param roleId 角色id
     * @param keyword 账号或名称
     * @return List<User> 用户
     * <AUTHOR>
     * @date 2021/12/1 10:08
     * @update zxy 2021/12/1 10:08
     * @since 1.0
     */
    List<User> findUserByRoleId(Long roleId, String keyword);

    /**
     * 检测账号是否存在
     *
     * @param id id
     * @param account 账号
     * @return boolean 是否存在
     * <AUTHOR>
     * @date 2021/12/1 10:07
     * @update zxy 2021/12/1 10:07
     * @since 1.0
     */
    boolean check(Long id, String account);

    /**
     * 根据账号查询用户
     *
     * @param account 账号
     * @return User 用户
     * <AUTHOR>
     * @date 2021/12/1 10:07
     * @update zxy 2021/12/1 10:07
     * @since 1.0
     */
    User getByAccount(String account);

    /**
     * 新增用户
     *
     * @param user 用户信息
     * @return User 用户信息
     * <AUTHOR>
     * @date 2021/12/1 10:06
     * @update zxy 2021/12/1 10:06
     * @since 1.0
     */
    User saveUser(User user);

    /**
     * 管理员重置密码
     *
     * @param model 重置密码对象
     * @return boolean 是否成功
     * <AUTHOR>
     * @date 2021/12/1 10:06
     * @update zxy 2021/12/1 10:06
     * @since 1.0
     */
    boolean reset(UserResetPasswordDTO model);

    /**
     * 修改用户状态
     *
     * @param userId 用户id
     * @param state 是否正常
     * @return User 用户信息
     * <AUTHOR>
     * @date 2021/12/6 15:15
     * @update zxy 2021/12/6 15:15
     * @since 1.0
     */
    User updateStateById(Long userId, Boolean state);

    /**
     * 修改用户基本信息
     *
     * @param user 用户信息
     * @return User 用户信息
     * <AUTHOR>
     * @date 2021/12/1 10:05
     * @update zxy 2021/12/1 10:05
     * @since 1.0
     */
    User updateUser(User user);

    /**
     * 删除用户
     *
     * @param ids 用户id集合
     * @return boolean 是否成功
     * <AUTHOR>
     * @date 2021/12/1 10:05
     * @update zxy 2021/12/1 10:05
     * @since 1.0
     */
    boolean remove(List<Long> ids);

    /**
     * 用户分页查询
     *
     * @param page 分页对象
     * @param wrapper 查询条件
     * @return IPage<User> 用户分页数据
     * <AUTHOR>
     * @date 2021/12/1 10:04
     * @update zxy 2021/12/1 10:04
     * @since 1.0
     */
    IPage<User> findPage(IPage<User> page, LbqWrapper<User> wrapper);

    /**
     * 修改密码
     *
     * @param data 修改密码对象
     * @return Boolean 修改结果
     * <AUTHOR>
     * @date 2021/12/1 10:04
     * @update zxy 2021/12/1 10:04
     * @since 1.0
     */
    Boolean updatePassword(UserUpdatePasswordDTO data);

    /**
     * 查询所有用户的id
     *
     * @return List<Long> 用户id
     * <AUTHOR>
     * @date 2021/12/1 10:03
     * @update zxy 2021/12/1 10:03
     * @since 1.0
     */
    List<Long> findAllUserId();

    /**
     * 根据id集合查询用户
     *
     * @param ids id集合
     * @return List<User> 用户信息
     * <AUTHOR>
     * @date 2021/12/1 10:03
     * @update zxy 2021/12/1 10:03
     * @since 1.0
     */
    List<User> findUser(Set<Serializable> ids);

    /**
     * 根据id集合查询用户
     *
     * @param ids id集合
     * @return List<User> 用户信息
     * <AUTHOR>
     * @date 2021/12/1 10:03
     * @update zxy 2021/12/1 10:03
     * @since 1.0
     */
    List<User> findUserById(List<Long> ids);

    /**
     * 根据条件查询用户信息，包含角色和机构信息
     * 主要导出时使用该方法
     *
     * @param model 查询条件
     * @return List<Map < String, Object>> 用户信息，包含角色和机构信息
     * <AUTHOR>
     * @date 2021/12/1 10:08
     * @update zxy 2021/12/1 10:08
     * @since 1.0
     */
    List<Map<String, Object>> findUserByUserExportQuery(UserExportQuery model);

    /**
     * 根据机构id集合查询机构下直属用户信息
     *
     * @param orgIds 机构id集合
     * @return List<User> 机构下直属用户信息
     * <AUTHOR>
     * @date 2022/5/23 17:17
     * @update zxy 2022/5/23 17:17
     * @since 1.0
     */
    List<User> findByOrgIds(List<Long> orgIds);


    /**
     * 同步OA系统用户信息
     * @return {@link boolean} 同步结果
     * <AUTHOR>
     * @date 2024/4/19 14:42
     * @update 2024/4/19 14:42
     * @since 1.0
     */
    boolean syncOaUser();

}
