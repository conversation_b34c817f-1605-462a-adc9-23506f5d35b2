package com.jettech.jettong.base.service.rbac.role.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.jettech.basic.base.service.SuperCacheServiceImpl;
import com.jettech.basic.cache.model.CacheKeyBuilder;
import com.jettech.basic.database.mybatis.conditions.Wraps;
import com.jettech.basic.exception.BizException;
import com.jettech.jettong.base.dao.rbac.role.FunctionMapper;
import com.jettech.jettong.base.entity.rbac.role.Function;
import com.jettech.jettong.base.service.rbac.role.FunctionService;
import com.jettech.jettong.base.service.rbac.role.RoleAuthorityService;
import com.jettech.jettong.common.cache.base.rbac.role.FunctionCacheKeyBuilder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 菜单功能业务处理层
 *
 * <AUTHOR>
 * @version 1.0
 * @description 菜单功能业务处理层
 * @projectName jettong
 * @package com.jettech.jettong.base.service.rbac.role.impl
 * @className FunctionServiceImpl
 * @date 2021/9/15 9:43
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Slf4j
@Service
@DS("#thread.tenant")
@RequiredArgsConstructor
public class FunctionServiceImpl extends SuperCacheServiceImpl<FunctionMapper, Function> implements FunctionService
{

    private final RoleAuthorityService roleAuthorityService;

    @Override
    protected CacheKeyBuilder cacheKeyBuilder()
    {
        return new FunctionCacheKeyBuilder();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeByIdWithCache(List<Long> ids)
    {
        if (ids.isEmpty())
        {
            return true;
        }
        boolean result = this.removeByIds(ids);

        // 删除角色的权限
        roleAuthorityService.removeByAuthorityId(ids);
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void removeByMenuIdWithCache(List<Long> menuIds)
    {
        List<Function> functions = super.list(Wraps.<Function>lbQ().in(Function::getMenuId, menuIds));
        if (functions.isEmpty())
        {
            return;
        }
        List<Long> resourceIdList = functions.stream().map(Function::getId).collect(Collectors.toList());
        this.removeByIds(resourceIdList);

        List<Long> allIds = CollUtil.newArrayList(menuIds);
        allIds.addAll(resourceIdList);
        // 删除角色的权限
        roleAuthorityService.removeByAuthorityId(allIds);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveWithCache(Function function)
    {
        if (check(null, function.getMenuId(), function.getCode()))
        {
            throw BizException.validFail("新增失败,原因:编码[%s]已存在", function.getCode());
        }
        this.save(function);
        return true;
    }

    @Override
    public boolean check(Long id, Long menuId, String code)
    {
        return super.count(Wraps.<Function>lbQ().ne(Function::getId, id).eq(Function::getMenuId, menuId)
                .eq(Function::getCode, code)) > 0;
    }
}
