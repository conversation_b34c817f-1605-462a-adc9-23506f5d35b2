package com.jettech.jettong.base.service.rbac.role.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.jettech.basic.base.entity.SuperEntity;
import com.jettech.basic.base.entity.TreeEntity;
import com.jettech.basic.base.service.SuperCacheServiceImpl;
import com.jettech.basic.cache.model.CacheKey;
import com.jettech.basic.cache.model.CacheKeyBuilder;
import com.jettech.basic.context.ContextUtil;
import com.jettech.basic.database.mybatis.conditions.Wraps;
import com.jettech.basic.exception.BizException;
import com.jettech.basic.utils.ArgumentAssert;
import com.jettech.basic.utils.BeanPlusUtil;
import com.jettech.basic.utils.TreeUtil;
import com.jettech.basic.uuid.UidGeneratorUtil;
import com.jettech.jettong.base.dao.rbac.role.MenuMapper;
import com.jettech.jettong.base.dto.rbac.role.MenuFunctionTreeVO;
import com.jettech.jettong.base.entity.rbac.role.Function;
import com.jettech.jettong.base.entity.rbac.role.Menu;
import com.jettech.jettong.base.enumeration.rbac.AuthorizeType;
import com.jettech.jettong.base.service.rbac.role.FunctionService;
import com.jettech.jettong.base.service.rbac.role.MenuService;
import com.jettech.jettong.base.service.rbac.user.UserService;
import com.jettech.jettong.base.vo.rbac.role.OtherMenusVo;
import com.jettech.jettong.common.cache.base.rbac.role.MenuManageCacheKeyBuilder;
import com.jettech.jettong.common.cache.base.rbac.role.RoleResourceCacheKeyBuilder;
import com.jettech.jettong.common.cache.base.rbac.user.UserMenuCacheKeyBuilder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

import static com.jettech.basic.utils.StrPool.DEF_PARENT_ID;

/**
 * 菜单业务处理层
 *
 * <AUTHOR>
 * @version 1.0
 * @description 菜单业务处理层
 * @projectName jettong
 * @package com.jettech.jettong.base.service.rbac.role.impl
 * @className MenuServiceImpl
 * @date 2021/9/15 9:43
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Slf4j
@Service
@DS("#thread.tenant")
@RequiredArgsConstructor
public class MenuServiceImpl extends SuperCacheServiceImpl<MenuMapper, Menu> implements MenuService
{
    private static final int MAX_RECURSION_DEPTH = 20;

    private final FunctionService functionService;
    private final UserService userService;

    @Override
    protected CacheKeyBuilder cacheKeyBuilder()
    {
        return new MenuManageCacheKeyBuilder();
    }

    @Override
    public boolean check(Long id, String code)
    {
        return super.count(Wraps.<Menu>lbQ().ne(Menu::getId, id).eq(Menu::getCode, code)) > 0;
    }

    @Override
    public List<Menu> findVisibleMenu(Long userId)
    {
        // fixme 与Oauth中使用相同的键，缓存的数据不一样，此处不用该缓存
        CacheKey userMenuKey = new UserMenuCacheKeyBuilder().key(userId);
        List<Menu> visibleMenu = new ArrayList<>();

        List<Long> list = cacheOps.get(userMenuKey, k ->
        {
            log.info("userMenuKey={}", userMenuKey.getKey());
            visibleMenu.addAll(baseMapper.findVisibleMenu(userId));
            return visibleMenu.stream().map(Menu::getId).collect(Collectors.toList());
        });
        log.info("visibleMenu={}", visibleMenu.size());
        if (!visibleMenu.isEmpty())
        {
            visibleMenu.forEach(this::setCache);
        }
        else
        {
            log.info("list={}", list.size());
            visibleMenu.addAll(findByIds(list, this::listByIds));
        }
        log.info("visibleMenu2={}", visibleMenu.size());
        return visibleMenu;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeByIdWithCache(List<Long> ids)
    {
        if (ids.isEmpty())
        {
            return true;
        }
        // 有子级菜单不能删除
        List<Menu> menuList = this.list(Wraps.<Menu>lbQ().in(Menu::getParentId, ids));
        if (CollUtil.isNotEmpty(menuList)) {
            List<String> names = menuList.stream().map(TreeEntity::getName).collect(Collectors.toList());
            throw BizException.wrap("存在【子级菜单】不能删除。请先删除子级菜单：%s", names);
        }

        List<Function> functions = functionService.list(Wraps.<Function>lbQ().in(Function::getMenuId, ids));
        if (CollUtil.isNotEmpty(functions)) {
            List<String> names = functions.stream().map(Function::getName).collect(Collectors.toList());
            throw BizException.wrap("存在【接口权限】不能删除。请先删除权限功能：%s", names);
        }

        functionService.removeByMenuIdWithCache(ids);
        return this.removeByIds(ids);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateWithCache(Menu menu)
    {
        Menu old = getById(menu);
        ArgumentAssert.notNull(old, "您修改的菜单已不存在");

        List<Long> userIds = userService.findAllUserId();
        cacheOps.del(userIds.stream().map(new UserMenuCacheKeyBuilder()::key).toArray(CacheKey[]::new));

        return this.updateById(menu);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveWithCache(Menu menu)
    {
        if (check(null, menu.getCode()))
        {
            throw BizException.validFail("新增失败,原因:编码[%s]已存在", menu.getCode());
        }
        menu.setState(Convert.toBool(menu.getState(), true));
        menu.setIsGeneral(Convert.toBool(menu.getIsGeneral(), false));
        menu.setParentId(Convert.toLong(menu.getParentId(), DEF_PARENT_ID));
        save(menu);

        if (menu.getIsGeneral())
        {
            List<Long> userIds = userService.findAllUserId();
            cacheOps.del(userIds.stream().map(new UserMenuCacheKeyBuilder()::key).toArray(CacheKey[]::new));
            cacheOps.del(userIds.stream().map(new RoleResourceCacheKeyBuilder()::key).toArray(CacheKey[]::new));
        }
        return true;
    }

    @Override
    public List<MenuFunctionTreeVO> findMenuFunctionTree(Menu menu)
    {
        List<MenuFunctionTreeVO> list = new ArrayList<>();
        List<Menu> menus = super.list();
        if(StringUtils.isEmpty(menu.getPlatform())|| "0".equals(menu.getPlatform())){
            List<MenuFunctionTreeVO> menuList = menus.stream()
                    .map(MenuServiceImpl::convertVo)
                    .collect(Collectors.toList());
            list.addAll(menuList);
            return TreeUtil.buildTree(list);
        }else{
            List<MenuFunctionTreeVO> voList = menus.stream()
                    .filter(m -> m.getPlatform().equals(menu.getPlatform()))
                    .map(MenuServiceImpl::convertVo)
                    .collect(Collectors.toList());
            return TreeUtil.buildTree(voList);
        }

    }

    private static MenuFunctionTreeVO convertVo(Menu item) {
        MenuFunctionTreeVO menu = new MenuFunctionTreeVO();
        BeanPlusUtil.copyProperties(item, menu);
        menu.setType(item.getIsButton() ? AuthorizeType.FUNCTION : AuthorizeType.MENU);
        return menu;
    }

    @Override
    public List<MenuFunctionTreeVO> currentMenuFunctionTree(Menu menu) {
        Long userId = ContextUtil.getUserId();

        // 超级管理员直接返回全部的菜单
        if (userId == 1) {
            return findMenuFunctionTree(menu);
        }
        List<Menu> menuList = baseMapper.findVisibleMenu(userId);
        // 补充缺失的父级菜单
        supplementParent(menuList, 0);
        if(StringUtils.isEmpty(menu.getPlatform())|| "0".equals(menu.getPlatform())){
            List<MenuFunctionTreeVO> voList = menuList.stream()
                    .map(MenuServiceImpl::convertVo)
                    .collect(Collectors.toList());
            return TreeUtil.buildTree(voList);
        }else{
            List<MenuFunctionTreeVO> voList = menuList.stream()
                    .filter(m -> m.getPlatform().equals(menu.getPlatform()))
                    .map(MenuServiceImpl::convertVo)
                    .collect(Collectors.toList());
            return TreeUtil.buildTree(voList);
        }
    }

    /**
     * 补充缺失的父级菜单
     * @param menus 菜单集合
     * @param depth 递归深度
     */
    private void supplementParent(List<Menu> menus, int depth) {
        Set<Long> ids = menus.stream().map(SuperEntity::getId).collect(Collectors.toSet());
        Set<Long> parentIds = menus.stream()
                .map(TreeEntity::getParentId)
                .filter(Objects::nonNull)
                .filter(id -> id != 0L)
                .collect(Collectors.toSet());

        parentIds.removeAll(ids);
        if (parentIds.isEmpty()) {
            return;
        }

        // 达到最大递归深度时，认为该菜单的父级已经被删除，不返回该菜单
        if (depth >= MAX_RECURSION_DEPTH) {
            menus.removeIf(m -> parentIds.contains(m.getParentId()));
        }

        menus.addAll(this.listByIds(parentIds));
        supplementParent(menus, depth + 1);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<Menu> addOrUpdateMenuAndFunction(List<OtherMenusVo> menus , String platform)
    {
        if (CollUtil.isEmpty(menus)) {
            return new ArrayList<>();
        }
        Map<String, Long> platformMap = new HashMap<>();
        platformMap.put("1",0L);
        platformMap.put("2",1946049125682249728L);
        platformMap.put("4",1946049054840455168L);
        platformMap.put("16",1946049431400873984L);
        platformMap.put("32",0L);
        if(platformMap.get(platform) == null){
            //抛出异常
            throw BizException.wrap("平台不存在，请联系管理员");
        }
        // 1. 收集所有菜单节点（扁平化树结构）
        List<OtherMenusVo> allMenuNodes = new ArrayList<>();
        collectAllMenuNodes(menus, allMenuNodes);

        // 2. 批量查询现有菜单
        Set<String> allCodes = allMenuNodes.stream().map(OtherMenusVo::getCode).collect(Collectors.toSet());
        List<Menu> existingMenus = list(Wraps.<Menu>lbQ().in(Menu::getCode, allCodes).eq(Menu::getPlatform, platform));
        Map<String, Menu> existingMenuMap = existingMenus.stream()
                .collect(Collectors.toMap(Menu::getCode,  java.util.function.Function.identity()));

        // 3. 批量查询现有功能权限
        Set<Long> existingMenuIds = existingMenus.stream().map(Menu::getId).collect(Collectors.toSet());
        List<Function> existingFunctions = functionService.list(Wraps.<Function>lbQ().in(Function::getMenuId, existingMenuIds));
        Map<Long, List<Function>> existingFunctionMap = existingFunctions.stream()
                .collect(Collectors.groupingBy(Function::getMenuId));

        // 4. 准备批量操作的数据
        List<Menu> menusToSave = new ArrayList<>();
        List<Menu> menusToUpdate = new ArrayList<>();
        List<Function> functionsToSave = new ArrayList<>();
        List<Long> functionIdsToDelete = new ArrayList<>();

        // 5. 处理树结构菜单
        Map<String, Long> codeToIdMap = new HashMap<>();
        for (OtherMenusVo rootMenu : menus) {
            processMenuTreeBatch(rootMenu, platformMap.get(platform), existingMenuMap, existingFunctionMap,
                    menusToSave, menusToUpdate, functionsToSave, functionIdsToDelete, codeToIdMap, platform);
        }

        // 6. 批量执行数据库操作
        executeBatchOperations(menusToSave, menusToUpdate, functionsToSave, functionIdsToDelete);

        // 7. 清除缓存
        clearMenuCache();

        // 8. 返回所有处理的菜单
        List<Menu> resultMenus = new ArrayList<>();
        resultMenus.addAll(menusToSave);
        resultMenus.addAll(menusToUpdate);

        return resultMenus;
    }

    /**
     * 递归收集所有菜单节点（扁平化）
     */
    private void collectAllMenuNodes(List<OtherMenusVo> menus, List<OtherMenusVo> allNodes) {
        for (OtherMenusVo menu : menus) {
            allNodes.add(menu);
            if (CollUtil.isNotEmpty(menu.getChildren())) {
                collectAllMenuNodes(menu.getChildren(), allNodes);
            }
        }
    }

    /**
     * 批量处理菜单树
     */
    private void processMenuTreeBatch(OtherMenusVo menuVo, Long parentId,
            Map<String, Menu> existingMenuMap,
            Map<Long, List<Function>> existingFunctionMap,
            List<Menu> menusToSave, List<Menu> menusToUpdate,
            List<Function> functionsToSave, List<Long> functionIdsToDelete,
            Map<String, Long> codeToIdMap, String platform) {

        Menu existingMenu = existingMenuMap.get(menuVo.getCode());
        Menu currentMenu;

        if (existingMenu != null) {
            // 更新现有菜单
            currentMenu = prepareMenuForUpdate(existingMenu, menuVo, parentId, platform);
            menusToUpdate.add(currentMenu);
            codeToIdMap.put(menuVo.getCode(), existingMenu.getId());
//
//            if (CollUtil.isNotEmpty(menuVo.getFunctions())) {
//                List<Function> oldFunctions = existingFunctionMap.get(existingMenu.getId());
//                if (CollUtil.isNotEmpty(oldFunctions)) {
//                    functionIdsToDelete.addAll(oldFunctions.stream().map(Function::getId).collect(Collectors.toList()));
//                }
//                prepareFunctionsForSave(menuVo.getFunctions(), existingMenu.getId(), functionsToSave, platform);
//            }
        } else {
            // 新增菜单
            currentMenu = prepareMenuForSave(menuVo, parentId, platform);
            menusToSave.add(currentMenu);
// 临时ID用于处理子菜单关系
            Long tempId = UidGeneratorUtil.getId();
            codeToIdMap.put(menuVo.getCode(), tempId);
            currentMenu.setId(tempId);
        }

        // 递归处理子菜单
        if (CollUtil.isNotEmpty(menuVo.getChildren())) {
            Long currentMenuId = codeToIdMap.get(menuVo.getCode());
            for (OtherMenusVo child : menuVo.getChildren()) {
                processMenuTreeBatch(child, currentMenuId, existingMenuMap, existingFunctionMap,
                        menusToSave, menusToUpdate, functionsToSave, functionIdsToDelete, codeToIdMap, platform);
            }
        }
    }

    /**
     * 准备更新的菜单
     */
    private Menu prepareMenuForUpdate(Menu existingMenu, OtherMenusVo menuVo, Long parentId, String platform) {
        Menu bean = BeanPlusUtil.toBean(menuVo, Menu.class);
        //existingMenu.setName(menuVo.getName());
        bean.setParentId(parentId);
      //  existingMenu.setExternalUrl(menuVo.getExternalUrl());
        bean.setPlatform(platform);
        bean.setId(existingMenu.getId());
        return bean;
    }

    /**
     * 准备新增的菜单
     */
    private Menu prepareMenuForSave(OtherMenusVo menuVo, Long parentId, String platform) {
        Menu menu = BeanPlusUtil.toBean(menuVo, Menu.class);
        menu.setParentId(parentId);
//        menu.setState(true);
//        menu.setIsGeneral(false);
//        menu.setIsButton(false);
//        menu.setPath(menuVo.getPath());
//        menu.setSort(menuVo.getSort() != null ? menuVo.getSort() : 1);
        menu.setPlatform(platform);
        return menu;
    }

    /**
     * 准备新增的功能权限
     */
    private void prepareFunctionsForSave(List<Function> functions, Long menuId, List<Function> functionsToSave, String platform) {
        for (Function function : functions) {
            Function newFunction = new Function();
            BeanPlusUtil.copyProperties(function, newFunction);
            newFunction.setMenuId(menuId);
            functionsToSave.add(newFunction);
        }
    }

    /**
     * 批量执行数据库操作
     */
    private void executeBatchOperations(List<Menu> menusToSave, List<Menu> menusToUpdate,
                                      List<Function> functionsToSave, List<Long> functionIdsToDelete) {

        // 1. 批量新增菜单
        if (CollUtil.isNotEmpty(menusToSave)) {
            saveBatch(menusToSave);

            // 更新功能权限中的菜单ID（处理新增菜单的功能权限）
            Map<Long, Long> tempIdToRealIdMap = new HashMap<>();
            for (Menu menu : menusToSave) {
                Long tempId = menu.getId();
                // 重新查询获取真实ID
                Menu savedMenu = getOne(Wraps.<Menu>lbQ().eq(Menu::getCode, menu.getCode()));
                if (savedMenu != null) {
                    tempIdToRealIdMap.put(tempId, savedMenu.getId());
                }
            }

            // 更新功能权限的菜单ID
            for (Function function : functionsToSave) {
                Long realMenuId = tempIdToRealIdMap.get(function.getMenuId());
                if (realMenuId != null) {
                    function.setMenuId(realMenuId);
                }
            }
        }

        // 2. 批量更新菜单
        if (CollUtil.isNotEmpty(menusToUpdate)) {
            updateBatchById(menusToUpdate);
        }

        // 3. 批量删除功能权限
        if (CollUtil.isNotEmpty(functionIdsToDelete)) {
            functionService.removeByIds(functionIdsToDelete);
        }

        // 4. 批量新增功能权限
        if (CollUtil.isNotEmpty(functionsToSave)) {
            functionService.saveBatch(functionsToSave);
        }
    }

    /**
     * 清除菜单相关缓存
     */
    private void clearMenuCache() {
        List<Long> userIds = userService.findAllUserId();
        if (CollUtil.isNotEmpty(userIds)) {
            cacheOps.del(userIds.stream().map(new UserMenuCacheKeyBuilder()::key).toArray(CacheKey[]::new));
            cacheOps.del(userIds.stream().map(new RoleResourceCacheKeyBuilder()::key).toArray(CacheKey[]::new));
        }
    }
}
