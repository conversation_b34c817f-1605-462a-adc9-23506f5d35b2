package com.jettech.jettong.base.websocket;

import org.springframework.context.annotation.Bean;
import org.springframework.web.socket.server.standard.ServerEndpointExporter;

/**
 * websocket 配置类
 *
 * <AUTHOR>
 * @version 1.0
 * @description websocket 配置类
 * @projectName jettong
 * @package com.jettech.jettong.base.ws
 * @className WebsocketConfiguration
 * @date 2021/9/15 9:43
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
//@Configuration
public class WebsocketConfiguration
{

    @Bean
    public ServerEndpointExporter serverEndpointExporter()
    {
        return new ServerEndpointExporter();
    }

}