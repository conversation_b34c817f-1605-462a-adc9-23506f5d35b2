package com.jettech.jettong.base.service.sys.dictionary;

import com.jettech.basic.base.service.SuperCacheService;
import com.jettech.basic.model.LoadService;
import com.jettech.jettong.base.entity.sys.dictionary.Dictionary;

import java.util.List;
import java.util.Map;

/**
 * 数据字典信息表业务接口
 *
 * <AUTHOR>
 * @version 1.0
 * @description 数据字典信息表业务接口
 * @projectName jettong
 * @package com.jettech.jettong.base.service.sys.dictionary
 * @className DictionaryService
 * @date 2021-10-15
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
public interface DictionaryService extends SuperCacheService<Dictionary>, LoadService
{

    /**
     * 添加数据字典信息
     *
     * @param data 数据字典信息
     * <AUTHOR>
     * @date 2021/10/15 11:01
     * @update zxy 2021/10/15 11:01
     * @since 1.0
     */
    void saveDictionary(Dictionary data);

    /**
     * 批量删除数据字典信息
     *
     * @param ids 数据字典ID
     * <AUTHOR>
     * @date 2021/10/15 11:02
     * @update zxy 2021/10/15 11:02
     * @since 1.0
     */
    void remove(List<Long> ids);

    /**
     * 修改数据字典信息
     *
     * @param data 数据字典对象
     * <AUTHOR>
     * @date 2021/10/15 11:06
     * @update zxy 2021/10/15 11:06
     * @since 1.0
     */
    void updateDictionary(Dictionary data);

    /**
     * 修改字典状态
     *
     * @param id 字典id
     * @param state 是否启用
     * @return Dictionary 字典信息
     * <AUTHOR>
     * @date 2021/12/6 16:24
     * @update zxy 2021/12/6 16:24
     * @since 1.0
     */
    Dictionary updateStateById(Long id, Boolean state);

    /**
     * 重写根据id获取对象方法, 将扩展属性放入
     *
     * @param id 主键id
     * @return Dictionary 字典信息
     * <AUTHOR>
     * @date 2021/10/15 15:36
     * @update zxy 2021/10/15 15:36
     * @since 1.0
     */
    Dictionary getByIdCache(Long id);

    /**
     * 根据父级key查询字典信息
     *
     * @param parentKey 父级key
     * @param dictionaryType 父级字典类型
     * @return List<Dictionary> 字典信息
     * <AUTHOR>
     * @date 2021/11/1 16:36
     * @update zxy 2021/11/1 16:36
     * @since 1.0
     */
    List<Dictionary> findByParentKey(String dictionaryType, String parentKey);

    /**
     * 检测编码是否可用
     *
     * @param code 编码
     * @param type 字典类型
     * @param parentId 父id
     * @return boolean 是否可用
     * <AUTHOR>
     * @date 2021/11/2 15:17
     * @update zxy 2021/11/2 15:17
     * @since 1.0
     */
    boolean check(String code, String type, Long parentId);

    /**
     * 批量推送字典类型
     *
     * @param dictionarys 需要新增或修改的数据
     * @return Boolean 是否成功
     * <AUTHOR>
     * @date 2025/8/18 15:17
     * @update tmm 2025/8/18 15:17
     * @since 1.0
     */
    Boolean addBatchDictionaryType(List<Dictionary> dictionarys);
}
