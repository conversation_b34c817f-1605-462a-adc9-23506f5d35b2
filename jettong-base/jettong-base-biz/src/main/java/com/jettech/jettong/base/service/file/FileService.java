package com.jettech.jettong.base.service.file;

import com.jettech.basic.base.service.SuperService;
import com.jettech.basic.model.LoadService;
import com.jettech.jettong.base.entity.file.File;
import com.jettech.jettong.base.vo.file.param.FileUploadVO;
import com.jettech.jettong.base.vo.file.result.FileResultVO;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 文件上传业务处理层接口
 *
 * <AUTHOR>
 * @version 1.0
 * @description 文件上传业务处理层接口
 * @projectName jettong
 * @package com.jettech.jettong.base.service.file
 * @className FileService
 * @date 2021/9/15 9:43
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
public interface FileService extends SuperService<File>, LoadService
{

    /**
     * 上传附件
     *
     * @param file 文件
     * @param attachmentVO 参数
     * @return 附件
     */
    FileResultVO upload(MultipartFile file, FileUploadVO attachmentVO);

    /**
     * 批量删除文件附件信息
     *
     * @param ids 文件id集合
     * @return boolean 删除结果
     * <AUTHOR>
     * @date 2021/12/20 15:39
     * @update zxy 2021/12/20 15:39
     * @since 1.0
     */
    boolean removeFileByIds(List<Long> ids);

    /**
     * 下载文件
     *
     * @param request request
     * @param response response
     * @param ids 文件id
     * @return
     * @throws Exception Exception
     * <AUTHOR>
     * @date 2022/12/5 14:14
     * @update 2022/12/5 14:14
     * @since 1.0
     */
    void download(HttpServletRequest request, HttpServletResponse response, List<Long> ids) throws Exception;

    /**
     * office文件在线编辑后保存接口
     *
     * @param id 文件id
     * @param sessionUserId 当前登录用户id
     * @param file 文件
     * <AUTHOR>
     * @date 2022/12/5 14:15
     * @update 2022/12/5 14:15
     * @since 1.0
     */
    void updateFile(Long id, Long sessionUserId, java.io.File file);

}
