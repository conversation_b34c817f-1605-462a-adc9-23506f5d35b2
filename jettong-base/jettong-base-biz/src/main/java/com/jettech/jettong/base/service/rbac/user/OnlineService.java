package com.jettech.jettong.base.service.rbac.user;

import com.jettech.jettong.base.dto.rbac.user.Online;

import java.util.List;

/**
 * 在线用户业务处理层接口
 *
 * <AUTHOR>
 * @version 1.0
 * @description 在线用户业务处理层接口
 * @projectName jettong
 * @package com.jettech.jettong.base.service.rbac.user
 * @className OnlineService
 * @date 2021/9/15 9:43
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
public interface OnlineService
{
    /**
     * 查询在线用户
     *
     * @param name 名称
     * @return List<Online> 在线用户
     * <AUTHOR>
     * @date 2021/11/6 17:17
     * @update zxy 2021/11/6 17:17
     * @since 1.0
     */
    List<Online> list(String name);


    /**
     * 保存在线用户
     *
     * @param model 在线用户
     * @return boolean 保存结果
     * <AUTHOR>
     * @date 2021/11/6 17:18
     * @update zxy 2021/11/6 17:18
     * @since 1.0
     */
    boolean save(Online model);

    /**
     * 清理在线用户
     *
     * @param token token
     * @param userId userId
     * @param clientId clientId
     * @return boolean
     * @throws
     * <AUTHOR>
     * @date 2021/11/6 17:18
     * @update zxy 2021/11/6 17:18
     * @since 1.0
     */
    boolean clear(String token, Long userId, String clientId);
}
