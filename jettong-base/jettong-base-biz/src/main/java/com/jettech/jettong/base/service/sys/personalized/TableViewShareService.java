package com.jettech.jettong.base.service.sys.personalized;

import com.jettech.basic.base.service.SuperService;
import com.jettech.jettong.base.entity.sys.personalized.TableViewShare;

import java.util.List;

/**
 * 筛选器收藏夹业务接口
 * <AUTHOR>
 * @version 1.0
 * @description 筛选器收藏夹业务接口
 * @projectName jettong
 * @package com.jettech.jettong.base.base.service.sys
 * @className TableViewCollectionService
 * @date 2023-03-23
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
public interface TableViewShareService extends SuperService<TableViewShare>
{

    /**
     * 分享筛选器
     *
     * @param viewIds    筛选器ID
     * @param userIds   分享到用户列表
     */
    void share(List<Long> viewIds, List<Long> userIds);

    /**
     * 取消关联的筛选器
     *
     * @param viewIds   筛选器ID
     * @param userIds   用户列表
     */
    void unshare(List<Long> viewIds, List<Long> userIds);
}
