package com.jettech.jettong.base.service.rbac.role;

import com.jettech.basic.base.service.SuperCacheService;
import com.jettech.basic.model.LoadService;
import com.jettech.jettong.base.dto.rbac.role.RoleSaveDTO;
import com.jettech.jettong.base.dto.rbac.role.RoleUpdateDTO;
import com.jettech.jettong.base.entity.rbac.role.Role;

import java.util.List;

/**
 * 角色业务处理层接口
 *
 * <AUTHOR>
 * @version 1.0
 * @description 角色业务处理层接口
 * @projectName jettong
 * @package com.jettech.jettong.base.service.rbac.role
 * @className RoleService
 * @date 2021/9/15 9:43
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
public interface RoleService extends SuperCacheService<Role>, LoadService
{

    /**
     * 根据ID删除
     *
     * @param ids id
     * @return 是否成功
     */
    boolean removeByIdWithCache(List<Long> ids);

    /**
     * 判断用户是否 租户系统的超级管理员
     *
     * @param code 角色编码
     * @return 是否成功
     */
    boolean isPtAdmin(String code);

    /**
     * 查询用户拥有的角色
     *
     * @param userId 用户id
     * @return 角色
     */
    List<Role> findRoleByUserId(Long userId);

    /**
     * 保存角色
     *
     * @param data 角色
     * @param userId 用户id
     */
    void saveRole(RoleSaveDTO data, Long userId);

    /**
     * 修改
     *
     * @param role 角色
     * @param userId 用户id
     */
    void updateRole(RoleUpdateDTO role, Long userId);

    /**
     * 根据角色编码查询用户ID
     *
     * @param codes 角色编码
     * @return 用户id
     */
    List<Long> findUserIdByCode(String[] codes);

    /**
     * 检测编码重复
     *
     * @param code 角色编码
     * @return 存在返回真
     */
    Boolean check(String code);
}
