package com.jettech.jettong.base.service.sys.log.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.jettech.basic.base.service.SuperServiceImpl;
import com.jettech.basic.database.mybatis.conditions.Wraps;
import com.jettech.basic.log.entity.OptLogDTO;
import com.jettech.basic.utils.BeanPlusUtil;
import com.jettech.jettong.base.dao.sys.log.OptLogDetailMapper;
import com.jettech.jettong.base.dao.sys.log.OptLogMapper;
import com.jettech.jettong.base.dto.sys.log.OptLogResult;
import com.jettech.jettong.base.entity.sys.log.OptLog;
import com.jettech.jettong.base.entity.sys.log.OptLogDetail;
import com.jettech.jettong.base.service.sys.log.OptLogService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

/**
 * 操作日志业务处理层
 *
 * <AUTHOR>
 * @version 1.0
 * @description 操作日志业务处理层
 * @projectName jettong
 * @package com.jettech.jettong.base.service.sys.log.impl
 * @className OptLogServiceImpl
 * @date 2021/9/15 9:43
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Slf4j
@Service
@DS("#thread.tenant")
@RequiredArgsConstructor
public class OptLogServiceImpl extends SuperServiceImpl<OptLogMapper, OptLog> implements OptLogService
{
    private final OptLogDetailMapper optLogDetailMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void save(OptLogDTO entity)
    {
        OptLog optLog = BeanPlusUtil.toBean(entity, OptLog.class);
        OptLogDetail optLogDetail = BeanPlusUtil.toBean(entity, OptLogDetail.class);

        super.save(optLog);
        optLogDetail.setOptLogId(optLog.getId());
        optLogDetailMapper.insert(optLogDetail);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void clearLog(LocalDateTime clearBeforeTime, Integer clearBeforeNum)
    {
        optLogDetailMapper.clearLog(clearBeforeTime, clearBeforeNum);
        baseMapper.clearLog(clearBeforeTime, clearBeforeNum);
    }

    @Override
    public OptLogResult getOptLogResultById(Long id)
    {
        OptLog opt = getById(id);
        OptLogResult result = BeanPlusUtil.toBean(opt, OptLogResult.class);
        OptLogDetail optLogDetail = optLogDetailMapper.selectOne(
                Wraps.<OptLogDetail>lbQ().eq(OptLogDetail::getOptLogId, id).last(" limit 1"));
        BeanPlusUtil.copyProperties(optLogDetail, result);
        return result;
    }

}
