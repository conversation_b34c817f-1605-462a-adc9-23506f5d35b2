package com.jettech.jettong.base.dao.rbac.role;

import com.jettech.basic.base.mapper.SuperMapper;
import com.jettech.jettong.base.entity.rbac.role.Menu;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 菜单持久化层接口
 *
 * <AUTHOR>
 * @version 1.0
 * @description 菜单持久化层接口
 * @projectName jettong
 * @package com.jettech.jettong.base.dao.rbac.role
 * @className MenuMapper
 * @date 2021/9/15 9:43
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Repository
public interface MenuMapper extends SuperMapper<Menu>
{

    /**
     * 查询用户可用菜单
     *
     * @param userId 用户id
     * @return 菜单
     */
    List<Menu> findVisibleMenu(@Param("userId") Long userId);
}
