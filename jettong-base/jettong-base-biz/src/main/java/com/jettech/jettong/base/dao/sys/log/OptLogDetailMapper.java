package com.jettech.jettong.base.dao.sys.log;

import com.jettech.basic.base.mapper.SuperMapper;
import com.jettech.jettong.base.entity.sys.log.OptLogDetail;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;

/**
 * 操作日志详情持久化层接口
 *
 * <AUTHOR>
 * @version 1.0
 * @description 操作日志详情持久化层接口
 * @projectName jettong
 * @package com.jettech.jettong.base.dao.sys.log
 * @className OptLogDetailMapper
 * @date 2021/9/15 9:43
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Repository
public interface OptLogDetailMapper extends SuperMapper<OptLogDetail>
{
    /**
     * 清理日志
     *
     * @param clearBeforeTime 多久之前的
     * @param clearBeforeNum 多少条
     * @return 是否成功
     */
    Long clearLog(@Param("clearBeforeTime") LocalDateTime clearBeforeTime,
            @Param("clearBeforeNum") Integer clearBeforeNum);
}
