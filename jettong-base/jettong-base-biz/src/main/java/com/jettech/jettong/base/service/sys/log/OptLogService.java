package com.jettech.jettong.base.service.sys.log;

import com.jettech.basic.base.service.SuperService;
import com.jettech.basic.log.entity.OptLogDTO;
import com.jettech.jettong.base.dto.sys.log.OptLogResult;
import com.jettech.jettong.base.entity.sys.log.OptLog;

import java.time.LocalDateTime;

/**
 * 操作日志业务处理层接口
 *
 * <AUTHOR>
 * @version 1.0
 * @description 操作日志业务处理层接口
 * @projectName jettong
 * @package com.jettech.jettong.base.service.sys.log
 * @className OptLogService
 * @date 2021/9/15 9:43
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
public interface OptLogService extends SuperService<OptLog>
{

    /**
     * 保存操作日志
     *
     * @param optLogDTO 操作日志
     * <AUTHOR>
     * @date 2021/11/1 15:15
     * @update zxy 2021/11/1 15:15
     * @since 1.0
     */
    void save(OptLogDTO optLogDTO);

    /**
     * 清理日志
     *
     * @param clearBeforeTime 多久之前的
     * @param clearBeforeNum 多少条
     * <AUTHOR>
     * @date 2021/11/1 15:15
     * @update zxy 2021/11/1 15:15
     * @since 1.0
     */
    void clearLog(LocalDateTime clearBeforeTime, Integer clearBeforeNum);

    /**
     * 查询操作日志详情
     *
     * @param id id
     * @return 详情
     */
    OptLogResult getOptLogResultById(Long id);

}
