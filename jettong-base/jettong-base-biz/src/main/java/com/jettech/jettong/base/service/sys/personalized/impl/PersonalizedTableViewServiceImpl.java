package com.jettech.jettong.base.service.sys.personalized.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.jettech.basic.base.entity.SuperEntity;
import com.jettech.basic.base.service.SuperServiceImpl;
import com.jettech.basic.context.ContextUtil;
import com.jettech.basic.database.mybatis.conditions.Wraps;
import com.jettech.basic.database.mybatis.conditions.query.LbqWrapper;
import com.jettech.basic.exception.BizException;
import com.jettech.basic.utils.ArgumentAssert;
import com.jettech.basic.uuid.UidGeneratorUtil;
import com.jettech.jettong.base.dao.sys.personalized.PersonalizedTableViewDefaultMapper;
import com.jettech.jettong.base.dao.sys.personalized.PersonalizedTableViewMapper;
import com.jettech.jettong.base.dto.sys.personalized.PersonalizedTableViewSetDefaultDTO;
import com.jettech.jettong.base.entity.sys.personalized.PersonalizedTableView;
import com.jettech.jettong.base.entity.sys.personalized.PersonalizedTableViewDefault;
import com.jettech.jettong.base.service.sys.personalized.PersonalizedTableViewService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 用户列表视图信息业务层
 *
 * <AUTHOR>
 * @version 1.0
 * @description 用户列表视图信息业务层
 * @projectName jettong
 * @package com.jettech.jettong.base.service.sys.personalized.impl
 * @className PersonalizedTableViewServiceImpl
 * @date 2021-11-20
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Slf4j
@Service
@DS("#thread.tenant")
@RequiredArgsConstructor
public class PersonalizedTableViewServiceImpl
        extends SuperServiceImpl<PersonalizedTableViewMapper, PersonalizedTableView>
        implements PersonalizedTableViewService
{

    private final PersonalizedTableViewDefaultMapper personalizedTableViewDefaultMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void savePersonalizedTableView(PersonalizedTableView personalizedTableView)
    {
        // 校验是否重名
        ArgumentAssert.isFalse(checkNameRepeat(personalizedTableView.getName(),personalizedTableView.getId(),personalizedTableView.getUserId()),"视图名称{}已存在",personalizedTableView.getName());

        boolean isSave = super.save(personalizedTableView);

        if (isSave)
        {
            // 判断是否为默认过滤器
            if (personalizedTableView.getIsDefault())
            {
                // 设置列表默认过滤器信息
                setDefaultView(personalizedTableView.getId(), personalizedTableView.getTableCode());
            }
        }
    }

    /**
     * 保存最后一次使用筛选记录，并作为默认视图
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveLastUseFilter(PersonalizedTableView lastView)
    {
        if (lastView == null) {
            return;
        }

        // 删除用户最后的查询视图信息，保证最后一次使用的视图为最新的
        LbqWrapper<PersonalizedTableView> wrapper = Wraps.<PersonalizedTableView>lbQ()
                .eq(PersonalizedTableView::getTableCode, lastView.getTableCode())
                .eq(PersonalizedTableView::getName, lastView.getName())
                .eq(PersonalizedTableView::getUserId, lastView.getUserId());
        List<PersonalizedTableView> oldViewList = super.list(wrapper);
        if (CollUtil.isNotEmpty(oldViewList))
        {
            Set<Long> tableViewIds = oldViewList.stream().map(PersonalizedTableView::getId).collect(Collectors.toSet());
            personalizedTableViewDefaultMapper.delete(Wraps.<PersonalizedTableViewDefault>lbQ()
                    .in(PersonalizedTableViewDefault::getTableViewId, tableViewIds));
            super.removeByIds(tableViewIds);
        }

        // 判断是否存在，如果 tableId存在对应的视图信息，则使用该视图作为默认视图；没有则新建视图
        Long tableViewId = Optional.ofNullable(lastView.getTableId())
                .map(super::getById)
                .map(SuperEntity::getId)
                .orElseGet(() -> {
                    // 如果不存在保存试图信息
                    super.save(lastView);
                    return lastView.getId();
                });

        // 设置默认视图。可能为系统试图，userId必须使用请求的数据
        setDefaultView(tableViewId, lastView.getTableCode(), lastView.getUserId());
    }

    @Override
    public void setDefaultView(Long tableViewId, String tableCode, Long userId ){
        // 删除当前登录用户列表默认视图信息
        personalizedTableViewDefaultMapper.delete(Wraps.<PersonalizedTableViewDefault>lbQ()
                .eq(PersonalizedTableViewDefault::getTableCode, tableCode)
                .eq(PersonalizedTableViewDefault::getUserId, userId));

        personalizedTableViewDefaultMapper.insert(
                PersonalizedTableViewDefault.builder().tableViewId(tableViewId).tableCode(tableCode)
                        .userId(userId).build());

    }

    /**
     * 设置当前登录用户列表默认视图信息
     *
     * @param tableViewId 列表视图id
     * @param tableCode 列表唯一code
     * <AUTHOR>
     * @date 2022/8/22 18:47
     * @update 2022/8/22 18:47
     * @since 1.0
     */
    private void setDefaultView(Long tableViewId, String tableCode)
    {
        Long userId = ContextUtil.getUserId();
        if (null == userId)
        {
            throw BizException.validFail("未获取到当前登录用户信息");
        }
        this.setDefaultView(tableViewId, tableCode, userId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deletePersonalizedTableView(List<Long> tableViewIds)
    {
        List<PersonalizedTableView> personalizedTableViews = super.listByIds(tableViewIds);

        Long userId = ContextUtil.getUserId();
        if (null == userId)
        {
            throw BizException.validFail("未获取到当前登录用户信息");
        }
        List<PersonalizedTableView> deletePersonalizedTableViews =
                personalizedTableViews.stream().filter(item -> !userId.equals(item.getUserId()))
                        .collect(Collectors.toList());
        if (!deletePersonalizedTableViews.isEmpty())
        {
            throw BizException.validFail("删除失败，原因：只能删除您创建的视图");
        }
        personalizedTableViewDefaultMapper.delete(Wraps.<PersonalizedTableViewDefault>lbQ()
                .in(PersonalizedTableViewDefault::getTableViewId, tableViewIds));
        super.removeByIds(tableViewIds);
    }
    @Override
    public Boolean checkNameRepeat(String name,Long id,Long userId){
        return baseMapper.selectCount(Wraps.<PersonalizedTableView>lbQ().eq(PersonalizedTableView::getName, name).eq(PersonalizedTableView::getUserId, userId).ne(PersonalizedTableView::getId, id)) > 0;
    }
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updatePersonalizedTableView(PersonalizedTableView personalizedTableView)
    {
        // 判断能否修改
        Long tableViewId = personalizedTableView.getId();

        // 校验是否重名
        ArgumentAssert.isFalse(checkNameRepeat(personalizedTableView.getName(),personalizedTableView.getId(),personalizedTableView.getUserId()),"视图名称{}已存在",personalizedTableView.getName());

        PersonalizedTableView oldPersonalizedTableView = super.getById(tableViewId);

        // 判断是否是保护视图
        if (oldPersonalizedTableView.getIsProtect())
        {
            // 判断是否是自己创建的视图
            if (!oldPersonalizedTableView.getUserId().equals(ContextUtil.getUserId()))
            {
                throw BizException.validFail("该视图为保护视图，您没有权限修改");
            }
        }

        if (!oldPersonalizedTableView.getIsPublic())
        {
            // 判断是否是自己创建的视图
            if (!oldPersonalizedTableView.getUserId().equals(ContextUtil.getUserId()))
            {
                throw BizException.validFail("您没有权限修改该视图");
            }
        }

        // 判断是否设置了默认
        if (personalizedTableView.getIsDefault())
        {
            setDefaultView(personalizedTableView.getId(), oldPersonalizedTableView.getTableCode());
        } else {
            // 将 默认视图 改为 非默认视图时
            // 删除当前登录用户列表默认视图信息
            personalizedTableViewDefaultMapper.delete(Wraps.<PersonalizedTableViewDefault>lbQ()
                    .eq(PersonalizedTableViewDefault::getTableCode, personalizedTableView.getTableCode())
                    .eq(PersonalizedTableViewDefault::getTableViewId, personalizedTableView.getId())
                    .eq(PersonalizedTableViewDefault::getUserId, ContextUtil.getUserId()));
        }

        super.updateById(personalizedTableView);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void setDefault(PersonalizedTableViewSetDefaultDTO model)
    {
        String tableCode = model.getTableCode();

        if (null == tableCode)
        {
            throw BizException.validFail("设置默认视图失败，原因：缺少必填参数tableCode");
        }
        if (model.getIsBuiltIn())
        {
            String name = model.getName();

            if (null == name)
            {
                throw BizException.validFail("设置默认视图失败，原因：缺少必填参数name");
            }

            PersonalizedTableView personalizedTableView = super.getOne(
                    Wraps.<PersonalizedTableView>lbQ().eq(PersonalizedTableView::getName, name)
                            .eq(PersonalizedTableView::getTableCode, tableCode)
                            .eq(PersonalizedTableView::getIsBuiltIn, true), false);

            if (null == personalizedTableView)
            {
                Long viewId = UidGeneratorUtil.getId();
                super.save(PersonalizedTableView.builder().id(viewId).tableCode(tableCode).userId(1L).isBuiltIn(true)
                        .name(name).isProtect(true).isPublic(true).search(model.getSearch()).sort(model.getSort())
                        .build());
                model.setId(viewId);
            }
            else
            {
                model.setId(personalizedTableView.getId());
            }
        }

        Long viewId = model.getId();

        if (null == viewId)
        {
            throw BizException.validFail("设置默认视图失败，原因：缺少必填参数id");
        }

        setDefaultView(model.getId(), tableCode);
    }

    @Override
    public List<PersonalizedTableView> findByUserIdAndTableCode(Long userId, String tableCode)
    {

        List<String> tableCodes = CollUtil.newArrayList(tableCode, "*");
        LbqWrapper<PersonalizedTableView> lbqWrapper = Wraps.<PersonalizedTableView>lbQ()
                .in(PersonalizedTableView::getTableCode, tableCodes)
                .and(wrapper -> wrapper.eq(PersonalizedTableView::getUserId, userId)
                        .eq(PersonalizedTableView::getIsPublic, false)
                        .or().eq(PersonalizedTableView::getIsPublic, true))
                .orderByAsc(PersonalizedTableView::getSort)
                .orderByAsc(SuperEntity::getId);
        List<PersonalizedTableView> personalizedTableViews = super.list(lbqWrapper);

        // 查询列表的用户默认视图
        List<PersonalizedTableViewDefault> personalizedTableViewDefaults =
                personalizedTableViewDefaultMapper.selectList(
                        Wraps.<PersonalizedTableViewDefault>lbQ().eq(PersonalizedTableViewDefault::getUserId, userId)
                                .eq(PersonalizedTableViewDefault::getTableCode, tableCode));
        if (personalizedTableViewDefaults.isEmpty())
        {
            personalizedTableViews.forEach(item -> item.setIsDefault(false));
        }
        else
        {
            PersonalizedTableViewDefault personalizedTableViewDefault = personalizedTableViewDefaults.get(0);

            Long defaultId = personalizedTableViewDefault.getTableViewId();

            personalizedTableViews.forEach(item -> item.setIsDefault(item.getId().equals(defaultId)));
        }

        return personalizedTableViews;
    }
}
