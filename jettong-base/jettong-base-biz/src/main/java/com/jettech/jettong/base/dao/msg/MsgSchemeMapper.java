package com.jettech.jettong.base.dao.msg;

import com.jettech.basic.base.mapper.SuperMapper;
import com.jettech.jettong.base.entity.msg.MsgScheme;

import org.springframework.stereotype.Repository;

/**
 * 消息通知方案信息Mapper 接口
 * <AUTHOR>
 * @version 1.0
 * @description 消息通知方案信息Mapper 接口
 * @projectName jettong
 * @package com.jettech.jettong.base.dao.msg
 * @className MsgSchemeMapper
 * @date 2023-05-31
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Repository
public interface MsgSchemeMapper extends SuperMapper<MsgScheme>
{

}
