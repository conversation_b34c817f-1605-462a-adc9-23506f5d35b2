package com.jettech.jettong.base.service.file;

import com.jettech.jettong.base.properties.file.FileServerProperties;
import io.minio.MinioClient;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 本地上传配置
 *
 * <AUTHOR>
 * @version 1.0
 * @description 本地上传配置
 * @projectName jettong
 * @package com.jettech.jettong.base.service.file
 * @className FileAutoConfigure
 * @date 2021/9/15 9:43
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@EnableConfigurationProperties(FileServerProperties.class)
@Configuration
@RequiredArgsConstructor
@Slf4j
public class FileAutoConfigure
{
    /**
     * 初始化minio客户端,不用每次都初始化
     *
     * @return MinioClient
     * <AUTHOR>
     */
    @Bean
    public MinioClient minioClient(FileServerProperties properties)
    {
        return new MinioClient.Builder()
                .endpoint(properties.getMinIo().getEndpoint())
                .credentials(properties.getMinIo().getAccessKey(), properties.getMinIo().getSecretKey())
                .build();
    }

}
