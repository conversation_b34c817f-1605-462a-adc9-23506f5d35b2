package com.jettech.jettong.base.dao.msg;

import com.jettech.basic.base.mapper.SuperMapper;
import com.jettech.basic.database.mybatis.conditions.Wraps;
import com.jettech.jettong.base.entity.msg.engine.MsgEngineMaintainer;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 消息引擎维护人信息mapper
 *
 * <AUTHOR>
 * @version 1.0
 * @description 消息引擎维护人信息mapper
 * @projectName jettong-base-cloud
 * @package com.jettech.jettong.base.dao.msg
 * @className MsgEngineMaintainerMapper
 * @date 2023/5/16 14:23
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Repository
public interface MsgEngineMaintainerMapper extends SuperMapper<MsgEngineMaintainer> {

    /**
     * 根据消息引擎id 获取维护人信息
     */
    default List<MsgEngineMaintainer> selectByEngineId(Long engineId) {
        return this.selectList(Wraps.<MsgEngineMaintainer>lbQ().eq(MsgEngineMaintainer::getEngineId, engineId));
    }

    /**
     * 根据消息引擎id 删除维护人信息
     */
    default int deleteByEngineId(Long engineId) {
        return this.delete(Wraps.<MsgEngineMaintainer>lbQ().eq(MsgEngineMaintainer::getEngineId, engineId));
    }

}
