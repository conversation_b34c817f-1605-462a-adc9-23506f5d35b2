package com.jettech.jettong.base.service.sys.personalized.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.jettech.basic.base.service.SuperServiceImpl;
import com.jettech.basic.database.mybatis.conditions.Wraps;
import com.jettech.basic.database.mybatis.conditions.query.LbqWrapper;
import com.jettech.basic.exception.BizException;
import com.jettech.jettong.base.dao.sys.personalized.TableViewMapper;
import com.jettech.jettong.base.dao.sys.personalized.TableViewShareMapper;
import com.jettech.jettong.base.entity.sys.personalized.TableView;
import com.jettech.jettong.base.entity.sys.personalized.TableViewShare;
import com.jettech.jettong.base.service.sys.personalized.TableViewShareService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 筛选器收藏夹业务层
 *
 * <AUTHOR>
 * @version 1.0
 * @description 筛选器收藏夹业务层
 * @projectName jettong
 * @package com.jettech.jettong.base.base.service.sys.impl
 * @className TableViewCollectionServiceImpl
 * @date 2023-03-23
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Slf4j
@Service
@DS("#thread.tenant")
@RequiredArgsConstructor
public class TableViewShareServiceImpl extends SuperServiceImpl<TableViewShareMapper, TableViewShare>
        implements TableViewShareService {

    private final TableViewMapper viewMapper;

    @Override
    public void share(List<Long> viewIds, List<Long> userIds) {

        List<TableView> tableViews = viewMapper.selectBatchIds(viewIds);
        if (tableViews.size() != viewIds.size()) {
            throw BizException.wrap("找不到筛选器资源");
        }

        List<TableViewShare> shareList = new ArrayList<>();
        for (Long viewId : viewIds) {
            for (Long userId : userIds) {
                TableViewShare share = TableViewShare.builder().viewId(viewId).userId(userId).build();
                shareList.add(share);
            }
        }

        this.saveBatch(shareList);
    }

    @Override
    public void unshare(List<Long> viewIds, List<Long> userIds) {
        LbqWrapper<TableViewShare> wrapper = Wraps.lbQ();
        wrapper.in(TableViewShare::getUserId, userIds)
                .in(TableViewShare::getViewId, viewIds);
        baseMapper.delete(wrapper);
    }
}
