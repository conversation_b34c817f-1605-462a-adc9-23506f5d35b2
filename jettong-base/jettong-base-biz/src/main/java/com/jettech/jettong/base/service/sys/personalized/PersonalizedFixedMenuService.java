package com.jettech.jettong.base.service.sys.personalized;

import com.jettech.basic.base.service.SuperService;
import com.jettech.jettong.base.entity.sys.personalized.PersonalizedFixedMenu;

import java.util.List;

/**
 * 用户自定义固定菜单信息业务接口
 *
 * <AUTHOR>
 * @version 1.0
 * @description 用户自定义固定菜单信息业务接口
 * @projectName jettong
 * @package com.jettech.jettong.base.service.sys.personalized
 * @className PersonalizedFixedMenuService
 * @date 2021-11-29
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
public interface PersonalizedFixedMenuService extends SuperService<PersonalizedFixedMenu>
{
    /**
     * 批量修改用户自定义固定菜单信息
     *
     * @param personalizedFixedMenus 用户自定义固定菜单信息
     * @param userId 用户id
     * <AUTHOR>
     * @date 2021/11/30 9:45
     * @update zxy 2021/11/30 9:45
     * @since 1.0
     */
    void batchUpdate(List<PersonalizedFixedMenu> personalizedFixedMenus, Long userId);
}
