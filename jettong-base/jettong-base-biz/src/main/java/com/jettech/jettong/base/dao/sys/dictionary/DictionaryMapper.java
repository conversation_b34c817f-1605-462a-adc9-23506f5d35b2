package com.jettech.jettong.base.dao.sys.dictionary;

import com.jettech.basic.base.mapper.SuperMapper;
import com.jettech.jettong.base.entity.sys.dictionary.Dictionary;
import org.springframework.stereotype.Repository;

/**
 * 数据字典信息表Mapper 接口
 *
 * <AUTHOR>
 * @version 1.0
 * @description 数据字典信息表Mapper 接口
 * @projectName jettong
 * @package com.jettech.jettong.base.dao.sys.dictionary
 * @className DictionaryMapper
 * @date 2021-10-15
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Repository
public interface DictionaryMapper extends SuperMapper<Dictionary>
{

}
