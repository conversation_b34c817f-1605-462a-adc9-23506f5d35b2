package com.jettech.jettong.base.service.rbac.domain.org.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;

import java.util.List;

/**
 * 同步OA系统组织机构请求参数
 * <AUTHOR>
 * @version 1.0
 * @description 同步OA系统组织机构请求参数
 * @projectName HBSC
 * @package com.jettech.jettong.base.service.rbac.domain.org.model
 * @className SyncOaUserParam
 * @date 2024/4/22 10:01
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@lombok.Data
@Builder
public class SyncOaOrgParam
{
    /**
     * 同步OA系统组织机构请求header中Usercode
     */
    public static final String USER_CODE = "Usercode";

    /**
     * 同步OA系统组织机构请求header中Password
     */
    public static final String PASSWORD = "Password";

    /**
     * 数据交互对象
     */
    @JsonProperty("ESB")
    private Esb esb;


    /**
     * 同步OA系统组织机构请求参数,数据交互对象
     * <AUTHOR>
     * @version 1.0
     * @description 同步OA系统组织机构请求参数,数据交互对象
     * @projectName HBSC
     * @package com.jettech.jettong.base.service.rbac.domain.org.model
     * @className SyncOaUserParam.Esb
     * @date 2024/4/22 10:01
 * @copyright 2021 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
     */
    @lombok.Data
    @Builder
    public static class Esb
    {
        /**
         * 数据对象
         */
        @JsonProperty("DATA")
        private Data data;

        /**
         * 同步OA系统组织机构请求参数,数据对象
         * <AUTHOR>
         * @version 1.0
         * @description 同步OA系统组织机构请求参数,数据对象
         * @projectName HBSC
         * @package com.jettech.jettong.base.service.rbac.domain.org.model
         * @className SyncOaUserParam.Esb.Data
         * @date 2024/4/22 10:01
 * @copyright 2021 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
         */
        @lombok.Data
        @Builder
        public static class Data
        {
            /**
             * 主数据集合
             */
            @JsonProperty("DATAINFOS")
            private Datainfos datainfos;

            /**
             * 分页参数
             */
            @JsonProperty("SPLITPAGE")
            private SplitPage splitPage;

            /**
             * 同步OA系统组织机构请求参数,主数据集合
             * <AUTHOR>
             * @version 1.0
             * @description 同步OA系统组织机构请求参数,主数据集合
             * @projectName HBSC
             * @package com.jettech.jettong.base.service.rbac.domain.org.model
             * @className SyncOaUserParam.Esb.Data.Datainfos
             * @date 2024/4/22 10:01
 * @copyright 2021 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
             */
            @lombok.Data
            @Builder
            public static class Datainfos
            {

                /**
                 * 数组对象
                 */
                @JsonProperty("DATAINFO")
                private List<Datainfo> datainfo;

                /**
                 * 批数据的UUID
                 */
                @JsonProperty("PUUID")
                private String puuid;

                /**
                 * 同步OA系统组织机构请求参数,数组对象
                 * <AUTHOR>
                 * @version 1.0
                 * @description 同步OA系统组织机构请求参数,数组对象
                 * @projectName HBSC
                 * @package com.jettech.jettong.base.service.rbac.domain.org.model
                 * @className SyncOaUserParam.Esb.Data.Datainfos.Datainfo
                 * @date 2024/4/22 10:01
 * @copyright 2021 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
                 */
                @lombok.Data
                @Builder
                public static class Datainfo
                {
                    /**
                     * 最后一次变更时间
                     * 取时间段的格式：
                     * 1970-01-01 00:00:00~2020-12-12 23:59:59
                     */
                    @JsonProperty("LASTMODIFYRECORDTIME")
                    private String lastModifyRecordTime;

                }
            }

            /**
             * 同步OA系统组织机构请求参数,分页参数
             * <AUTHOR>
             * @version 1.0
             * @description 同步OA系统组织机构请求参数,分页参数
             * @projectName HBSC
             * @package com.jettech.jettong.base.service.rbac.domain.org.model
             * @className SyncOaUserParam.Esb.Data.SplitPage
             * @date 2024/4/22 10:01
 * @copyright 2021 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
             */
            @lombok.Data
            @Builder
            public static class SplitPage
            {

                /**
                 * 每页查询条数
                 * 默认200条
                 */
                @JsonProperty("COUNTPERPAGE")
                private Integer countPerPage;

                /**
                 * 当前页码
                 * 默认第1页
                 */
                @JsonProperty("CURRENTPAGE")
                private Integer currentPage;
            }
        }
    }

}
