package com.jettech.jettong.base.dao.msg;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.jettech.basic.base.mapper.SuperMapper;
import com.jettech.jettong.base.dto.msg.MsgQuery;
import com.jettech.jettong.base.entity.msg.Msg;
import com.jettech.jettong.base.vo.msg.MyMsgResult;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 消息持久化层接口
 *
 * <AUTHOR>
 * @version 1.0
 * @description 消息持久化层接口
 * @projectName jettong
 * @package com.jettech.jettong.base.dao.msg
 * @className MsgMapper
 * @date 2021/9/15 9:43
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Repository
public interface MsgMapper extends SuperMapper<Msg>
{

    /**
     * 清理没有接收人的站内信信息
     *
     * @return int 删除条数
     * <AUTHOR>
     * @date 2021/12/27 15:38
     * @update zxy 2021/12/27 15:38
     * @since 1.0
     */
    int cleanNoHaveReceive();

    /**
     * 分页查询我的消息
     *
     * @param page 分页对象
     * @param query 查询条件
     * @return IPage<MyMsgResult> 我的消息
     * <AUTHOR>
     * @date 2021/11/30 17:26
     * @update zxy 2021/11/30 17:26
     * @since 1.0
     */
    IPage<MyMsgResult> findMyMsgPage(IPage<MyMsgResult> page, @Param(Constants.COLUMN_MAP) MsgQuery query);

    /**
     * 查询所有我的消息
     *
     * @param query 查询条件
     * @return List<MyMsgResult> 我的消息
     * <AUTHOR>
     * @date 2021/11/30 17:03
     * @update zxy 2021/11/30 17:03
     * @since 1.0
     */
    List<MyMsgResult> findByQuery(@Param(Constants.COLUMN_MAP) MsgQuery query);

}
