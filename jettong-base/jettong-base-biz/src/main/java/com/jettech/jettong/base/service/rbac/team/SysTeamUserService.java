package com.jettech.jettong.base.service.rbac.team;

import com.jettech.basic.base.service.SuperService;
import com.jettech.jettong.base.entity.rbac.team.SysTeam;
import com.jettech.jettong.base.entity.rbac.team.SysTeamUser;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 团队成员表业务接口
 *
 * <AUTHOR>
 * @version 1.0
 * @description 团队成员表业务接口
 * @projectName jettong
 * @package com.jettech.jettong.base.base.service
 * @className SysTeamUserService
 * @date 2023-03-13
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
public interface SysTeamUserService extends SuperService<SysTeamUser>
{
    /**
     * 添加成员，需将成员添加到父团队中
     *
     * @param teamId 团队id
     * @param userIds 用户id集合
     * <AUTHOR>
     * @date 2023/4/18 10:47
     * @update 2023/4/18 10:47
     * @since 1.0
     */
    void addTeamUser(Long teamId, List<Long> userIds);

    /**
     * 删除成员子团队有的成员不能删除
     *
     * @param teamId 团队id
     * @param userIds 用户id集合
     * <AUTHOR>
     * @date 2023/4/18 10:54
     * @update 2023/4/18 10:54
     * @since 1.0
     */
    void removeTeamUser(Long teamId, List<Long> userIds);

    /**
     * 根据用户id获取团队
     *
     * @param userId
     * @return
     */
    List<SysTeam> getTeamByUserId(Long userId);

    /**
     * 根据用户id获取团队
     * @param userIds
     * @return
     */
    Map<Long, List<SysTeam>> getTeamByUserIds(Collection<Long> userIds);
}
