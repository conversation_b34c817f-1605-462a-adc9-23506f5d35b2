package com.jettech.jettong.base.dao.rbac.team;

import com.jettech.basic.base.mapper.SuperMapper;
import com.jettech.jettong.base.entity.rbac.team.SysTeam;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.io.Serializable;
import java.util.Collection;

/**
 * 团队信息表Mapper 接口
 * <AUTHOR>
 * @version 1.0
 * @description 团队信息表Mapper 接口
 * @projectName jettong
 * @package com.jettech.jettong.base.base.dao
 * @className SysTeamMapper
 * @date 2023-03-13
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Repository
public interface SysTeamMapper extends SuperMapper<SysTeam>
{
    /**
     * 根据团队id查询项目使用团队数量
     *
     * @param teamIds 团队id集合
     * @return {@link long}
     * @throws
     * <AUTHOR>
     * @date 2023/4/18 11:25
     * @update 2023/4/18 11:25
     * @since 1.0
     */
    long getProjectTeamCount(@Param("teamIds") Collection<? extends Serializable> teamIds);

}
