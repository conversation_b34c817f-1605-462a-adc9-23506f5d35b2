package com.jettech.jettong.base.service.rbac.team.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.jettech.basic.base.entity.SuperEntity;
import com.jettech.basic.base.service.SuperServiceImpl;
import com.jettech.basic.database.mybatis.conditions.Wraps;
import com.jettech.basic.exception.BizException;
import com.jettech.basic.model.LoadService;
import com.jettech.jettong.base.dao.rbac.team.SysTeamMapper;
import com.jettech.jettong.base.entity.rbac.team.SysTeam;
import com.jettech.jettong.base.entity.rbac.user.User;
import com.jettech.jettong.base.service.rbac.team.SysTeamService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 团队信息表业务层
 *
 * <AUTHOR>
 * @version 1.0
 * @description 团队信息表业务层
 * @projectName jettong
 * @package com.jettech.jettong.base.base.service.impl
 * @className SysTeamServiceImpl
 * @date 2023-03-13
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Slf4j
@Service
@RequiredArgsConstructor
@DS("#thread.tenant")
public class SysTeamServiceImpl extends SuperServiceImpl<SysTeamMapper, SysTeam>
        implements SysTeamService, LoadService
{

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeByIds(Collection<? extends Serializable> ids)
    {
        // 判断团队是否有下级团队
        long childSysTeamCount = super.count(Wraps.<SysTeam>lbQ().in(SysTeam::getParentId, ids));
        if (childSysTeamCount > 0)
        {
            throw new BizException("删除失败，原因：请先删除子团队信息");
        }

        // 判断团队是否被项目使用
        long projectSysTeamCount = baseMapper.getProjectTeamCount(ids);

        if (projectSysTeamCount > 0)
        {
            throw new BizException("删除失败，原因：团队已经被项目使用");
        }

        return super.removeByIds(ids);
    }

    @Override
    public Map<Serializable, Object> findByIds(Set<Serializable> ids) {
        List<SysTeam> sysTeams = this.listByIds(ids);
        return sysTeams.stream().collect(Collectors.toMap(SuperEntity::getId, Function.identity()));
    }

    @Override
    public Boolean checkNameUnique(Long id, String name){
        return count(Wraps.<SysTeam>lbQ().eq(SysTeam::getName, name).ne(SysTeam::getId, id)) > 0;
    }
}
