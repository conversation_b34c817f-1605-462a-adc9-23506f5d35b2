package com.jettech.jettong.base.service.rbac.role.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.jettech.basic.base.service.SuperServiceImpl;
import com.jettech.basic.cache.model.CacheKey;
import com.jettech.basic.cache.repository.CacheOps;
import com.jettech.basic.context.ContextUtil;
import com.jettech.basic.database.mybatis.conditions.Wraps;
import com.jettech.basic.database.mybatis.conditions.query.LbqWrapper;
import com.jettech.basic.utils.ArgumentAssert;
import com.jettech.jettong.base.dao.rbac.role.FunctionMapper;
import com.jettech.jettong.base.dao.rbac.role.RoleAuthorityMapper;
import com.jettech.jettong.base.dto.rbac.role.RoleAuthoritySaveDTO;
import com.jettech.jettong.base.dto.rbac.user.UserRoleSaveDTO;
import com.jettech.jettong.base.entity.rbac.role.RoleAuthority;
import com.jettech.jettong.base.entity.rbac.user.UserRole;
import com.jettech.jettong.base.enumeration.rbac.AuthorizeType;
import com.jettech.jettong.base.service.rbac.role.RoleAuthorityService;
import com.jettech.jettong.base.service.rbac.user.UserRoleService;
import com.jettech.jettong.common.cache.base.rbac.role.RoleMenuCacheKeyBuilder;
import com.jettech.jettong.common.cache.base.rbac.role.RoleResourceCacheKeyBuilder;
import com.jettech.jettong.common.cache.base.rbac.user.UserCacheKeyBuilder;
import com.jettech.jettong.common.cache.base.rbac.user.UserFunctionCacheKeyBuilder;
import com.jettech.jettong.common.cache.base.rbac.user.UserMenuCacheKeyBuilder;
import com.jettech.jettong.common.cache.base.rbac.user.UserRoleCacheKeyBuilder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 角色权限业务处理层
 *
 * <AUTHOR>
 * @version 1.0
 * @description 角色权限业务处理层
 * @projectName jettong
 * @package com.jettech.jettong.base.service.rbac.role.impl
 * @className RoleAuthorityServiceImpl
 * @date 2021/9/15 9:43
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Slf4j
@Service
@DS("#thread.tenant")
@RequiredArgsConstructor
public class RoleAuthorityServiceImpl extends SuperServiceImpl<RoleAuthorityMapper, RoleAuthority>
        implements RoleAuthorityService
{

    private final UserRoleService userRoleService;

    private final FunctionMapper functionMapper;

    private final CacheOps cacheOps;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveUserRole(UserRoleSaveDTO userRole)
    {
        // 保存用户关联角色
        List<UserRole> oldUserRoleList =
                userRoleService.list(Wraps.<UserRole>lbQ().eq(UserRole::getRoleId, userRole.getRoleId()));

        userRoleService.remove(Wraps.<UserRole>lbQ().eq(UserRole::getRoleId, userRole.getRoleId()));

        Set<Long> delUserIdList = new HashSet<>();
        if (CollUtil.isNotEmpty(userRole.getUserIdList()))
        {
            List<UserRole> list = userRole.getUserIdList()
                    .stream()
                    .map(userId -> UserRole.builder()
                            .userId(userId)
                            .roleId(userRole.getRoleId())
                            .build())
                    .collect(Collectors.toList());
            userRoleService.saveBatch(list, 100);
            delUserIdList.addAll(userRole.getUserIdList());
        }

        // 处理和用户相关的缓存
        if (!oldUserRoleList.isEmpty())
        {
            delUserIdList.addAll(oldUserRoleList.stream().map(UserRole::getUserId).collect(Collectors.toSet()));
        }

        delCache(delUserIdList);

        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveRoleAuthority(RoleAuthoritySaveDTO dto)
    {
        Long roleId = dto.getRoleId();
        ArgumentAssert.notNull(roleId, "请选择角色");

        // 将当前角色中拥有 而登录角色中没有的 权限记录 放入修改后的数据中
        List<RoleAuthority> moreAuthList = moreAuthList(roleId);

        //删除角色和资源的关联
        super.remove(Wraps.<RoleAuthority>lbQ().eq(RoleAuthority::getRoleId, dto.getRoleId()));
        List<RoleAuthority> list = new ArrayList<>();
        if (dto.getFunctionIdList() != null && !dto.getFunctionIdList().isEmpty())
        {
            List<Long> menuIdList = functionMapper.findMenuIdByFunctionId(dto.getFunctionIdList());
            if (dto.getMenuIdList() == null || dto.getMenuIdList().isEmpty())
            {
                dto.setMenuIdList(menuIdList);
            }
            else
            {
                dto.getMenuIdList().addAll(menuIdList);
            }

            //保存授予的资源resourceMapper
            List<RoleAuthority> resourceList = new HashSet<>(dto.getFunctionIdList())
                    .stream()
                    .map(resourceId -> RoleAuthority.builder()
                            .authorityType(AuthorizeType.FUNCTION)
                            .authorityId(resourceId)
                            .roleId(roleId)
                            .build())
                    .collect(Collectors.toList());
            list.addAll(resourceList);
        }
        if (dto.getMenuIdList() != null && !dto.getMenuIdList().isEmpty())
        {
            //保存授予的菜单
            List<RoleAuthority> menuList = new HashSet<>(dto.getMenuIdList())
                    .stream()
                    .map(menuId -> RoleAuthority.builder()
                            .authorityType(AuthorizeType.MENU)
                            .authorityId(menuId)
                            .roleId(roleId)
                            .build())
                    .collect(Collectors.toList());
            list.addAll(menuList);
        }

        // 将超出的权限重新保存
        list.addAll(moreAuthList);

        super.saveBatch(list, 100);

        // 角色下的所有用户
        List<Long> userIdList = userRoleService.listObjs(
                Wraps.<UserRole>lbQ().select(UserRole::getUserId).eq(UserRole::getRoleId, roleId),
                Convert::toLong);

        delCache(userIdList);
        cacheOps.del(new RoleResourceCacheKeyBuilder().key(roleId));
        cacheOps.del(new RoleMenuCacheKeyBuilder().key(roleId));
        return true;
    }

    /**
     * 获取当前角色中拥有 而登录角色中没有的 权限记录 放入修改后的数据中
     */
    private List<RoleAuthority> moreAuthList(Long roleId) {
        // 登录角色的权限
        Long userId = ContextUtil.getUserId();
        LbqWrapper<UserRole> userRoleLbqWrapper = Wraps.lbQ();
        userRoleLbqWrapper.eq(UserRole::getUserId, userId);
        List<UserRole> userRoleList = userRoleService.list(userRoleLbqWrapper);
        Set<Long> loginRoleIds = userRoleList.stream().map(UserRole::getRoleId).collect(Collectors.toSet());
        LbqWrapper<RoleAuthority> loginAuthWrapper = Wraps.lbQ();
        loginAuthWrapper.in(RoleAuthority::getRoleId, loginRoleIds);
        List<RoleAuthority> loginAuthList = this.list(loginAuthWrapper);


        // 当前角色的权限
        LbqWrapper<RoleAuthority> currentAuthWrapper = Wraps.lbQ();
        currentAuthWrapper.eq(RoleAuthority::getRoleId, roleId);
        List<RoleAuthority> currentAuthList = this.list(currentAuthWrapper);

        currentAuthList.removeIf(e -> loginAuthList.stream().anyMatch(loginAuth -> this.eqAuth(loginAuth, e)));
        return currentAuthList;
    }

    /**
     * 判断两个 RoleAuthority 在业务上是否相同
     */
    private boolean eqAuth(RoleAuthority auth1, RoleAuthority auth2) {
        return Objects.equals(auth1, auth2) || Objects.equals(auth1.getAuthorityId(), auth2.getAuthorityId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeByAuthorityId(List<Long> ids)
    {
        List<Long> roleIds = listObjs(
                Wraps.<RoleAuthority>lbQ().select(RoleAuthority::getRoleId).in(RoleAuthority::getAuthorityId, ids),
                Convert::toLong);

        if (!roleIds.isEmpty())
        {
            remove(Wraps.<RoleAuthority>lbQ().in(RoleAuthority::getAuthorityId, ids));

            List<Long> userIdList = userRoleService.listObjs(
                    Wraps.<UserRole>lbQ().select(UserRole::getUserId).in(UserRole::getRoleId, roleIds),
                    Convert::toLong);

            delCache(userIdList);
            cacheOps.del(roleIds.stream().map(new RoleResourceCacheKeyBuilder()::key).toArray(CacheKey[]::new));
            cacheOps.del(roleIds.stream().map(new RoleMenuCacheKeyBuilder()::key).toArray(CacheKey[]::new));
        }
        return true;
    }

    /**
     * 根据用户id删除缓存信息
     *
     * @param userIds 用户id
     * <AUTHOR>
     * @date 2022/6/6 11:27
     * @update 2022/6/6 11:27
     * @since 1.0
     */
    private void delCache(Collection<Long> userIds)
    {
        if (!userIds.isEmpty())
        {
            cacheOps.del(userIds.stream().map(new UserRoleCacheKeyBuilder()::key).toArray(CacheKey[]::new));
            cacheOps.del(userIds.stream().map(new UserMenuCacheKeyBuilder()::key).toArray(CacheKey[]::new));
            cacheOps.del(userIds.stream().map(new UserFunctionCacheKeyBuilder()::key).toArray(CacheKey[]::new));
            cacheOps.del(userIds.stream().map(new UserCacheKeyBuilder()::key).toArray(CacheKey[]::new));
        }
    }
}
