package com.jettech.jettong.base.service.file.impl.minio;

import com.jettech.basic.base.R;
import com.jettech.jettong.base.dao.file.FileMapper;
import com.jettech.jettong.base.dto.file.chunk.FileChunksMergeDTO;
import com.jettech.jettong.base.entity.file.File;
import com.jettech.jettong.base.properties.file.FileServerProperties;
import com.jettech.jettong.base.service.file.impl.AbstractFileChunkStrategy;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.util.List;

/**
 * minIO的putObject自身就支持断点续传， 所以先将分片文件上传到文件服务器并合并成大文件后， 在将大文件通过putObject直接上传到minIO
 *
 * <AUTHOR>
 * @version 1.0
 * @description minIO的putObject自身就支持断点续传， 所以先将分片文件上传到文件服务器并合并成大文件后， 在将大文件通过putObject直接上传到minIO
 * @projectName jettong
 * @package com.jettech.jettong.base.service.file.impl.minio
 * @className MinIoFileChunkStrategyImpl
 * @date 2021/9/15 9:43
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Slf4j
public class MinIoFileChunkStrategyImpl extends AbstractFileChunkStrategy
{
    public MinIoFileChunkStrategyImpl(FileMapper fileMapper, FileServerProperties fileProperties)
    {
        super(fileMapper, fileProperties);
    }


    @Override
    protected void copyFile(File file)
    {

    }


    @Override
    protected R<File> merge(List<java.io.File> files, String path, String fileName, FileChunksMergeDTO info)
            throws IOException
    {

        File filePo = File.builder()
                .build();
        return R.success(filePo);
    }
}
