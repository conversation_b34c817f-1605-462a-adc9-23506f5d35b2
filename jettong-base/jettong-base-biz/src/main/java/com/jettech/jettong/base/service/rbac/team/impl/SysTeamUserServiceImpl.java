package com.jettech.jettong.base.service.rbac.team.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.jettech.basic.base.entity.SuperEntity;
import com.jettech.basic.base.service.SuperServiceImpl;
import com.jettech.basic.database.mybatis.conditions.Wraps;
import com.jettech.basic.exception.BizException;
import com.jettech.jettong.base.dao.rbac.team.SysTeamMapper;
import com.jettech.jettong.base.dao.rbac.team.SysTeamUserMapper;
import com.jettech.jettong.base.entity.rbac.team.SysTeam;
import com.jettech.jettong.base.entity.rbac.team.SysTeamUser;
import com.jettech.jettong.base.service.rbac.team.SysTeamUserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 团队成员表业务层
 * <AUTHOR>
 * @version 1.0
 * @description 团队成员表业务层
 * @projectName jettong
 * @package com.jettech.jettong.base.base.service.impl
 * @className SysTeamUserServiceImpl
 * @date 2023-03-13
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Slf4j
@Service
@DS("#thread.tenant")
@RequiredArgsConstructor
public class SysTeamUserServiceImpl extends SuperServiceImpl<SysTeamUserMapper, SysTeamUser>
        implements SysTeamUserService
{
    private final SysTeamMapper sysTeamMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addTeamUser(Long teamId, List<Long> userIds)
    {
        // 添加成员到团队中
        List<SysTeamUser> addTeamUsers = getAddTeamUsers(teamId, userIds);

        if (!addTeamUsers.isEmpty())
        {
            super.saveBatch(addTeamUsers, 20);
        }

    }

    /**
     * 递归获取要添加的团队成员信息
     *
     * @param teamId 团队id
     * @param userIds 要添加的用户id
     * @return {@link List<SysTeamUser>} 团队成员信息
     * <AUTHOR>
     * @date 2023/4/9 14:10
     * @update 2023/4/9 14:10
     * @since 1.0
     */
    private List<SysTeamUser> getAddTeamUsers(Long teamId, List<Long> userIds)
    {
        // 判断用户id是否在团队中，如果不在团队中，将用户添加到团队中
        List<SysTeamUser> sysTeamUsers = super.list(
                Wraps.<SysTeamUser>lbQ().select(SysTeamUser::getUserId).eq(SysTeamUser::getTeamId, teamId)
                        .in(SysTeamUser::getUserId, userIds));

        List<Long> hasUserIds = sysTeamUsers.stream().map(SysTeamUser::getUserId).collect(Collectors.toList());

        userIds.removeAll(hasUserIds);

        if (userIds.isEmpty())
        {
            return Collections.emptyList();
        }

        List<SysTeamUser> addTeamUsers =
                userIds.stream().map(item -> SysTeamUser.builder().teamId(teamId).userId(item).build()).collect(
                        Collectors.toList());
        SysTeam sysTeam = sysTeamMapper.selectById(teamId);
        if (null != sysTeam.getParentId())
        {
            addTeamUsers.addAll(getAddTeamUsers(sysTeam.getParentId(), userIds));
        }
        return addTeamUsers;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void removeTeamUser(Long teamId, List<Long> userIds)
    {
        // 判断下级团队是否有该用户
        List<SysTeam> childSysTeams =
                sysTeamMapper.selectList(Wraps.<SysTeam>lbQ().select(SysTeam::getId).eq(SysTeam::getParentId, teamId));
        if (!childSysTeams.isEmpty())
        {
            List<Long> childSysTeamIds = childSysTeams.stream().map(SysTeam::getId).collect(Collectors.toList());

            List<SysTeamUser> childSysTeamUsers =
                    super.list(Wraps.<SysTeamUser>lbQ().in(SysTeamUser::getTeamId, childSysTeamIds));

            Set<Long> childSysTeamUserIds =
                    childSysTeamUsers.stream().map(SysTeamUser::getUserId).collect(Collectors.toSet());

            // 判断userIds是否都不在子团队的用户集合中
            List<Long> newUserIds = new ArrayList<>(userIds.size());
            newUserIds.addAll(userIds);

            newUserIds.removeAll(childSysTeamUserIds);

            if (newUserIds.size() != userIds.size())
            {
                throw new BizException("删除失败，原因：要删除的成员已经被子团队使用");
            }
        }

        if (!userIds.isEmpty())
        {
            // 判断团队成员是否被使用
            long projectTeamUserCount = baseMapper.getProjectTeamUserCount(teamId, userIds);

            if (projectTeamUserCount > 0)
            {
                throw new BizException("删除失败，原因：要删除的成员已经被项目使用");
            }

            super.remove(
                    Wraps.<SysTeamUser>lbQ().eq(SysTeamUser::getTeamId, teamId).in(SysTeamUser::getUserId, userIds));
        }
    }

    @Override
    public List<SysTeam> getTeamByUserId(Long userId) {
        List<SysTeamUser> teamUserList = this.list(Wraps.<SysTeamUser>lbQ().eq(SysTeamUser::getUserId, userId));

        Set<Long> teamIds = teamUserList.stream().map(SysTeamUser::getTeamId).collect(Collectors.toSet());
        if (teamIds.isEmpty()) {
            return Collections.emptyList();
        }
        return sysTeamMapper.selectBatchIds(teamIds);
    }

    @Override
    public Map<Long, List<SysTeam>> getTeamByUserIds(Collection<Long> userIds) {
        List<SysTeamUser> teamUserList = this.list(Wraps.<SysTeamUser>lbQ().in(SysTeamUser::getUserId, userIds));

        Set<Long> teamIds = teamUserList.stream().map(SysTeamUser::getTeamId).collect(Collectors.toSet());
        List<SysTeam> sysTeams = sysTeamMapper.selectBatchIds(teamIds);
        Map<Long, SysTeam> teamMap = sysTeams.stream().collect(Collectors.toMap(SuperEntity::getId, Function.identity()));

        // key:userId value:teamList，一个用户可能在多个团队中
        Map<Long, List<SysTeam>> userTeamMap = new HashMap<>(userIds.size());
        for (SysTeamUser teamUser : teamUserList) {
            Long userId = teamUser.getUserId();
            List<SysTeam> teamList = userTeamMap.computeIfAbsent(userId, k -> new ArrayList<>());
            teamList.add(teamMap.get(teamUser.getTeamId()));
        }

        return userTeamMap;
    }
}
