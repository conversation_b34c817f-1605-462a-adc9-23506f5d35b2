package com.jettech.jettong.base.dao.rbac.user;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.jettech.basic.base.mapper.SuperMapper;
import com.jettech.basic.database.mybatis.auth.DataScope;
import com.jettech.jettong.base.dto.rbac.user.UserExportQuery;
import com.jettech.jettong.base.entity.rbac.user.User;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 用户持久化层接口
 *
 * <AUTHOR>
 * @version 1.0
 * @description 用户持久化层接口
 * @projectName jettong
 * @package com.jettech.jettong.base.dao.rbac.user
 * @className UserMapper
 * @date 2021/9/15 9:43
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Repository
public interface UserMapper extends SuperMapper<User>
{

    /**
     * 根据角色id，查询已关联用户
     *
     * @param roleId 角色id
     * @param keyword 关键字
     * @return List<User> 用户信息
     * <AUTHOR>
     * @date 2022/5/16 14:18
     * @update zxy 2022/5/16 14:18
     * @since 1.0
     */
    List<User> findUserByRoleId(@Param("roleId") Long roleId, @Param("keyword") String keyword);

    /**
     * 递增 密码错误次数
     * @param id 用户id
     * @return int 被修改了几行数据
     * <AUTHOR>
     * @date 2022/5/16 14:17
     * @update zxy 2022/5/16 14:17
     * @since 1.0
     */
    int incrPasswordErrorNumById(@Param("id") Long id);

    /**
     * 带数据权限的分页查询
     * @param page 分页对象
     * @param wrapper 查询条件
     * @param dataScope 数据权限番位
     * @return IPage<User> 分页用户数据
     * <AUTHOR>
     * @date 2022/5/16 14:16
     * @update zxy 2022/5/16 14:16
     * @since 1.0
     */
    IPage<User> findPage(IPage<User> page, @Param(Constants.WRAPPER) Wrapper<User> wrapper, DataScope dataScope);

    /**
     * 重置 密码错误次数
     * @param id 用户id
     * @param now 当前时间
     * @return int 被修改了几行数据
     * <AUTHOR>
     * @date 2022/5/16 14:15
     * @update zxy 2022/5/16 14:15
     * @since 1.0
     */
    int resetPassErrorNum(@Param("id") Long id, @Param("now") LocalDateTime now);

    /**
     * 根据条件查询用户信息，包含角色和机构信息
     * 主要导出时使用该方法
     *
     * @param model 查询条件
     * @return List<Map < String, Object>> 用户信息，包含角色和机构信息
     * <AUTHOR>
     * @date 2021/12/1 10:08
     * @update zxy 2021/12/1 10:08
     * @since 1.0
     */
    List<Map<String, Object>> findUserByUserExportQuery(UserExportQuery model);

}
