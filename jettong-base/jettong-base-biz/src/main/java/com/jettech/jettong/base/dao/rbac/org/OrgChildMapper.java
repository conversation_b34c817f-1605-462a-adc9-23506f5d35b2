package com.jettech.jettong.base.dao.rbac.org;

import com.jettech.basic.base.mapper.SuperMapper;
import com.jettech.jettong.base.entity.rbac.org.OrgChild;
import org.springframework.stereotype.Repository;

/**
 * 组织机构子机构持久化层接口
 *
 * <AUTHOR>
 * @version 1.0
 * @description 组织机构子机构持久化层接口
 * @projectName jettong
 * @package com.jettech.jettong.base.dao.rbac.org
 * @className OrgChildMapper
 * @date 2021/9/15 9:43
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Repository
public interface OrgChildMapper extends SuperMapper<OrgChild>
{
}
