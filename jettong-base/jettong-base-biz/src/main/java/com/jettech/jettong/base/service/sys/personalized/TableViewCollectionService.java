package com.jettech.jettong.base.service.sys.personalized;

import com.jettech.basic.base.service.SuperService;
import com.jettech.jettong.base.entity.sys.personalized.TableView;
import com.jettech.jettong.base.entity.sys.personalized.TableViewCollection;

import java.util.List;

/**
 * 筛选器收藏夹业务接口
 * <AUTHOR>
 * @version 1.0
 * @description 筛选器收藏夹业务接口
 * @projectName jettong
 * @package com.jettech.jettong.base.base.service.sys
 * @className TableViewCollectionService
 * @date 2023-03-23
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
public interface TableViewCollectionService extends SuperService<TableViewCollection>
{

    /**
     * 当前用户收藏筛选器
     *
     * @param viewId    筛选器ID
     */
    void collect(Long viewId);

    /**
     * 当前用户取消收藏筛选器
     *
     * @param viewId    筛选器ID
     */
    void uncollect(Long viewId);

    /**
     * 获取收藏的筛选器列表ID
     */
    List<Long> getCollectViewIds(Long userId);

    /**
     * 获取收藏的筛选器列表
     */
    List<TableView> getCollectViewList(Long userId);

}
