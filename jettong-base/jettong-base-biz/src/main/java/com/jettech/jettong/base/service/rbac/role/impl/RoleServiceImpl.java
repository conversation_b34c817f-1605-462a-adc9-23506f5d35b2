package com.jettech.jettong.base.service.rbac.role.impl;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.RandomUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.jettech.basic.base.service.SuperCacheServiceImpl;
import com.jettech.basic.cache.model.CacheKey;
import com.jettech.basic.cache.model.CacheKeyBuilder;
import com.jettech.basic.database.mybatis.conditions.Wraps;
import com.jettech.basic.security.constant.RoleConstant;
import com.jettech.basic.utils.ArgumentAssert;
import com.jettech.basic.utils.BeanPlusUtil;
import com.jettech.basic.utils.CollHelper;
import com.jettech.basic.utils.StrHelper;
import com.jettech.jettong.base.dao.rbac.role.RoleMapper;
import com.jettech.jettong.base.dto.rbac.role.RoleSaveDTO;
import com.jettech.jettong.base.dto.rbac.role.RoleUpdateDTO;
import com.jettech.jettong.base.entity.rbac.role.Role;
import com.jettech.jettong.base.entity.rbac.role.RoleAuthority;
import com.jettech.jettong.base.entity.rbac.user.UserRole;
import com.jettech.jettong.base.service.rbac.role.RoleAuthorityService;
import com.jettech.jettong.base.service.rbac.role.RoleService;
import com.jettech.jettong.base.service.rbac.user.UserRoleService;
import com.jettech.jettong.common.cache.base.rbac.role.RoleCacheKeyBuilder;
import com.jettech.jettong.common.cache.base.rbac.role.RoleMenuCacheKeyBuilder;
import com.jettech.jettong.common.cache.base.rbac.role.RoleResourceCacheKeyBuilder;
import com.jettech.jettong.common.cache.base.rbac.user.UserCacheKeyBuilder;
import com.jettech.jettong.common.cache.base.rbac.user.UserFunctionCacheKeyBuilder;
import com.jettech.jettong.common.cache.base.rbac.user.UserMenuCacheKeyBuilder;
import com.jettech.jettong.common.cache.base.rbac.user.UserRoleCacheKeyBuilder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 角色业务处理层
 *
 * <AUTHOR>
 * @version 1.0
 * @description 角色业务处理层
 * @projectName jettong
 * @package com.jettech.jettong.base.service.rbac.role.impl
 * @className RoleServiceImpl
 * @date 2021/9/15 9:43
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Slf4j
@Service
@DS("#thread.tenant")
@RequiredArgsConstructor
public class RoleServiceImpl extends SuperCacheServiceImpl<RoleMapper, Role> implements RoleService
{
    private final RoleAuthorityService roleAuthorityService;
    private final UserRoleService userRoleService;

    @Override
    protected CacheKeyBuilder cacheKeyBuilder()
    {
        return new RoleCacheKeyBuilder();
    }

    @Override
    public boolean isPtAdmin(String code)
    {
        return RoleConstant.SUPER_ADMIN.equals(code);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveRole(RoleSaveDTO data, Long userId)
    {
        ArgumentAssert.isFalse(check(data.getCode()), "编号{}已经存在", data.getCode());
        Role role = BeanPlusUtil.toBean(data, Role.class);
        role.setCode(StrHelper.getOrDef(data.getCode(), RandomUtil.randomString(8)));
        role.setReadonly(false);
        save(role);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeByIdWithCache(List<Long> ids)
    {
        if (ids.isEmpty())
        {
            return true;
        }
        // 角色权限
        roleAuthorityService.remove(Wraps.<RoleAuthority>lbQ().in(RoleAuthority::getRoleId, ids));

        // 角色绑定了那些用户
        List<Long> userIds = userRoleService.listObjs(
                Wraps.<UserRole>lbQ().select(UserRole::getUserId).in(UserRole::getRoleId, ids),
                Convert::toLong);

        //角色拥有的用户
        userRoleService.remove(Wraps.<UserRole>lbQ().in(UserRole::getRoleId, ids));

        cacheOps.del(ids.stream().map(new RoleMenuCacheKeyBuilder()::key).toArray(CacheKey[]::new));
        cacheOps.del(ids.stream().map(new RoleResourceCacheKeyBuilder()::key).toArray(CacheKey[]::new));

        if (!userIds.isEmpty())
        {
            delCache(userIds);
        }
        // 删除角色
        return removeByIds(ids);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateRole(RoleUpdateDTO data, Long userId)
    {
        Role role = BeanPlusUtil.toBean(data, Role.class);
        updateById(role);
    }

    /**
     * 根据用户id删除缓存信息
     *
     * @param userIds 用户id
     * <AUTHOR>
     * @date 2022/6/6 11:27
     * @update 2022/6/6 11:27
     * @since 1.0
     */
    private void delCache(List<Long> userIds)
    {
        if (!userIds.isEmpty())
        {
            cacheOps.del(userIds.stream().map(new UserRoleCacheKeyBuilder()::key).toArray(CacheKey[]::new));
            cacheOps.del(userIds.stream().map(new UserMenuCacheKeyBuilder()::key).toArray(CacheKey[]::new));
            cacheOps.del(userIds.stream().map(new UserFunctionCacheKeyBuilder()::key).toArray(CacheKey[]::new));
            cacheOps.del(userIds.stream().map(new UserCacheKeyBuilder()::key).toArray(CacheKey[]::new));
        }
    }

    @Override
    public List<Role> findRoleByUserId(Long userId)
    {
        CacheKey cacheKey = new UserRoleCacheKeyBuilder().key(userId);
        return cacheOps.get(cacheKey, k ->
        {
            List<Role> roles = baseMapper.findRoleByUserId(userId);
            cacheOps.set(cacheKey, roles, false);
            return roles;
        });
    }


    @Override
    public List<Long> findUserIdByCode(String[] codes)
    {
        return baseMapper.findUserIdByCode(codes);
    }

    @Override
    public Boolean check(String code)
    {
        return super.count(Wraps.<Role>lbQ().eq(Role::getCode, code)) > 0;
    }

    private List<Role> findRole(Set<Serializable> ids)
    {
        return findByIds(ids,
                missIds -> super.listByIds(
                        missIds.stream().filter(Objects::nonNull).map(Convert::toLong).collect(Collectors.toList()))
        );
    }

    @Override
    public Map<Serializable, Object> findByIds(Set<Serializable> ids)
    {
        return CollHelper.uniqueIndex(findRole(ids), Role::getId, role -> role);
    }
}
