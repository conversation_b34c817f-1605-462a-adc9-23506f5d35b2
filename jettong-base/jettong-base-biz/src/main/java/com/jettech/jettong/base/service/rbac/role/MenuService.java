package com.jettech.jettong.base.service.rbac.role;

import com.jettech.basic.base.service.SuperCacheService;
import com.jettech.jettong.base.dto.rbac.role.MenuFunctionTreeVO;
import com.jettech.jettong.base.entity.rbac.role.Menu;
import com.jettech.jettong.base.vo.rbac.role.OtherMenusVo;

import java.util.List;

/**
 * 菜单业务处理层接口
 *
 * <AUTHOR>
 * @version 1.0
 * @description 菜单业务处理层接口
 * @projectName jettong
 * @package com.jettech.jettong.base.service.rbac.role
 * @className MenuService
 * @date 2021/9/15 9:43
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
public interface MenuService extends SuperCacheService<Menu>
{

    /**
     * 检查菜单编码是否可用
     *
     * @param id 功能id
     * @param code 菜单编码
     * @return boolean 是否可用
     * <AUTHOR>
     * @date 2021/10/23 16:16
     * @update zxy 2021/10/23 16:16
     * @since 1.0
     */
    boolean check(Long id, String code);

    /**
     * 根据主键id删除菜单信息
     *
     * @param ids 主键id集合
     * @return boolean 是否成功
     * <AUTHOR>
     * @date 2021/10/23 16:20
     * @update zxy 2021/10/23 16:20
     * @since 1.0
     */
    boolean removeByIdWithCache(List<Long> ids);

    /**
     * 修改菜单信息
     *
     * @param menu 菜单信息
     * @return boolean 是否成功
     * <AUTHOR>
     * @date 2021/10/23 16:19
     * @update zxy 2021/10/23 16:19
     * @since 1.0
     */
    boolean updateWithCache(Menu menu);

    /**
     * 新增菜单信息
     *
     * @param menu 菜单信息
     * @return boolean 是否成功
     * <AUTHOR>
     * @date 2021/10/23 16:19
     * @update zxy 2021/10/23 16:19
     * @since 1.0
     */
    boolean saveWithCache(Menu menu);

    /**
     * 查询用户可用菜单
     *
     * @param userId 用户id
     * @return List<Function> 可用菜单信息
     * <AUTHOR>
     * @date 2021/10/23 16:21
     * @update zxy 2021/10/23 16:21
     * @since 1.0
     */
    List<Menu> findVisibleMenu(Long userId);

    /**
     * 查询系统所有的菜单和资源树
     *
     * @return List<MenuFunctionTreeVO> 所有的菜单和资源树
     * <AUTHOR>
     * @date 2021/10/23 16:31
     * @update zxy 2021/10/23 16:31
     * @since 1.0
     */
    List<MenuFunctionTreeVO> findMenuFunctionTree(Menu menu);

    /**
     * 查询当前登录用户的菜单资源树
     *
     * @return  List<MenuFunctionTreeVO> 当前登录用户的菜单资源树
     */
    List<MenuFunctionTreeVO> currentMenuFunctionTree(Menu menu);

    /**
     * 外部应用新增或修改菜单及功能权限
     * @param menus 外部菜单信息
     * @return 处理结果
     */
    List<Menu> addOrUpdateMenuAndFunction(List<OtherMenusVo> menus,String platform);

}
