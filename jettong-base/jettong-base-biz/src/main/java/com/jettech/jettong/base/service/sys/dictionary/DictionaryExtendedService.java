package com.jettech.jettong.base.service.sys.dictionary;

import com.jettech.basic.base.service.SuperService;
import com.jettech.jettong.base.entity.sys.dictionary.DictionaryExtended;

import java.util.List;

/**
 * 数据字典扩展信息业务接口
 *
 * <AUTHOR>
 * @version 1.0
 * @description 数据字典扩展信息业务接口
 * @projectName jettong
 * @package com.jettech.jettong.base.service.sys.dictionary
 * @className DictionaryExtendedService
 * @date 2021-10-15
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
public interface DictionaryExtendedService extends SuperService<DictionaryExtended>
{
    /**
     * 根据字典id查询字典扩展信息
     *
     * @param dictId 字典id
     * @return List<DictionaryExtended> 字典扩展信息
     * <AUTHOR>
     * @date 2021/10/15 14:48
     * @update zxy 2021/10/15 14:48
     * @since 1.0
     */
    List<DictionaryExtended> findByDictId(Long dictId);

}
