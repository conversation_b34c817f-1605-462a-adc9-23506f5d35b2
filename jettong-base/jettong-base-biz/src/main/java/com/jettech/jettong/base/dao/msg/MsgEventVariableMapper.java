package com.jettech.jettong.base.dao.msg;

import com.jettech.basic.base.mapper.SuperMapper;
import com.jettech.basic.database.mybatis.conditions.Wraps;
import com.jettech.basic.database.mybatis.conditions.query.LbqWrapper;
import com.jettech.jettong.base.entity.msg.MsgEventVariable;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 消息通知事件模板支持变量信息Mapper 接口
 * <AUTHOR>
 * @version 1.0
 * @description 消息通知事件模板支持变量信息Mapper 接口
 * @projectName jettong
 * @package com.jettech.jettong.base.base.dao.msg
 * @className MsgEventVariableMapper
 * @date 2023-05-22
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Repository
public interface MsgEventVariableMapper extends SuperMapper<MsgEventVariable>
{

    /**
     * 根据事件ID查询事件变量
     * @param eventId 事件ID
     * @return 事件变量列表
     */
    public default List<MsgEventVariable> queryVariableByEventId(Long eventId) {
        LbqWrapper<MsgEventVariable> queryWrapper = Wraps.lbQ();
        queryWrapper.eq(MsgEventVariable::getEventId, eventId);
        return this.selectList(queryWrapper);
    }


}
