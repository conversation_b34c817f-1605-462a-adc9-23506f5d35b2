package com.jettech.jettong.base.service.rbac.org.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.tree.TreeNode;
import cn.hutool.crypto.symmetric.AES;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.jettech.basic.base.service.SuperCacheServiceImpl;
import com.jettech.basic.cache.model.CacheKeyBuilder;
import com.jettech.basic.database.mybatis.conditions.Wraps;
import com.jettech.basic.database.mybatis.conditions.query.LbqWrapper;
import com.jettech.basic.exception.BizException;
import com.jettech.basic.utils.*;
import com.jettech.basic.uuid.UidGeneratorUtil;
import com.jettech.jettong.base.dao.rbac.org.OrgMapper;
import com.jettech.jettong.base.dao.rbac.user.UserMapper;
import com.jettech.jettong.base.entity.rbac.org.Org;
import com.jettech.jettong.base.entity.rbac.org.OrgChild;
import com.jettech.jettong.base.entity.rbac.team.SysTeam;
import com.jettech.jettong.base.entity.rbac.user.User;
import com.jettech.jettong.base.service.rbac.domain.org.model.SyncOaOrgParam;
import com.jettech.jettong.base.service.rbac.domain.org.model.SyncOaOrgResult;
import com.jettech.jettong.base.service.rbac.domain.org.properties.JettongOaOrgProperties;
import com.jettech.jettong.base.service.rbac.org.OrgChildService;
import com.jettech.jettong.base.service.rbac.org.OrgService;
import com.jettech.jettong.base.service.rbac.team.SysTeamService;
import com.jettech.jettong.base.vo.rbac.org.OrgUserVO;
import com.jettech.jettong.common.cache.base.rbac.org.OrgCacheKeyBuilder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 组织机构业务处理层
 *
 * <AUTHOR>
 * @version 1.0
 * @description 组织机构业务处理层
 * @projectName jettong
 * @package com.jettech.jettong.base.service.rbac.org.impl
 * @className OrgServiceImpl
 * @date 2021/9/15 9:43
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Slf4j
@Service
@DS("#thread.tenant")
@RequiredArgsConstructor
public class OrgServiceImpl extends SuperCacheServiceImpl<OrgMapper, Org> implements OrgService
{
    /**
     * 同步OA系统机构状态
     * true:表示正在同步
     * false:表示没有在同步
     */
    public static boolean syncOAOrgState = false;
    private final JettongOaOrgProperties jettongOaOrgProperties;
    private final UserMapper userMapper;
    private final SysTeamService sysTeamService;
    private final OrgChildService orgChildService;

    @Override
    protected CacheKeyBuilder cacheKeyBuilder()
    {
        return new OrgCacheKeyBuilder();
    }

    @Override
    public List<OrgUserVO> getOrgUserVO()
    {

        List<Org> orgList = this.list();
        List<User> userList = userMapper.selectList(Wraps.lbQ());

        List<OrgUserVO> voList = new ArrayList<>();

        orgList.forEach(org ->
        {
            OrgUserVO vo = new OrgUserVO();
            BeanUtil.copyProperties(org, vo);
            vo.setType("ORG");
            voList.add(vo);
        });
        for (int i = 0; i < userList.size(); i++)
        {
            voList.add(
                    OrgUserVO.builder().id(UidGeneratorUtil.getId()).userId(userList.get(i).getId())
                            .parentId(userList.get(i).getOrgId())
                            .name(userList.get(i).getName())
                            .avatarPath(userList.get(i).getAvatarPath())
                            .type("USER")
                            .sort(i).build());
        }

        return TreeUtil.buildTree(voList);
    }

    @Override
    public List<Org> tree(String name, Integer state, String leadingBy)
    {
        if (null != state)
        {
            return super.list(
                    Wraps.<Org>lbQ().select(Org.class, i -> !"logo".equals(i.getProperty())).like(Org::getName, name)
                            .eq(Org::getState, state).eq(Org::getLeadingBy, leadingBy));
        }
        else
        {
            return super.list(
                    Wraps.<Org>lbQ().select(Org.class, i -> !"logo".equals(i.getProperty())).like(Org::getName, name)
                            .ne(Org::getState, 3).eq(Org::getLeadingBy, leadingBy));
        }
    }

    @Override
    public List<Org> sessionUserTree(Long orgId, String name, Integer state)
    {
        LbqWrapper<Org> lbqWrapper = Wraps.<Org>lbQ().select().like(Org::getName, name);

        if (null == state)
        {
            lbqWrapper.ne(Org::getState, 3);
        }
        else
        {
            lbqWrapper.eq(Org::getState, state);
        }

        if (null != orgId)
        {
            lbqWrapper.apply("(org_id in (select child_id from sys_org_child where parent_id = {0}) or org_id = {1})", orgId, orgId);
        }

        return super.list(lbqWrapper);
    }

    @Override
    public List<SysTeam> getTeamByTreeId(List<Org> tree)
    {
        List<SysTeam> list = sysTeamService.list(Wraps.<SysTeam>lbQ().isNotNull(SysTeam::getOrgId));
        list.forEach(item -> item.setParentId(item.getOrgId()));
        return list;

    }

    @Override
    public boolean check(Long id, String name)
    {
        return super.count(Wraps.<Org>lbQ().ne(Org::getId, id).eq(Org::getName, name)) > 0;
    }

    @Override
    public List<Org> findAllParent(Long orgId)
    {
        if (orgId == null)
        {
            return new ArrayList<>();
        }
        return baseMapper.findAllParent(orgId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean save(Org org)
    {
        ArgumentAssert.isFalse(check(org.getCode()), "编号{}已经存在", org.getCode());
        if (check(null, org.getName()))
        {
            throw BizException.validFail("新增失败,原因:名称[%s]已经存在", org.getName());
        }
        // 获取当前父机构下最大排序
        Org parentOrg =
                super.getOne(Wraps.<Org>lbQ().eq(Org::getParentId, org.getParentId()).orderByDesc(Org::getSort), false);
        if (parentOrg == null || null == parentOrg.getSort())
        {
            org.setSort(0);
        }
        else
        {
            org.setSort(parentOrg.getSort() + 1);
        }
        return super.save(org);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateById(Org org)
    {
        if (check(org.getId(), org.getName()))
        {
            throw BizException.validFail("修改失败,原因:名称[%s]已经存在", org.getName());
        }
        Org oldOrg = super.getByIdCache(org.getId());
        // 如果父机构有变化，重新计算机构子机构
        if (!oldOrg.getParentId().equals(org.getParentId()))
        {
            // 查询所有机构信息，只查询id和parent_id字段
            List<Org> allOrgs = super.list(Wraps.<Org>lbQ().select(Org::getId, Org::getParentId));

            // 按parentId分组
            Map<Long, Set<Long>> groupParentIdMap =
                    allOrgs.stream().filter(item -> null != item.getParentId()).collect(
                            Collectors.groupingBy(Org::getParentId,
                                    Collectors.mapping(Org::getId, Collectors.toSet())));

            // 将allOrgs转换为Map
            Map<Long, Long> orgIdParentIdMap = allOrgs.stream().filter(item -> null != item.getParentId()).collect(Collectors.toMap(Org::getId, Org::getParentId));

            // 有变更的机构，获取所有的上级机构，重新计算这些机构的下级机构
            Set<Long> allParentId = recursiveQueryParentId(org.getId(), orgIdParentIdMap);

            Set<OrgChild> orgChildSet = new HashSet<>();

            for (Long orgId : allParentId)
            {
                Set<Long> childOrgIdSet =
                        recursiveQueryChildOrgId(orgId, groupParentIdMap);

                childOrgIdSet.forEach(
                        item -> orgChildSet.add(
                                OrgChild.builder().parentId(orgId).childId(item).build()));

                // 删除所有机构子机构信息
                orgChildService.remove(Wraps.<OrgChild>lbQ().eq(OrgChild::getParentId, orgId));
            }

            if (!orgChildSet.isEmpty())
            {
                orgChildService.saveBatch(orgChildSet, 200);
            }

        }
        oldOrg.setName(org.getName());
        oldOrg.setState(org.getState());
        oldOrg.setParentId(org.getParentId());
        oldOrg.setParentCode(org.getParentCode());
        oldOrg.setSort(org.getSort());
        oldOrg.setDescription(org.getDescription());
        oldOrg.setLogo(org.getLogo());
        oldOrg.setLeadingBy(org.getLeadingBy());

        return this.updateAllById(oldOrg);
    }

    @Override
    public void updateSort(Long parentId, String parentCode, Long orgId, Long preOrgId)
    {
        // 查询上级机构
        Org parentOrg = super.getByIdCache(parentId);
        if (parentOrg == null)
        {
            throw BizException.validFail("修改失败,原因:上级机构不存在");
        }
        if (preOrgId != null)
        {
            // 查询相邻机构
            Org preOrg = super.getByIdCache(preOrgId);
            if (preOrg == null)
            {
                throw BizException.validFail("修改失败,原因:相邻机构不存在");
            }
        }
        // 查询要修改机构
        Org org = super.getByIdCache(orgId);
        if (org == null)
        {
            throw BizException.validFail("修改失败,原因:要修改的机构不存在");
        }
        Long oldParentId = org.getParentId();

        // 给机构赋予新的父级id
        org.setParentId(parentId);
        org.setParentCode(parentCode);
        // 对当前父机构下的所有机构进行重新排序
        // 查询父机构下的所有机构
        List<Org> childOrg = super.list(Wraps.<Org>lbQ().eq(Org::getParentId, parentId).orderByAsc(Org::getSort));

        // 先移除要修改的机构
        Iterator<Org> iterator = childOrg.iterator();
        while (iterator.hasNext())
        {
            if (iterator.next().getId().equals(orgId))
            {
                iterator.remove();
                break;
            }
        }
        // 判断要修改的机构插入位置
        if (preOrgId == null)
        {
            childOrg.add(0, org);
        }
        else
        {
            for (int i = 0; i < childOrg.size(); i++)
            {
                if (childOrg.get(i).getId().equals(preOrgId))
                {
                    childOrg.add(i + 1, org);
                    break;
                }
            }
        }

        for (int i = 0; i < childOrg.size(); i++)
        {
            childOrg.get(i).setSort(i);
        }

        super.updateBatchById(childOrg);

        // 删除缓存
        super.delCache(childOrg.stream().map(Org::getId).collect(Collectors.toList()));
        super.delCache(oldParentId);
    }

    @Override
    public List<Org> findChildren(Long id)
    {
        return baseMapper.findChildren(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean remove(List<Long> ids)
    {
        if (ids.isEmpty())
        {
            return true;
        }
        // 获取要删除机构下的所有机构
        List<Org> orgs = new ArrayList<>();
        for (Long id : ids)
        {
            orgs.addAll(this.findChildren(id));
        }
        List<Org> collect = orgs.stream().filter(org -> "oem".equals(org.getType())).collect(Collectors.toList());
        if (collect.size() > 0)
        {
            throw BizException.validFail("删除失败,原因:要删除的机构下有内置机构");
        }
        List<Long> idList = orgs.stream().mapToLong(Org::getId).boxed().collect(Collectors.toList());

        boolean checkUser = userMapper.selectCount(Wraps.<User>lbQ().in(User::getOrgId, idList)) > 0;

        if (checkUser)
        {
            throw BizException.validFail("删除失败,原因:要删除的机构下还有用户");
        }
        List<SysTeam> list = sysTeamService.list(Wraps.<SysTeam>lbQ().in(SysTeam::getOrgId, ids));
        if (list.size() != 0)
        {
            throw BizException.validFail("删除失败,原因:要删除的机构下有团队");
        }
        int a = baseMapper.getProductOrgByOrgId(ids);
        if (a != 0)
        {
            throw BizException.validFail("删除失败,原因:要删除的机构下有产品");
        }

        // 删除机构子机构信息
        orgChildService.remove(Wraps.<OrgChild>lbQ().in(OrgChild::getParentId, idList).or().in(OrgChild::getChildId, idList));

        return super.removeByIds(idList);
    }

    private List<Org> findOrg(Set<Serializable> ids)
    {
        return findByIds(ids,
                missIds -> super.listByIds(
                        missIds.stream().filter(Objects::nonNull).map(Convert::toLong).collect(Collectors.toList()))
        );
    }

    @Override
    public Map<Serializable, Object> findByIds(Set<Serializable> ids)
    {
        return CollHelper.uniqueIndex(findOrg(ids), Org::getId, org -> org);
    }

    @Override
    public Boolean check(String code)
    {
        return super.count(Wraps.<Org>lbQ().eq(Org::getCode, code)) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean syncOaOrg()
    {
        // 获取集团数据最后创建时间
        LocalDateTime lastDateTime =
                super.getObj(Wraps.<Org>lbQ().select(Org::getCreateTime).eq(Org::getType, "ORG_TYPE").last(" limit 1"),
                        Convert::toLocalDateTime);

        String lastModifyRecordTime;
        // 获取当前时间yyyy-MM-dd HH:mm:ss格式
        String nowDateTime = DateUtil.now();

        if (null == lastDateTime)
        {
            lastModifyRecordTime = "1970-01-01 00:00:00~" + nowDateTime;
        }
        else
        {
            lastModifyRecordTime = DateUtil.formatLocalDateTime(lastDateTime) + "~" + nowDateTime;
        }

        // 查询时间
        List<SyncOaOrgParam.Esb.Data.Datainfos.Datainfo> datainfoList = new ArrayList<>();
        datainfoList.add(SyncOaOrgParam.Esb.Data.Datainfos.Datainfo.builder().lastModifyRecordTime(lastModifyRecordTime)
                .build());

        // 批处理UUID
        String puuid = UUID.randomUUID().toString().replaceAll("-", "").toLowerCase();

        // 拼接查询参数
        SyncOaOrgParam syncOaOrgParam = SyncOaOrgParam.builder()
                .esb(SyncOaOrgParam.Esb.builder()
                        .data(SyncOaOrgParam.Esb.Data.builder()
                                .datainfos(SyncOaOrgParam.Esb.Data.Datainfos.builder()
                                        .datainfo(datainfoList)
                                        .puuid(puuid)
                                        .build())
                                .splitPage(SyncOaOrgParam.Esb.Data.SplitPage.builder()
                                        .countPerPage(200)
                                        .currentPage(1)
                                        .build())
                                .build())
                        .build())
                .build();

        // 开启新线程
        ExecutorService executorService =
                new ThreadPoolExecutor(1, 1, 0L, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>());

        executorService.execute(() ->
        {
            // 开始同步
            syncOAOrgState = true;
            try
            {
                log.info("开始同步OA系统机构信息");
                // 开始时间毫秒数
                long beginMillis = System.currentTimeMillis();
                // 递归查询OA系统机构信息
                List<SyncOaOrgResult.Esb.Data.Datainfos.Datainfo> datainfoSet =
                        recursiveQueryOaOrg(new ArrayList<>(), syncOaOrgParam);
                log.info("共查询到{}条数据", datainfoSet.size());

                log.info("去重OA系统返回的数据");
                // 根据code去重
                List<SyncOaOrgResult.Esb.Data.Datainfos.Datainfo> newDatainfoSet = datainfoSet.stream().collect(
                        Collectors.collectingAndThen(
                                Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(
                                        SyncOaOrgResult.Esb.Data.Datainfos.Datainfo::getCode))), ArrayList::new));

                log.info("去重后有效数据{}条", newDatainfoSet.size());

                log.info("数据解析入库");
                // 转换为平台机构对象
                List<Org> orgList = BeanPlusUtil.toBeanList(newDatainfoSet, Org.class);

                // 筛选出要同步的顶级机构及其下级机构，其他舍弃
                Set<Org> orgs = recursiveQueryChildOrg(jettongOaOrgProperties.getTopcode(), orgList.stream().filter(item -> null != item.getParentCode()).collect(
                        Collectors.groupingBy(Org::getParentCode)));

                if (!orgs.isEmpty())
                {
                    // 查询所有集团机构信息
                    List<Org> allOrg = super.list(Wraps.<Org>lbQ().eq(Org::getType, "ORG_TYPE"));

                    // 转换为Map, key为code,value为id
                    Map<String, Org> codeIdMap = allOrg.stream().collect(Collectors.toMap(Org::getCode, org -> org));

                    // 筛选出要添加的数据
                    List<Org> insertOrgs = orgs.stream().filter(item -> !codeIdMap.containsKey(item.getCode()))
                            .collect(Collectors.toList());

                    // 筛选出要更新的数据
                    List<Org> updateOrgs = orgs.stream().filter(item -> codeIdMap.containsKey(item.getCode()))
                            .collect(Collectors.toList());

                    log.info("解析出{}条需要添加的数据", insertOrgs.size());
                    log.info("解析出{}条需要更新的数据", updateOrgs.size());
                    if (!insertOrgs.isEmpty())
                    {
                        log.info("开始组装要添加的数据");
                        insertOrgs.forEach(item ->
                        {
                            // 主键id
                            Long id = UidGeneratorUtil.getId();
                            item.setId(id);
                            item.setType("ORG_TYPE");
                            if (codeIdMap.containsKey(item.getParentCode()))
                            {
                                item.setParentId(codeIdMap.get(item.getParentCode()).getId());
                            }

                            // 将新加的数据插入codeIdMap中
                            codeIdMap.put(item.getCode(), item);
                        });

                        // 再循环一次处理parentId为空的数据
                        insertOrgs.forEach(item ->
                        {
                            if (null == item.getParentId())
                            {
                                item.setParentId(null == codeIdMap.get(item.getParentCode()) ? null :
                                        codeIdMap.get(item.getParentCode()).getId());
                                // 将修改的数据更新到codeIdMap中
                                codeIdMap.put(item.getCode(), item);
                            }
                        });
                        log.info("组装完成要添加的数据，开始入库");
                        super.saveBatch(insertOrgs, 20);
                        log.info("要添加的数据，入库完成");
                    }

                    // 需要更新的数据
                    if (!updateOrgs.isEmpty())
                    {

                        log.info("开始比对数据差异");
                        List<Org> updateOrgDatas = new ArrayList<>();
                        for (Org org : updateOrgs)
                        {
                            Org oldOrg = codeIdMap.get(org.getCode());
                            if (!org.getName().equals(oldOrg.getName()) || !org.getState().equals(oldOrg.getState()) ||
                                    !org.getDescription().equals(oldOrg.getDescription()) ||
                                    !org.getParentCode().equals(oldOrg.getParentCode()) ||
                                    !org.getCreateTime().equals(oldOrg.getCreateTime()))
                            {
                                // 名称
                                oldOrg.setName(org.getName());
                                // 状态
                                oldOrg.setState(org.getState());
                                // 父组织编码是否改变
                                if (!org.getParentCode().equals(oldOrg.getParentCode()))
                                {
                                    oldOrg.setParentCode(org.getParentCode());
                                    // 修改parentId
                                    oldOrg.setParentId(codeIdMap.get(org.getParentCode()).getId());
                                }
                                oldOrg.setCreateTime(org.getCreateTime());

                                updateOrgDatas.add(oldOrg);
                            }
                        }

                        log.info("数据差异比对完成，共{}条数据有差异", updateOrgDatas.size());
                        if (!updateOrgDatas.isEmpty())
                        {
                            log.info("需要更新的数据开始入库");
                            super.updateBatchById(updateOrgDatas, 20);
                            log.info("需要更新的数据入库完成");
                            insertOrgs.addAll(updateOrgDatas);
                        }
                    }

                    // 计算所有机构的下级机构
                    log.info("开始计算所有有变更机构的下级机构，共需计算{}条数据", insertOrgs.size());
                    if (!insertOrgs.isEmpty())
                    {
                        // 查询所有机构信息，只查询id和parent_id字段
                        List<Org> allOrgs = super.list(Wraps.<Org>lbQ().select(Org::getId, Org::getParentId));

                        // 按parentId分组
                        Map<Long, Set<Long>> groupParentIdMap =
                                allOrgs.stream().filter(item -> null != item.getParentId()).collect(
                                        Collectors.groupingBy(Org::getParentId,
                                                Collectors.mapping(Org::getId, Collectors.toSet())));

                        Set<OrgChild> orgChildSet = new HashSet<>();

                        // 有变更的机构，获取所有的上级机构，重新计算这些机构的下级机构
                        Set<Long> allParentId = new HashSet<>();

                        // 将allOrgs转换为Map
                        Map<Long, Long> orgIdParentIdMap = allOrgs.stream().filter(item -> null != item.getParentId()).collect(Collectors.toMap(Org::getId, Org::getParentId));

                        for (Org org : insertOrgs)
                        {
                            allParentId.addAll(recursiveQueryParentId(org.getId(), orgIdParentIdMap));
                        }
                        for (Long orgId : allParentId)
                        {
                            log.info("计算机构id为[{}]的机构的下级机构信息", orgId);
                            Set<Long> childOrgIdSet =
                                    recursiveQueryChildOrgId(orgId, groupParentIdMap);

                            childOrgIdSet.forEach(
                                    item -> orgChildSet.add(
                                            OrgChild.builder().parentId(orgId).childId(item).build()));

                            // 删除所有机构子机构信息
                            orgChildService.remove(Wraps.<OrgChild>lbQ().eq(OrgChild::getParentId, orgId));
                        }

                        log.info("计算所有机构的下级机构完成，开始入库，共{}条数据", orgChildSet.size());

                        if (!orgChildSet.isEmpty())
                        {
                            orgChildService.saveBatch(orgChildSet, 200);
                        }

                        log.info("所有机构的下级机构完成，入库完成");
                    }

                }

                log.info("同步OA系统机构信息结束，共耗时：{}毫秒", System.currentTimeMillis() - beginMillis);
            }
            catch (Exception e)
            {
                log.error("同步OA系统机构信息失败，原因：{}", e.getMessage(), e);
            }
            finally
            {
                syncOAOrgState = false;
                // 关闭线程池
                executorService.shutdown();
            }
        });

        return true;
    }

    @Override
    public void updateOrg(Org org) {
        //判断选中的结构是否是该节点的子集
        Long id = org.getId();
        Long pId = org.getParentId();
        if (!id.equals(pId)) {
            TreeNode parent = new TreeNode();
            parent.setId(id);
            TreeNode target = new TreeNode();
            target.setId(pId);
            List<Org> orgList = tree(null, null, null);
            List<TreeNode> orgs = BeanUtil.copyToList(orgList, TreeNode.class);
            boolean flag = TreeUtil.isChildNode(orgs,parent,target);
            if (flag) {
                throw BizException.validFail("修改失败,原因:不可以将子集目录修改为父级");
            }
            super.baseMapper.updateById(org);
            super.delCache(id);
        }
    }

    /**
     * 递归的方式获取所有子机构，包含自己
     *
     * @param code 机构code
     * @param groupByParentCodeMap 所有机构按parentCode分组
     * @return {@link Set<Org>} 子机构
     * <AUTHOR>
     * @date 2024/4/23 16:34
     * @update 2024/4/23 16:34
     * @since 1.0
     */
    Set<Org> recursiveQueryChildOrg(String code, Map<String, List<Org>> groupByParentCodeMap)
    {
        Set<Org> childOrg = new HashSet<>();
        // 判断是否存在code的parentCode分组
        if (groupByParentCodeMap.containsKey(code))
        {

            List<Org> childOrgSet = groupByParentCodeMap.get(code);

            childOrg.addAll(childOrgSet);

            for (Org o : childOrgSet)
            {
                childOrg.addAll(recursiveQueryChildOrg(o.getCode(), groupByParentCodeMap));
            }
        }
        return childOrg;

    }

    /**
     * 递归方式获取机构的上级机构
     * @param orgId 机构id
     * @param idParentIdMap 所有机构id和parentIdMap
     * @return {@link Set<Long>} 所有上级机构，包含自己
     * <AUTHOR>
     * @date 2024/4/24 9:36
     * @update 2024/4/24 9:36
     * @since 1.0
     */
    Set<Long> recursiveQueryParentId(Long orgId, Map<Long, Long> idParentIdMap)
    {
        Set<Long> orgIds = new HashSet<>();
        orgIds.add(orgId);
        if (idParentIdMap.containsKey(orgId))
        {
            Long parentId = idParentIdMap.get(orgId);
            orgIds.addAll(recursiveQueryParentId(parentId, idParentIdMap));
        }
        return orgIds;
    }

    /**
     * 递归的方式获取所有子机构id
     *
     * @param orgId 机构id
     * @param groupByParentIdMap 所有机构按parentId分组
     * @return {@link Set<Long>} 子机构id
     * <AUTHOR>
     * @date 2024/4/23 16:34
     * @update 2024/4/23 16:34
     * @since 1.0
     */
    Set<Long> recursiveQueryChildOrgId(Long orgId, Map<Long, Set<Long>> groupByParentIdMap)
    {
        Set<Long> childOrg = new HashSet<>();
        // 判断是否存在orgId的parentId分组
        if (groupByParentIdMap.containsKey(orgId))
        {

            Set<Long> childOrgSet = groupByParentIdMap.get(orgId);

            childOrg.addAll(childOrgSet);

            for (Long childOrgId : childOrgSet)
            {
                childOrg.addAll(recursiveQueryChildOrgId(childOrgId, groupByParentIdMap));
            }
        }
        return childOrg;

    }

    /**
     * 递归查询OA系统组织机构信息
     *
     * @param datainfoSet OA系统组织机构信息
     * @param syncOaOrgParam 查询参数
     * @return {@link List<SyncOaOrgResult.Esb.Data.Datainfos.Datainfo>} OA系统组织机构信息
     * <AUTHOR>
     * @date 2024/4/22 10:55
     * @update 2024/4/22 10:55
     * @since 1.0
     */
    private List<SyncOaOrgResult.Esb.Data.Datainfos.Datainfo> recursiveQueryOaOrg(
            List<SyncOaOrgResult.Esb.Data.Datainfos.Datainfo> datainfoSet, SyncOaOrgParam syncOaOrgParam) throws Exception
    {
        log.info("----------------------------------------------");
        log.info("请求OA系统接口");

        log.info("接口地址：{}", jettongOaOrgProperties.getSyncurl());
        log.info("Usercode：{}", jettongOaOrgProperties.getUsercode());
        log.info("Password：{}", jettongOaOrgProperties.getPassword());
        log.info("lastModifyRecordTime：{}",
                syncOaOrgParam.getEsb().getData().getDatainfos().getDatainfo().get(0).getLastModifyRecordTime());
        log.info("CurrentPage：{}", syncOaOrgParam.getEsb().getData().getSplitPage().getCurrentPage());
        log.info("CountPerPage：{}", syncOaOrgParam.getEsb().getData().getSplitPage().getCountPerPage());

        HttpRequest request = HttpUtil.createPost(jettongOaOrgProperties.getSyncurl());

        request.header(SyncOaOrgParam.USER_CODE, jettongOaOrgProperties.getUsercode());
        request.header(SyncOaOrgParam.PASSWORD, jettongOaOrgProperties.getPassword());
        request.header("Content-type", StrPool.CONTENT_TYPE);

        // 查询参数
        String param = JSONObject.toJSONString(syncOaOrgParam);
        AES aes = null;
        // 判断是否需要加密
        if (jettongOaOrgProperties.getIsencry())
        {
            aes = new AES(jettongOaOrgProperties.getEncrykey().getBytes());
            param = aes.encryptHex(param);
        }

        request.body(param);

        HttpResponse response = request.execute();

        if (response.isOk())
        {

            log.info("原始返回结果：{}", response.body());
            SyncOaOrgResult syncOaOrgResult;
            // 判断是否需要解密
            if (jettongOaOrgProperties.getIsencry())
            {
                // 解密并转换为同步OA系统用户返回值对象
                syncOaOrgResult = JSON.parseObject(aes.decryptStr(response.body()), SyncOaOrgResult.class);

                log.info("解密后原始返回结果：{}", aes.decryptStr(response.body()));
            }
            else
            {
                // 转换为同步OA系统用户返回值对象
                syncOaOrgResult = JSON.parseObject(response.body(), SyncOaOrgResult.class);
            }

            // 判断是否成功
            if (SyncOaOrgResult.RESULT_S.equals(syncOaOrgResult.getEsb().getResult()))
            {
                // 当前页数
                int currentPage = syncOaOrgParam.getEsb().getData().getSplitPage().getCurrentPage();

                // 总页数
                int totalPages = syncOaOrgResult.getEsb().getData().getSplitPage().getTotalPages();

                datainfoSet.addAll(syncOaOrgResult.getEsb().getData().getDatainfos().getDatainfo());

                // 如果当前页数小于总页数，递归继续执行
                if (currentPage < totalPages)
                {
                    syncOaOrgParam.getEsb().getData().getSplitPage().setCurrentPage(currentPage + 1);
                    return recursiveQueryOaOrg(datainfoSet, syncOaOrgParam);
                }
            }
            else
            {
                log.error("同步OA系统组织机构失败，原因：{}", syncOaOrgResult.getEsb().getDesc());
            }

        }

        return datainfoSet;
    }
}
