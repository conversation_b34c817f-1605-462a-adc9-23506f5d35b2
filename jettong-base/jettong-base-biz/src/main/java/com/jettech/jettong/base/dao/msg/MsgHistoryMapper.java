package com.jettech.jettong.base.dao.msg;

import com.jettech.basic.base.mapper.SuperMapper;
import com.jettech.jettong.base.entity.msg.MsgHistory;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 消息通知发送历史信息Mapper 接口
 * <AUTHOR>
 * @version 1.0
 * @description 消息通知发送历史信息Mapper 接口
 * @projectName jettong
 * @package com.jettech.jettong.base.dao.msg
 * @className MsgHistoryMapper
 * @date 2023-06-07
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Repository
public interface MsgHistoryMapper extends SuperMapper<MsgHistory>
{

    /**
     * 查询站内信消息
     * @param userId    用户id
     * @param isRead    是否已读
     * @return
     */
    List<MsgHistory> queryLetterMsg(@Param("userId") Long userId);
}
