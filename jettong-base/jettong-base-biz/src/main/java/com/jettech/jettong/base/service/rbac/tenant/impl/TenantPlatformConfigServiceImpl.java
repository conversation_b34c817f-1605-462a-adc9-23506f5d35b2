package com.jettech.jettong.base.service.rbac.tenant.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.jettech.basic.base.entity.SuperEntity;
import com.jettech.basic.base.service.SuperServiceImpl;
import com.jettech.basic.database.mybatis.conditions.Wraps;
import com.jettech.basic.exception.BizException;
import com.jettech.jettong.base.dao.rbac.tenant.TenantPlatformConfigMapper;
import com.jettech.jettong.base.entity.rbac.tenant.TenantPlatformConfig;
import com.jettech.jettong.base.service.rbac.tenant.TenantPlatformConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 平台配置信息（如：logo，名称，版权信息等）业务层
 *
 * <AUTHOR>
 * @version 1.0
 * @description 平台配置信息（如：logo，名称，版权信息等）业务层
 * @projectName jettong
 * @package com.jettech.jettong.base.service.rbac.tenant.impl
 * @className TenantPlatformConfigServiceImpl
 * @date 2021-11-20
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Slf4j
@Service
@DS("master")
public class TenantPlatformConfigServiceImpl extends SuperServiceImpl<TenantPlatformConfigMapper, TenantPlatformConfig>
        implements
        TenantPlatformConfigService
{
    @Override
    public List<TenantPlatformConfig> saveBatch(List<TenantPlatformConfig> tenantPlatformConfigs)
    {
        List<String> codes =
                tenantPlatformConfigs.stream().map(TenantPlatformConfig::getCode).collect(Collectors.toList());
        boolean add = super.count(Wraps.<TenantPlatformConfig>lbQ().in(TenantPlatformConfig::getCode, codes)) > 0;
        if (add)
        {
            throw BizException.validFail("添加失败,原因:code不能重复");
        }
        super.saveBatch(tenantPlatformConfigs);
        return tenantPlatformConfigs;
    }

    @Override
    public boolean removeByIds(Collection<? extends Serializable> ids)
    {
        // 只能删除非默认数据
        boolean remove = super.count(Wraps.<TenantPlatformConfig>lbQ().eq(TenantPlatformConfig::getReadonly, true).in(
                SuperEntity::getId, ids)) > 0;
        if (remove)
        {
            throw BizException.validFail("平台内置数据不能删除");
        }
        return super.removeByIds(ids);
    }

    @Override
    public List<TenantPlatformConfig> updateBatch(List<TenantPlatformConfig> tenantPlatformConfigs)
    {
        for (TenantPlatformConfig tenantPlatformConfig : tenantPlatformConfigs)
        {
            if (check(tenantPlatformConfig.getId(), tenantPlatformConfig.getCode()))
            {
                throw BizException.validFail("报错失败,原因:code不能重复");
            }
        }

        super.saveOrUpdateBatch(tenantPlatformConfigs);

        return tenantPlatformConfigs;
    }

    public boolean check(Long id, String code)
    {
        return super.count(Wraps.<TenantPlatformConfig>lbQ().ne(
                SuperEntity::getId, id).eq(TenantPlatformConfig::getCode, code)) > 0;
    }
}
