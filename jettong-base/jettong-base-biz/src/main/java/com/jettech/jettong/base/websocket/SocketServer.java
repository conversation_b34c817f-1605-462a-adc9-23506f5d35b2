package com.jettech.jettong.base.websocket;

import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;

import javax.websocket.*;
import javax.websocket.server.PathParam;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * websocket 控制器
 *
 * <AUTHOR>
 * @version 1.0
 * @description websocket 控制器
 * @projectName jettong
 * @package com.jettech.jettong.base.websocket
 * @className SocketServer
 * @date 2021/9/15 9:43
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Slf4j
//@Component
//@ServerEndpoint("/annoy/websocketServer/{userId}/{uuid}")
public class SocketServer
{

    /**
     * session map
     */
    private static Map<String, Session> sessionPool = Maps.newHashMapWithExpectedSize(7);
    /**
     * session id 和 userId map
     */
    private static Map<String, String> sessionIds = Maps.newHashMapWithExpectedSize(7);
    private Session session;

    /**
     * 发送信息
     *
     * @param message 发送的消息
     * @param userId 用户id
     * <AUTHOR>
     * @date 2022/11/3 14:46
     * @update 2022/11/3 14:46
     * @since 1.0
     */
    public static void sendMessage(String message, Long userId)
    {
        for (Map.Entry<String, String> entry : sessionIds.entrySet())
        {
            String userIdUuid = entry.getValue();
            // 判断是否是以userId开头的
            if (userIdUuid.indexOf(String.valueOf(userId)) == 0)
            {
                sendMessage(message, userIdUuid);
            }
        }
    }

    /**
     * 发送信息
     *
     * @param message 信息
     * @param userIdUuid 用户和uuid唯一值
     * <AUTHOR>
     * @date 2022/11/4 11:55
     * @update 2022/11/4 11:55
     * @since 1.0
     */
    private static void sendMessage(String message, String userIdUuid)
    {
        Session s = sessionPool.get(userIdUuid);
        if (s != null)
        {
            try
            {
                s.getBasicRemote().sendText(message);
            }
            catch (IOException e)
            {
                log.error("发送信息失败，原因：{}", e.getMessage(), e);
            }
        }
    }

    /**
     * 信息群发，发送给所有在线用户
     *
     * @param message 消息
     * <AUTHOR>
     * @date 2022/11/3 14:48
     * @update 2022/11/3 14:48
     * @since 1.0
     */
    public static void sendAll(String message)
    {
        for (String key : sessionIds.keySet())
        {
            sendMessage(message, sessionIds.get(key));
        }
    }

    /**
     * 批量发送给多个人信息
     *
     * @param message 消息
     * @param userIds 用户id
     * <AUTHOR>
     * @date 2022/11/3 14:52
     * @update 2022/11/3 14:52
     * @since 1.0
     */
    public static void sendMany(String message, List<Long> userIds)
    {
        for (Long userId : userIds)
        {
            sendMessage(message, userId);
        }
    }

    /**
     * 用户连接时出啊发
     *
     * @param session session
     * @param userId 用户id
     * <AUTHOR>
     * @date 2022/11/3 14:44
     * @update 2022/11/3 14:44
     * @since 1.0
     */
    @OnOpen
    public void open(Session session, @PathParam(value = "userId") Long userId, @PathParam(value = "uuid") String uuid)
    {
        this.session = session;

        sessionPool.put(userId + uuid, session);
        sessionIds.put(session.getId(), userId + uuid);

        log.info("创建sessionId为【{}】的连接，userId为【{}】，uuid为【{}】", session.getId(), userId, uuid);
    }

    /**
     * 收到信息时触发
     *
     * @param message 要发送的消息
     * <AUTHOR>
     * @date 2022/11/3 14:39
     * @update 2022/11/3 14:39
     * @since 1.0
     */
    @OnMessage
    public void onMessage(String message)
    {
        log.info("当前发送人sessionId为【{}】，发送内容为：{}", session.getId(), message);
    }

    /**
     * 连接关闭触发
     *
     * <AUTHOR>
     * @date 2022/11/3 14:39
     * @update 2022/11/3 14:39
     * @since 1.0
     */
    @OnClose
    public void onClose()
    {
        sessionPool.remove(sessionIds.get(session.getId()));
        sessionIds.remove(session.getId());
        log.info("关闭连接的sessionId为{}", session.getId());
    }

    /**
     * 发生错误时触发
     *
     * @param session session
     * @param error 错误信息
     * <AUTHOR>
     * @date 2022/11/3 14:40
     * @update 2022/11/3 14:40
     * @since 1.0
     */
    @OnError
    public void onError(Session session, Throwable error)
    {
        log.error("sessionId为【{}】连接的发生错误, 原因：{}", session.getId(), error.getMessage());
        error.printStackTrace();
    }
}