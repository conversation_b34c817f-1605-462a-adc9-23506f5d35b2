package com.jettech.jettong.base.dao.msg;

import com.jettech.basic.base.mapper.SuperMapper;
import com.jettech.basic.database.mybatis.conditions.Wraps;
import com.jettech.basic.database.mybatis.conditions.query.LbqWrapper;
import com.jettech.jettong.base.entity.msg.engine.MsgEngine;
import com.jettech.jettong.common.event.enumeration.MsgEngineType;
import org.springframework.stereotype.Repository;

/**
 * 消息引擎mapper接口
 *
 * <AUTHOR>
 * @version 1.0
 * @description 消息引擎mapper接口
 * @projectName jettong-base-cloud
 * @package com.jettech.jettong.base.dao.msg
 * @className MsgEngineMapper
 * @date 2023/5/16 11:40
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Repository
public interface MsgEngineMapper extends SuperMapper<MsgEngine> {

    /**
     * 根据引擎类型获取消息引擎
     */
    default MsgEngine getMsgEngineByType(MsgEngineType type) {
        LbqWrapper<MsgEngine> wrapper = Wraps.<MsgEngine>lbQ()
                .eq(MsgEngine::getType, type)
                .eq(MsgEngine::getStatus, true);

        return selectOne(wrapper);
    }

}
