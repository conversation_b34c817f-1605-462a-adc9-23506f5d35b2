package com.jettech.jettong.base.properties.file;

import com.jettech.jettong.base.enumeration.file.FileStorageType;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.io.File;
import java.util.HashSet;
import java.util.Set;

/**
 * 获取文件配置文件配置项
 *
 * <AUTHOR>
 * @version 1.0
 * @description 获取文件配置文件配置项
 * @projectName jettong
 * @package com.jettech.jettong.base.properties.file
 * @className FileServerProperties
 * @date 2021/9/15 9:43
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Setter
@Getter
@ConfigurationProperties(prefix = FileServerProperties.PREFIX)
public class FileServerProperties
{
    public static final String PREFIX = "jettong.file";
    /**
     * 指定不同的默认上传存储策略
     * LOCAL: 本地存储
     */
    private FileStorageType storageType = FileStorageType.LOCAL;

    /**
     * 调用接口删除附件时，是否删除文件系统中的文件
     */
    private Boolean delFile = false;

    /**
     * 公开桶
     * <p>
     * 配置后获取连接时改桶下的 url = urlPrefix + path
     * 使用场景：
     * 1. 富文本编辑器
     * 2. 需要url永久访问的场景
     */
    private Set<String> publicBucket = new HashSet<>();

    private Local local = new Local();

    private MinIo minIo = new MinIo();

    private S3 s3 = new S3();

    @Data
    public static class Local
    {
        private String bucket = "dev";

        /**
         * 文件存储路径
         */
        private String storagePath = "";

        public String getStoragePath()
        {
            if (!storagePath.endsWith(File.separator))
            {
                storagePath += File.separator;
            }
            return storagePath;
        }
    }

    @Data
    public static class MinIo {
        /**
         * minio地址+端口号
         */
        private String endpoint = "http://127.0.0.1:9000";

        /**
         * minio用户名
         */
        private String accessKey = "minioadmin";

        /**
         * minio密码
         */
        private String secretKey = "minioadmin";

        /**
         * 文件桶的名称
         */
        private String bucket = "dev";
        /**
         * 默认 URL有效期 2小时
         */
        private Integer expiry = 3600;

        public String getUrlPrefix()
        {
            return endpoint;
        }
    }

    @Data
    public static class S3 {
        /**
         * minio地址+端口号
         */
        private String endpoint = "http://127.0.0.1:9000";

        /**
         * minio用户名
         */
        private String accessKey = "minioadmin";

        /**
         * minio密码
         */
        private String secretKey = "minioadmin";

        /**
         * 文件桶的名称
         */
        private String bucket = "s3-dev";
        /**
         * 默认 URL有效期 2小时
         */
        private Integer expiry = 3600;

        public String getUrlPrefix()
        {
            return endpoint;
        }
    }
}
