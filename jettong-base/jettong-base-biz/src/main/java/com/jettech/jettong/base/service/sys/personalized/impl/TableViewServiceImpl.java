package com.jettech.jettong.base.service.sys.personalized.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;

import com.jettech.basic.base.service.SuperServiceImpl;
import com.jettech.basic.context.ContextUtil;
import com.jettech.basic.database.mybatis.conditions.Wraps;
import com.jettech.basic.database.mybatis.conditions.query.LbqWrapper;
import com.jettech.jettong.base.dao.sys.personalized.TableViewMapper;
import com.jettech.jettong.base.entity.rbac.user.User;
import com.jettech.jettong.base.entity.sys.personalized.TableView;
import com.jettech.jettong.base.entity.sys.personalized.TableViewShare;
import com.jettech.jettong.base.service.rbac.user.UserService;
import com.jettech.jettong.base.service.sys.personalized.TableViewService;
import com.jettech.jettong.base.service.sys.personalized.TableViewShareService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.TreeSet;
import java.util.stream.Collectors;

/**
 * 筛选器信息业务层
 * <AUTHOR>
 * @version 1.0
 * @description 筛选器信息业务层
 * @projectName jettong
 * @package com.jettech.jettong.base.base.service.sys.impl
 * @className TableViewServiceImpl
 * @date 2023-03-23
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Slf4j
@Service
@DS("#thread.tenant")
@RequiredArgsConstructor
public class TableViewServiceImpl extends SuperServiceImpl<TableViewMapper, TableView> implements TableViewService
{

    private final TableViewShareService shareService;
    private final UserService userService;

    @Override
    public List<TableView> enableTableView(String table, Long userId, Long projectId) {

        List<TableView> tableViews = baseMapper.selectEnableTableViews(table, userId, projectId);

        // 根据ID去重
        TreeSet<TableView> tableViewTreeSet = new TreeSet<>(Comparator.comparing(TableView::getId));
        tableViewTreeSet.addAll(tableViews);

        return new ArrayList<>(tableViewTreeSet);
    }

    /**
     * 复制筛选器
     * 1、复制后的为私有，所有人为登录人
     * 2、有名称使用名称，无名称使用原来的名称
     * 3、基础属性不复制（ID、创建和修改人、时间）
     *
     * @param viewId
     * @param name
     * @return
     */
    @Override
    public TableView copy(String viewId, String name) {
        Long userId = ContextUtil.getUserId();

        TableView oldView = this.getById(viewId);

        TableView tableView = TableView.builder()
                .description(oldView.getDescription())
                .name(StrUtil.emptyToDefault(name, oldView.getName()))
                .scope(2)
                .ownerUser(userId)
                .search(oldView.getSearch())
                .sort(oldView.getSort())
                .tableType(oldView.getTableType())
                .type(oldView.getType())
                .build();

        this.save(tableView);

        return tableView;
    }

    @Override
    public List<User> shareUsers(Long viewId) {
        LbqWrapper<TableViewShare> wrapper = Wraps.lbQ();
        wrapper.eq(TableViewShare::getViewId, viewId);
        List<TableViewShare> list = shareService.list(wrapper);
        List<Long> userIds = list.stream().map(TableViewShare::getUserId).distinct().collect(Collectors.toList());
        return userService.findUserById(userIds);
    }
}
