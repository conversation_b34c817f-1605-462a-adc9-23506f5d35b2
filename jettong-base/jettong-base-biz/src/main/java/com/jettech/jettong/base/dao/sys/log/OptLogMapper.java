package com.jettech.jettong.base.dao.sys.log;

import com.jettech.basic.base.mapper.SuperMapper;
import com.jettech.jettong.base.entity.sys.log.OptLog;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 操作日志持久化层接口
 *
 * <AUTHOR>
 * @version 1.0
 * @description 操作日志持久化层接口
 * @projectName jettong
 * @package com.jettech.jettong.base.dao.sys.log
 * @className OptLogMapper
 * @date 2021/9/15 9:43
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Repository
public interface OptLogMapper extends SuperMapper<OptLog>
{
    /**
     * 清理日志
     *
     * @param clearBeforeTime 多久之前的
     * @param clearBeforeNum 多少条
     * @return 是否成功
     */
    Long clearLog(@Param("clearBeforeTime") LocalDateTime clearBeforeTime,
            @Param("clearBeforeNum") Integer clearBeforeNum);


    /**
     * 根据日期范围查询操作日志次数信息
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return {@link Map<String,Object>} 操作日志次数信息
     * <AUTHOR>
     * @date 2023/5/15 16:56
     * @update 2023/5/15 16:56
     * @since 1.0
     */
    List<Map<String, Object>> getOptCountByDate(@Param("startDate") LocalDate startDate,
            @Param("endDate") LocalDate endDate);
}
