package com.jettech.jettong.base.dao.rbac.org;

import com.jettech.basic.base.mapper.SuperMapper;
import com.jettech.jettong.base.entity.rbac.org.Org;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 组织机构持久化层接口
 *
 * <AUTHOR>
 * @version 1.0
 * @description 组织机构持久化层接口
 * @projectName jettong
 * @package com.jettech.jettong.base.dao.rbac.org
 * @className OrgMapper
 * @date 2021/9/15 9:43
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Repository
public interface OrgMapper extends SuperMapper<Org>
{
    /**
     * 递归查询所有下级机构，包含本级机构
     *
     * @param id 机构id
     * @return List<Org> 机构信息
     * <AUTHOR>
     * @date 2021/11/25 11:09
     * @update zxy 2021/11/25 11:09
     * @since 1.0
     */
    List<Org> findChildren(Long id);

    /**
     * 递归查询机构ID的所有父级组织，包含当前机构
     *
     * @param orgId 组织ID
     * @return      父级组织机构列表，平铺形式；有顺序，父级在前
     */
    List<Org> findAllParent(Long orgId);

    int getProductOrgByOrgId(@Param("ids") List<Long> ids);

    /**
     * 修改机构所有下级机构状态
     * @param state 状态
     * @param id 机构id
     * @return {@link int} 修改条数
     * <AUTHOR>
     * @date 2024/4/23 11:35
     * @update 2024/4/23 11:35
     * @since 1.0
     */
    int updateChildOrgStateById(@Param("state") Integer state, @Param("id") Long id);
}
