package com.jettech.jettong.base.service.file.impl.local;

import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.jettech.jettong.base.dao.file.FileMapper;
import com.jettech.jettong.base.domain.file.FileDeleteBO;
import com.jettech.jettong.base.entity.file.File;
import com.jettech.jettong.base.enumeration.file.FileStorageType;
import com.jettech.jettong.base.properties.file.FileServerProperties;
import com.jettech.jettong.base.service.file.impl.AbstractFileStrategy;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;

/**
 * 本地上传策略处理类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 本地上传策略处理类
 * @projectName jettong
 * @package com.jettech.jettong.base.service.file.impl.local
 * @className LocalFileStrategyImpl
 * @date 2021/9/15 9:43
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Slf4j
@DS("#thread.tenant")
@Component("LOCAL")
public class LocalFileStrategyImpl extends AbstractFileStrategy
{
    public LocalFileStrategyImpl(FileServerProperties fileProperties, FileMapper fileMapper)
    {
        super(fileProperties, fileMapper);
    }

    @Override
    protected void uploadFile(File file, MultipartFile multipartFile, String bucket) throws Exception
    {
        FileServerProperties.Local local = fileProperties.getLocal();
        bucket = StrUtil.isEmpty(bucket) ? local.getBucket() : bucket;

        //生成文件名
        String uniqueFileName = getUniqueFileName(file);
        // 相对路径
        String path = getPath(file.getBizType(), uniqueFileName);
        // web服务器存放的绝对路径
        Path absolutePath = Paths.get(local.getStoragePath(), bucket, path);

        // 判断文件存储的路径是否存在
        if (!Files.exists(absolutePath.getParent()))
        {
            Files.createDirectories(absolutePath.getParent());
        }

        // 存储文件
        InputStream input = multipartFile.getInputStream();
        Files.copy(input, absolutePath, StandardCopyOption.REPLACE_EXISTING);

        // 返回数据
        file.setUniqueFileName(uniqueFileName);
        file.setPath(path);
        file.setBucket(bucket);
        file.setStorageType(FileStorageType.LOCAL);
    }

    @Override
    public boolean delete(FileDeleteBO file)
    {
        FileServerProperties.Local local = fileProperties.getLocal();
        java.io.File ioFile =
                new java.io.File(Paths.get(local.getStoragePath(), file.getBucket(), file.getPath()).toString());
        FileUtils.deleteQuietly(ioFile);
        return true;
    }

    @Override
    @SneakyThrows
    public String findUrl(File file)
    {
        FileServerProperties.Local local = fileProperties.getLocal();

        return local.getStoragePath() + file.getBucket() + java.io.File.separator +
                file.getPath();
    }

    @Override
    public Boolean updateFile(java.io.File file, File model) throws Exception
    {

        Files.copy(Paths.get(file.getPath()), Paths.get(model.getPath()), StandardCopyOption.REPLACE_EXISTING);
        return true;
    }
}
