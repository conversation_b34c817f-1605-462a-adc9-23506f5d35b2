package com.jettech.jettong.base.service.sys.personalized;

import com.jettech.basic.base.service.SuperService;
import com.jettech.jettong.base.entity.rbac.user.User;
import com.jettech.jettong.base.entity.sys.personalized.TableView;

import java.util.List;

/**
 * 筛选器信息业务接口
 * <AUTHOR>
 * @version 1.0
 * @description 筛选器信息业务接口
 * @projectName jettong
 * @package com.jettech.jettong.base.base.service.sys
 * @className TableViewService
 * @date 2023-03-23
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
public interface TableViewService extends SuperService<TableView>
{

    /**
     * 查询 用户 项目下（可选）适用于table的筛选器
     * @param table     对应的表
     * @param userId    用户ID
     * @param projectId 项目ID
     * @return          可用的筛选器
     */
    List<TableView> enableTableView(String table, Long userId, Long projectId);

    /**
     * 复制筛选器
     * 1、复制后的为私有，所有人为登录人
     * 2、有名称使用名称，无名称使用原来的名称
     * 3、基础属性不复制（ID、创建和修改人、时间）
     *
     * @param viewId
     * @param name
     * @return
     */
    TableView copy(String viewId, String name);

    /**
     * 查询筛选器已分享给哪些人
     */
    List<User> shareUsers(Long viewId);
}
