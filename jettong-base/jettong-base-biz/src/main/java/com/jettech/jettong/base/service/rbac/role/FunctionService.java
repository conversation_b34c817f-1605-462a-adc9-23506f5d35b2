package com.jettech.jettong.base.service.rbac.role;

import com.jettech.basic.base.service.SuperCacheService;
import com.jettech.jettong.base.entity.rbac.role.Function;

import java.util.List;

/**
 * 菜单功能业务处理层接口
 *
 * <AUTHOR>
 * @version 1.0
 * @description 菜单功能业务处理层接口
 * @projectName jettong
 * @package com.jettech.jettong.base.service.rbac.role
 * @className FunctionService
 * @date 2021/9/15 9:43
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
public interface FunctionService extends SuperCacheService<Function>
{

    /**
     * 根据主键id删除功能信息
     *
     * @param ids 主键id集合
     * @return boolean 是否成功
     * <AUTHOR>
     * @date 2021/10/23 16:20
     * @update zxy 2021/10/23 16:20
     * @since 1.0
     */
    boolean removeByIdWithCache(List<Long> ids);

    /**
     * 新增功能信息
     *
     * @param function 功能信息
     * @return boolean 是否成功
     * <AUTHOR>
     * @date 2021/10/23 16:19
     * @update zxy 2021/10/23 16:19
     * @since 1.0
     */
    boolean saveWithCache(Function function);


    /**
     * 根据菜单id删除功能信息
     *
     * @param menuIds 菜单id集合
     * <AUTHOR>
     * @date 2021/10/23 16:18
     * @update zxy 2021/10/23 16:18
     * @since 1.0
     */
    void removeByMenuIdWithCache(List<Long> menuIds);

    /**
     * 检查功能编码是否可用
     *
     * @param id 功能id
     * @param menuId 菜单id
     * @param code 功能编码
     * @return boolean 是否可用
     * <AUTHOR>
     * @date 2021/10/23 16:16
     * @update zxy 2021/10/23 16:16
     * @since 1.0
     */
    boolean check(Long id, Long menuId, String code);
}
