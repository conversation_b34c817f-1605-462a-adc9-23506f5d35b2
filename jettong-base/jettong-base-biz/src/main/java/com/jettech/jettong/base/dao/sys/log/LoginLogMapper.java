package com.jettech.jettong.base.dao.sys.log;

import com.jettech.basic.base.mapper.SuperMapper;
import com.jettech.jettong.base.entity.sys.log.LoginLog;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 登录日志持久化层接口
 *
 * <AUTHOR>
 * @version 1.0
 * @description 登录日志持久化层接口
 * @projectName jettong
 * @package com.jettech.jettong.base.dao.sys.log
 * @className LoginLogMapper
 * @date 2021/9/15 9:43
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Repository
public interface LoginLogMapper extends SuperMapper<LoginLog>
{
    /**
     * 获取系统总访问次数
     *
     * @return 总访问次数
     */
    Long getTotalLoginPv();

    /**
     * 获取系统今日访问次数
     *
     * @param today 今天
     * @return 今日访问次数
     */
    Long getTodayLoginPv(@Param("today") LocalDate today);

    /**
     * 获取系统今日 登录IV
     *
     * @param today 今天
     * @return 今日访问IP数
     */
    Long getTodayLoginIv(@Param("today") LocalDate today);

    /**
     * 获取系统 登录IV
     *
     * @return 今日访问IP数
     */
    Long getTotalLoginIv();

    /**
     * 获取系统近十天来的访问记录
     *
     * @param tenDays 10天前
     * @param account 用户账号
     * @return 系统近十天来的访问记录
     */
    List<Map<String, String>> findLastTenDaysVisitCount(@Param("tenDays") LocalDateTime tenDays,
            @Param("account") String account);

    /**
     * 按浏览器来统计数量
     *
     * @return 浏览器的数量
     */
    List<Map<String, Object>> findByBrowser();

    /**
     * 按操作系统来统计数量
     *
     * @return 操作系统的数量
     */
    List<Map<String, Object>> findByOperatingSystem();

    /**
     * 清理日志
     *
     * @param clearBeforeTime 多久之前的
     * @param clearBeforeNum 多少条
     * @return 是否成功
     */
    Long clearLog(@Param("clearBeforeTime") LocalDateTime clearBeforeTime,
            @Param("clearBeforeNum") Integer clearBeforeNum);

    /**
     * 根据日期范围查询登录日志登录次数信息
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return {@link Map<String,Object>} 登录日志登录次数信息
     * <AUTHOR>
     * @date 2023/5/15 16:56
     * @update 2023/5/15 16:56
     * @since 1.0
     */
    List<Map<String, Object>> getLoginCountByDate(@Param("startDate") LocalDate startDate,
            @Param("endDate") LocalDate endDate);
}
