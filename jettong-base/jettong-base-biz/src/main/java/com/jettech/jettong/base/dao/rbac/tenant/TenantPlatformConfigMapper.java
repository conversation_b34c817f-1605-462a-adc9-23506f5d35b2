package com.jettech.jettong.base.dao.rbac.tenant;

import com.jettech.basic.base.mapper.SuperMapper;
import com.jettech.jettong.base.entity.rbac.tenant.TenantPlatformConfig;
import org.springframework.stereotype.Repository;

/**
 * 平台配置信息（如：logo，名称，版权信息等）Mapper 接口
 *
 * <AUTHOR>
 * @version 1.0
 * @description 平台配置信息（如：logo，名称，版权信息等）Mapper 接口
 * @projectName jettong
 * @package com.jettech.jettong.base.dao.rbac.tenant
 * @className TenantPlatformConfigMapper
 * @date 2021-11-20
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Repository
public interface TenantPlatformConfigMapper extends SuperMapper<TenantPlatformConfig>
{

}
