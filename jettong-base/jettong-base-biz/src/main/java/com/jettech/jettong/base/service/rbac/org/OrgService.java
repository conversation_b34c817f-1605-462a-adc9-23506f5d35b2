package com.jettech.jettong.base.service.rbac.org;

import com.jettech.basic.base.service.SuperCacheService;
import com.jettech.basic.model.LoadService;
import com.jettech.jettong.base.entity.rbac.org.Org;
import com.jettech.jettong.base.entity.rbac.team.SysTeam;
import com.jettech.jettong.base.vo.rbac.org.OrgUserVO;

import java.util.List;

/**
 * 组织机构业务处理层接口
 *
 * <AUTHOR>
 * @version 1.0
 * @description 组织机构业务处理层接口
 * @projectName jettong
 * @package com.jettech.jettong.base.service.rbac.org
 * @className OrgService
 * @date 2021/9/15 9:43
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
public interface OrgService extends SuperCacheService<Org>, LoadService
{

    /**
     * 批量删除以及删除其子机构
     *
     * @param ids 机构id
     * @return boolean 是否成功
     * <AUTHOR>
     * @date 2021/10/24 10:15
     * @update zxy 2021/10/24 10:15
     * @since 1.0
     */
    boolean remove(List<Long> ids);

    /**
     * 修改机构排序和父级机构
     *
     * @param parentId 父级机构
     * @param parentCode 父级机构
     * @param orgId 修改的机构id
     * @param preOrgId 上一个机构
     * <AUTHOR>
     * @date 2021/11/25 10:15
     * @update zxy 2021/11/25 10:15
     * @since 1.0
     */
    void updateSort(Long parentId, String parentCode, Long orgId, Long preOrgId);

    /**
     * 根据机构id递归查询所有机构信息
     *
     * @param id 主键id
     * @return List<Org> 机构集合信息
     * <AUTHOR>
     * @date 2021/10/24 10:16
     * @update zxy 2021/10/24 10:16
     * @since 1.0
     */
    List<Org> findChildren(Long id);

    /**
     * 检测机构名称是否存在
     *
     * @param id 主键id
     * @param name 机构名称
     * @return boolean 是否存在
     * <AUTHOR>
     * @date 2021/10/24 10:14
     * @update zxy 2021/10/24 10:14
     * @since 1.0
     */
    boolean check(Long id, String name);

    /**
     * 递归查询机构ID的所有父级组织，包含当前机构
     *
     * @param orgId 组织ID
     * @return      父级组织机构列表，平铺形式；有顺序，父级在前
     */
    List<Org> findAllParent(Long orgId);

    /**
     * 获取所有组织机构和用户
     *
     * @return
     */
    List<OrgUserVO> getOrgUserVO();

    List<Org> tree(String name, Integer state, String leadingBy);

    /**
     * 根据条件查询组织机构信息
     *
     * @param orgId 机构id 当前登录人所在机构id
     * @param name 机构名称（模糊）
     * @param state 状态
     * @return {@link List<Org>} 机构id
     * <AUTHOR>
     * @date 2024/1/26 9:33
     * @update 2024/1/26 9:33
     * @since 1.0
     */
    List<Org> sessionUserTree(Long orgId, String name, Integer state);

    List<SysTeam> getTeamByTreeId(List<Org> tree);

    /**
     * 检测编码重复
     *
     * @param code 角色编码
     * @return 存在返回真
     */
    Boolean check(String code);

    /**
     * 同步OA系统机构信息
     * @return {@link boolean} 同步结果
     * <AUTHOR>
     * @date 2024/4/19 14:42
     * @update 2024/4/19 14:42
     * @since 1.0
     */
    boolean syncOaOrg();

    void updateOrg(Org org);
}
