package com.jettech.jettong.base.service.sys.personalized;

import com.jettech.basic.base.service.SuperService;
import com.jettech.jettong.base.dto.sys.personalized.PersonalizedTableViewSetDefaultDTO;
import com.jettech.jettong.base.entity.sys.personalized.PersonalizedTableView;

import java.util.List;

/**
 * 用户列表视图信息业务接口
 *
 * <AUTHOR>
 * @version 1.0
 * @description 用户列表视图信息业务接口
 * @projectName jettong
 * @package com.jettech.jettong.base.service.sys.personalized
 * @className PersonalizedTableViewService
 * @date 2021-11-20
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
public interface PersonalizedTableViewService extends SuperService<PersonalizedTableView>
{

    /**
     * 添加列表视图信息
     *
     * @param personalizedTableView 列表视图信息
     * <AUTHOR>
     * @date 2022/6/10 10:47
     * @update 2022/6/10 10:47
     * @since 1.0
     */
    void savePersonalizedTableView(PersonalizedTableView personalizedTableView);

    /**
     * 设置默认视图信息，
     * WARN：会删除原来的同名视图（name、userId、tableCode相同视为同一个视图）
     * @param lastView  最后一次使用的视图
     */
    void saveLastUseFilter(PersonalizedTableView lastView);

    /**
     * 设置默认视图信息
     * @param tableViewId 视图id
     * @param tableCode   表code
     * @param userId    用户id
     */
    void setDefaultView(Long tableViewId, String tableCode, Long userId);

    /**
     * 根据视图id删除列表视图信息
     *
     * @param tableViewIds 列表视图id集合
     * <AUTHOR>
     * @date 2022/6/10 10:47
     * @update 2022/6/10 10:47
     * @since 1.0
     */
    void deletePersonalizedTableView(List<Long> tableViewIds);

    /**
     * 修改列表视图信息
     *
     * @param personalizedTableView 列表视图信息
     * <AUTHOR>
     * @date 2022/6/10 10:47
     * @update 2022/6/10 10:47
     * @since 1.0
     */
    void updatePersonalizedTableView(PersonalizedTableView personalizedTableView);

    /**
     * 设置默认视图信息
     *
     * @param model 列表视图信息
     * <AUTHOR>
     * @date 2022/6/10 10:51
     * @update 2022/6/10 10:51
     * @since 1.0
     */
    void setDefault(PersonalizedTableViewSetDefaultDTO model);

    /**
     * 根据用户id和列表code查询列表视图信息
     *
     * @param userId 用户id
     * @param tableCode 列表code
     * @return List<PersonalizedTableView> 列表视图信息
     * <AUTHOR>
     * @date 2022/9/29 17:27
     * @update 2022/9/29 17:27
     * @since 1.0
     */
    List<PersonalizedTableView> findByUserIdAndTableCode(Long userId, String tableCode);

    /**
     *  判断是否重名
     * @param name 视图名称
     * @param id 视图id
     * @param userId 所属人
     * @return
     */
    Boolean checkNameRepeat(String name,Long id,Long userId);
}
