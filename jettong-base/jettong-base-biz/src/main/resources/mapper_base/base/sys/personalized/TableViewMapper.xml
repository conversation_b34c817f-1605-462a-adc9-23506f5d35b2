<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jettech.jettong.base.dao.sys.personalized.TableViewMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jettech.jettong.base.entity.sys.personalized.TableView">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="created_by" jdbcType="BIGINT" property="createdBy"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="updated_by" jdbcType="BIGINT" property="updatedBy"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="table_type" jdbcType="VARCHAR" property="tableType"/>
        <result column="type" jdbcType="VARCHAR" property="type"/>
        <result column="scope" jdbcType="INTEGER" property="scope"/>
        <result column="owner_project" jdbcType="BIGINT" property="ownerProject"/>
        <result column="owner_user" jdbcType="BIGINT" property="ownerUser"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="description" jdbcType="VARCHAR" property="description"/>
        <result column="sort" jdbcType="INTEGER" property="sort"/>
        <result column="search" jdbcType="LONGVARCHAR" property="search"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        `id`,`created_by`,`create_time`,`updated_by`,`update_time`, `type`,
        table_type, scope, owner_project, owner_user, name, description, sort, search
    </sql>


    <select id="selectEnableTableViews" resultType="com.jettech.jettong.base.entity.sys.personalized.TableView">
        select stv.*, (stvs.id IS NOT NULL) as shared,
                EXISTS (SELECT *
                    FROM sys_table_view_collection stvc
                    WHERE stvc.view_id = stv.id and stvc.user_id = #{userId}) AS collected
        from sys_table_view stv
                 left join sys_table_view_share stvs on stv.id = stvs.view_id

        where stv.table_type = #{table}
            and (
                scope = 0
        <if test="projectId != null">
                or (scope = 1 and stv.owner_project = #{projectId})
        </if>
                or (scope = 2 and stv.created_by = #{userId})
                or (scope = 3 and stv.owner_user = #{userId})
                or stvs.user_id = #{userId}
            )
        order by scope desc, collected desc, sort, id
    </select>

    <select id="selectMyTableViews" resultType="com.jettech.jettong.base.entity.sys.personalized.TableView">
        select <include refid="Base_Column_List"/>, shared, collected
        from (
            select stv.*, false as shared,  (stvc.id IS NOT NULL) as collected
            from sys_table_view stv
            left join sys_table_view_collection stvc on stv.id = stvc.view_id

            where ((scope = 2 and stv.owner_user = #{userId}))
            union all
            select stv.*, true as shared,  (stvc.id IS NOT NULL) as collected
            from sys_table_view stv
            right join sys_table_view_share stvs on stv.id = stvs.view_id
            left join sys_table_view_collection stvc on stv.id = stvc.view_id
            where stvs.user_id = #{userId}
            ) as t
        ${ew.customSqlSegment}
    </select>

</mapper>
