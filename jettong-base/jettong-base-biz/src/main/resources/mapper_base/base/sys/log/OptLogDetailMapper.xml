<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jettech.jettong.base.dao.sys.log.OptLogDetailMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jettech.jettong.base.entity.sys.log.OptLogDetail">
        <result column="opt_log_id" jdbcType="LONGVARCHAR" property="optLogId"/>
        <result column="params" jdbcType="LONGVARCHAR" property="params"/>
        <result column="result" jdbcType="LONGVARCHAR" property="result"/>
        <result column="ex_detail" jdbcType="LONGVARCHAR" property="exDetail"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        opt_log_id
        , params, result, ex_detail
    </sql>

</mapper>
