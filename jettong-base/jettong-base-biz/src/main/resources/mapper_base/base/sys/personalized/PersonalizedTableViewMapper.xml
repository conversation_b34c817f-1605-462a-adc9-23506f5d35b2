<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jettech.jettong.base.dao.sys.personalized.PersonalizedTableViewMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jettech.jettong.base.entity.sys.personalized.PersonalizedTableView">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="created_by" jdbcType="BIGINT" property="createdBy"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="updated_by" jdbcType="BIGINT" property="updatedBy"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="table_code" jdbcType="VARCHAR" property="tableCode"/>
        <result column="is_public" jdbcType="BIT" property="isPublic"/>
        <result column="is_protect" jdbcType="BIT" property="isProtect"/>
        <result column="is_built_in" jdbcType="BIT" property="isBuiltIn"/>
        <result column="sort" jdbcType="INTEGER" property="sort"/>
        <result column="search" jdbcType="LONGVARCHAR" property="search"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        `id`
        ,`created_by`,`create_time`,`updated_by`,`update_time`,
        `name`, `user_id`, `table_code`, `is_public`, `is_protect`, `is_built_in`, `sort` `search`
    </sql>

</mapper>
