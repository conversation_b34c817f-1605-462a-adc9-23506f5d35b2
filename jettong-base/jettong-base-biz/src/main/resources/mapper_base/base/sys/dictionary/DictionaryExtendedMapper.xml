<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jettech.jettong.base.dao.sys.dictionary.DictionaryExtendedMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jettech.jettong.base.entity.sys.dictionary.DictionaryExtended">
        <result column="dict_id" jdbcType="BIGINT" property="dictId"/>
        <result column="key" jdbcType="VARCHAR" property="key"/>
        <result column="value" jdbcType="VARCHAR" property="value"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        `dict_id`
        , `key`, `value`
    </sql>

</mapper>
