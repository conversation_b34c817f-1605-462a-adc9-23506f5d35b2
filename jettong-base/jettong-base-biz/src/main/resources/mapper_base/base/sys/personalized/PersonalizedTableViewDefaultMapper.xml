<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jettech.jettong.base.dao.sys.personalized.PersonalizedTableViewDefaultMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jettech.jettong.base.entity.sys.personalized.PersonalizedTableViewDefault">
        <result column="created_by" jdbcType="BIGINT" property="createdBy"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="table_view_id" jdbcType="BIT" property="tableViewId"/>
        <result column="table_code" jdbcType="VARCHAR" property="tableCode"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        `created_by`
        ,`create_time`,
        `table_view_id`, `table_code`, `user_id`
    </sql>

</mapper>
