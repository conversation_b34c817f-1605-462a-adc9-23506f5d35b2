<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jettech.jettong.base.dao.sys.personalized.PersonalizedFixedMenuMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jettech.jettong.base.entity.sys.personalized.PersonalizedFixedMenu">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="created_by" jdbcType="BIGINT" property="createdBy"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="menu_code" jdbcType="VARCHAR" property="menuCode"/>
        <result column="sort" jdbcType="INTEGER" property="sort"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        `id`
        ,`create_time`,`created_by`,
        `user_id`, `menu_code`, `sort`
    </sql>

</mapper>
