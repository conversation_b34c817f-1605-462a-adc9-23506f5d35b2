<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jettech.jettong.base.dao.sys.dictionary.DictionaryMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jettech.jettong.base.entity.sys.dictionary.Dictionary">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="updated_by" jdbcType="BIGINT" property="updatedBy"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="type" jdbcType="VARCHAR" property="type"/>
        <result column="label" jdbcType="VARCHAR" property="label"/>
        <result column="code" jdbcType="VARCHAR" property="code"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="platform" jdbcType="VARCHAR" property="platform"/>
        <result column="state" jdbcType="BIT" property="state"/>
        <result column="description" jdbcType="VARCHAR" property="description"/>
        <result column="sort" jdbcType="INTEGER" property="sort"/>
        <result column="readonly" jdbcType="BIT" property="readonly"/>
        <result column="parent_id" jdbcType="BIGINT" property="parentId"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        `id`
        , `create_time`, `updated_by`, `update_time`,`platform`,
        `type`, `label`, `code`, `name`, `state`, `description`, `sort`, `readonly`, `parent_id`
    </sql>

</mapper>
