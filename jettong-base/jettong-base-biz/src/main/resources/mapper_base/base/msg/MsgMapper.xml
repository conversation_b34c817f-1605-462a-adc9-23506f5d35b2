<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jettech.jettong.base.dao.msg.MsgMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jettech.jettong.base.entity.msg.Msg">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="created_by" jdbcType="BIGINT" property="createdBy"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="updated_by" jdbcType="BIGINT" property="updatedBy"/>
        <result column="biz_id" jdbcType="BIGINT" property="bizId"/>
        <result column="biz_type" jdbcType="VARCHAR" property="bizType"/>
        <result column="msg_type" jdbcType="VARCHAR" property="msgType"/>
        <result column="title" jdbcType="VARCHAR" property="title"/>
        <result column="content" jdbcType="LONGVARCHAR" property="content"/>
        <result column="author_Id" jdbcType="BIGINT" property="authorId"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        `id`
        ,`create_time`,`created_by`,`update_time`,`updated_by`,
        `biz_id`, `biz_type`, `msg_type`, `title`, `content`, `author_Id`
    </sql>

</mapper>
