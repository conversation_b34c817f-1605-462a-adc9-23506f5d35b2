<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jettech.jettong.base.dao.msg.MsgReceiveMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jettech.jettong.base.entity.msg.MsgReceive">
        <result column="msg_id" jdbcType="BIGINT" property="msgId"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="is_read" jdbcType="BIT" property="isRead"/>
        <result column="read_time" jdbcType="TIMESTAMP" property="readTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        `read_time`
        , `msg_id`, `user_id`, `is_read`
    </sql>

</mapper>
