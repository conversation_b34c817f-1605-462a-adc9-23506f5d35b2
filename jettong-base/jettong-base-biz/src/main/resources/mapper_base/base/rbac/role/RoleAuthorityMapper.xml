<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jettech.jettong.base.dao.rbac.role.RoleAuthorityMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jettech.jettong.base.entity.rbac.role.RoleAuthority">
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="created_by" jdbcType="BIGINT" property="createdBy"/>
        <result column="authority_id" jdbcType="BIGINT" property="authorityId"/>
        <result column="authority_type" jdbcType="VARCHAR" property="authorityType"/>
        <result column="role_id" jdbcType="BIGINT" property="roleId"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        `create_time`
        ,`created_by`,
        `authority_id`, `authority_type`, `role_id`
    </sql>

</mapper>
