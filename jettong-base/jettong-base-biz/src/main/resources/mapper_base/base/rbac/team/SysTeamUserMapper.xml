<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jettech.jettong.base.dao.rbac.team.SysTeamUserMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jettech.jettong.base.entity.rbac.team.SysTeamUser">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="created_by" jdbcType="BIGINT" property="createdBy"/>
        <result column="team_id" jdbcType="BIGINT" property="teamId"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        `id`,`create_time`,`created_by`,
        team_id, user_id
    </sql>

</mapper>
