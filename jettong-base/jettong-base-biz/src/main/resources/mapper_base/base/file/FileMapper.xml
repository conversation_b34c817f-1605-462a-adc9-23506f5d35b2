<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jettech.jettong.base.dao.file.FileMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jettech.jettong.base.entity.file.File">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="created_by" jdbcType="BIGINT" property="createdBy"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="updated_by" jdbcType="BIGINT" property="updatedBy"/>
        <result column="biz_type" jdbcType="VARCHAR" property="bizType"/>
        <result column="biz_id" jdbcType="BIGINT" property="bizId"/>
        <result column="file_type" jdbcType="VARCHAR" property="fileType"/>
        <result column="storage_type" jdbcType="VARCHAR" property="storageType"/>
        <result column="bucket" jdbcType="VARCHAR" property="bucket"/>
        <result column="path" jdbcType="VARCHAR" property="path"/>
        <result column="unique_file_name" jdbcType="VARCHAR" property="uniqueFileName"/>
        <result column="file_md5" jdbcType="VARCHAR" property="fileMd5"/>
        <result column="original_file_name" jdbcType="VARCHAR" property="originalFileName"/>
        <result column="content_type" jdbcType="VARCHAR" property="contentType"/>
        <result column="suffix" jdbcType="VARCHAR" property="suffix"/>
        <result column="size" jdbcType="BIGINT" property="size"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        `id`
        ,`create_time`,`created_by`,`update_time`,`updated_by`,
        `biz_type`, `biz_id`, `file_type`, `storage_type`, `bucket`, `path`, `unique_file_name`, `file_md5`, `original_file_name`, `content_type`, `suffix`, `size`
    </sql>

</mapper>
