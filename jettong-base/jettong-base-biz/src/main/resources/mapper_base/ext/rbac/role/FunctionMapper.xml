<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jettech.jettong.base.dao.rbac.role.FunctionMapper">

    <select id="findVisibleResource" parameterType="map" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        from sys_function where 1=1
        and `id` in (
        SELECT `authority_id` FROM sys_role_authority ra INNER JOIN sys_user_role ur on ra.`role_id` = ur.`role_id`
        INNER JOIN sys_role r on r.`id` = ra.`role_id`
        where ur.`user_id` = #{userId, jdbcType=BIGINT} and r.`state` = true
        and ra.`authority_type` = 'FUNCTION'
        )
    </select>

    <select id="findMenuIdByFunctionId" parameterType="map" resultType="java.lang.Long">
        SELECT DISTINCT menu_id from sys_function where id in
        <foreach close=")" collection="functionIds" item="id" open="(" separator=",">
            #{id}
        </foreach>
    </select>

</mapper>
