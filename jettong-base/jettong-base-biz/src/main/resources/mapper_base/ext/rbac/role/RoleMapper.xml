<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jettech.jettong.base.dao.rbac.role.RoleMapper">

    <!-- 通用查询结果列 -->
    <sql id="R_Column_List">
        r
        .
        `id`
        , r.`name`, r.`code`, r.`description`, r.`state`, r.`readonly`, r.`ds_type`
    </sql>

    <select id="findRoleByUserId" parameterType="map" resultMap="BaseResultMap">
        select
        <include refid="R_Column_List"/>
        from sys_role r INNER JOIN sys_user_role ur on r.id = ur.role_id
        where `state` = true and ur.user_id = #{userId, jdbcType=BIGINT}
    </select>


    <select id="findUserIdByCode" parameterType="map" resultType="java.lang.Long">
        select ur.`user_id` from sys_user_role ur INNER JOIN sys_role r on r.`id` = ur.`role_id`
        where r.`state` = true
        and r.`code` in
        <foreach close=")" collection="codes" item="code" open="(" separator=",">
            #{code}
        </foreach>
    </select>
</mapper>
