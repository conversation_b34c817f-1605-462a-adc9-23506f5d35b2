<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jettech.jettong.base.dao.rbac.team.SysTeamMapper">

    <select id="getProjectTeamCount" resultType="java.lang.Long">
        select count(*) from `project_team` where `team_id`
        in
        <foreach open="(" close=")" collection="teamIds" item="teamId" separator=",">
            #{teamId}
        </foreach>
    </select>

</mapper>
