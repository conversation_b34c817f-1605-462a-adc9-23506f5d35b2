<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jettech.jettong.base.dao.rbac.user.UserMapper">

    <select id="findUserByRoleId" resultMap="BaseResultMap" parameterType="map">
        SELECT u.`id` as id, `account`, `name`, `mobile`
        FROM sys_user u INNER JOIN sys_user_role ur on u.`id` = ur.`user_id`
        where ur.`role_id` = #{roleId, jdbcType=BIGINT}
        <if test="keyword != null and keyword != ''">
            and (u.`account` like #{keyword, typeHandler=fullLike} or u.`name` like #{keyword, typeHandler=fullLike})
        </if>
    </select>


    <update id="incrPasswordErrorNumById">
        update sys_user
        set `password_error_num`       = `password_error_num` + 1,
            `password_error_last_time` = SYSDATE()
        where `id` = #{id, jdbcType=BIGINT}
    </update>
    <update id="resetPassErrorNum">
        update sys_user
        set `password_error_num`       = 0,
            `password_error_last_time` = #{now, jdbcType=TIMESTAMP}
        where `id` = #{id, jdbcType=BIGINT}
    </update>

    <select id="findPage" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM sys_user s ${ew.customSqlSegment}
    </select>

    <select id="findUserByUserExportQuery" resultType="map"
            parameterType="com.jettech.jettong.base.dto.rbac.user.UserExportQuery">
        select su.`name`,
        su.`description`,
        su.`account`,
        su.`id_card`,
        su.`email`,
        su.`mobile`,
        su.`state`,
        (select `name` from sys_dictionary where `type`='USER_TYPE' and `code` = su.`type`) type,
        so.`name` orgName,
        (select group_concat(sr.`name` separator ';')
        from `sys_role` sr
        inner join `sys_user_role` sur on sr.`id` = sur.`role_id`
        where sur.`user_id` = su.`id`) roleName
        from `sys_user` su
        inner join `sys_org` so on su.`org_id` = so.`id`
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="orgId != null">
                and (su.org_id in (select child_id from sys_org_child where parent_id = #{orgId, jdbcType=BIGINT}) or su.org_id = #{orgId, jdbcType=BIGINT})
            </if>
            <if test="account != null and account != ''">
                su.`account` like #{account, typeHandler=fullLike}
            </if>
            <if test="name != null and name != ''">
                su.`name` like #{name, typeHandler=fullLike}
            </if>
            <if test="readonly != null">
                su.`readonly` = #{readonly}
            </if>
            <if test="idCard != null and idCard != ''">
                su.`id_card` like #{idCard, typeHandler=fullLike}
            </if>
            <if test="email != null and email != ''">
                su.`email` like #{email, typeHandler=fullLike}
            </if>
            <if test="mobile != null and mobile != ''">
                su.`mobile` like #{mobile, typeHandler=fullLike}
            </if>
            <if test="state != null">
                su.`readonly` = #{state}
            </if>
            <if test="type != null">
                su.`type` = #{type}
            </if>
        </trim>
    </select>

</mapper>
