<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jettech.jettong.base.dao.rbac.org.OrgMapper">

    <select id="findChildren" resultMap="BaseResultMap" parameterType="long">
        select
        <include refid="Base_Column_List"/>
        from
        `sys_org`
        where (id in (select child_id from sys_org_child where parent_id = #{id}) or id = #{id})
    </select>


    <select id="findAllParent" resultMap="BaseResultMap" parameterType="long">
        select
        <include refid="Base_Column_List"/>
        from(
        select
        _id,
        lvl
        from
        (
        select
        @orgid as _id,
        ( select @orgid := parent_id from sys_org where id = _id ) as parent_id,
        @l := @l + 1 as lvl
        from
        ( select @orgid := #{orgid}, @l := 0 ) vars,
        sys_org h
        where
        @orgid is not null
        ) t1
        where
        t1.parent_id is not null
        and @orgid is not null
        ) t2,
        sys_org
        where
        t2._id = id
        order by
        t2.lvl desc
    </select>

    <select id="getProductOrgByOrgId" resultType="java.lang.Integer">
        select
        count(*)
        from
        `product_org`
        where org_id in
        <foreach close=")" collection="ids" item="id" open="(" separator=",">
            #{id}
        </foreach>
    </select>
</mapper>
