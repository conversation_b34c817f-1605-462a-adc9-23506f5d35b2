<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jettech.jettong.base.dao.rbac.role.MenuMapper">
    <!-- 通用查询结果列 -->
    <sql id="V_Column_List">
        `id`
        , `code`, `name`, `path`, `state`, `sort`, `icon`, `component`, `parent_id`, `is_hide`, `show_children`, `is_button`,`is_external`, `external_url`
    </sql>

    <select id="findVisibleMenu" parameterType="map"
            resultMap="BaseResultMap">
        SELECT
        <include refid="V_Column_List"/>
        from (
        SELECT
        <include refid="V_Column_List"/>
        from sys_menu where `state` = true and `is_general` = true
        UNION ALL
        SELECT
        <include refid="V_Column_List"/>
        from sys_menu where `state` = true and `is_general` = false
        and `id` in (
        SELECT distinct `authority_id` FROM sys_role_authority ra
        INNER JOIN sys_user_role ur on ra.`role_id` = ur.`role_id`
        INNER JOIN sys_role r on r.`id` = ra.`role_id`
        where ur.`user_id` = #{userId, jdbcType=BIGINT} and r.`state` = true
        )
        ) tmp ORDER BY `sort` asc
    </select>


</mapper>
