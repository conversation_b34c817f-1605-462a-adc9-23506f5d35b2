<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jettech.jettong.base.dao.rbac.team.SysTeamUserMapper">

    <select id="getProjectTeamUserCount" resultType="java.lang.Long">
        select count(*) from `project_user_role` where `team_id` = #{teamId}
        and `user_id` in
        <foreach open="(" close=")" collection="userIds" item="userId" separator=",">
            #{userId}
        </foreach>
    </select>

</mapper>
