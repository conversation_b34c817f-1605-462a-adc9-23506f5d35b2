<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jettech.jettong.base.dao.sys.log.OptLogDetailMapper">

    <delete id="clearLog" parameterType="map">
        delete from `sys_opt_log_detail`
        where exists (
        select `id` from `sys_opt_log`
        where `id` = `opt_log_id`
        <if test="clearBeforeTime != null">
            AND `create_time` <![CDATA[ <= ]]> #{clearBeforeTime}
        </if>
        <if test="clearBeforeNum  != null">
            AND `id` NOT in(
            SELECT `id` FROM(
            SELECT `id` FROM sys_opt_log AS t
            ORDER BY t.`create_time` desc LIMIT 0, #{clearBeforeNum}
            ) t1
            )
        </if>
        )
    </delete>
</mapper>
