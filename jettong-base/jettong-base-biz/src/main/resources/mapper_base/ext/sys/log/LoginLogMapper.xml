<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jettech.jettong.base.dao.sys.log.LoginLogMapper">

    <select id="getTotalLoginPv" resultType="long">
        select count(1)
        from sys_login_log
    </select>

    <select id="getTodayLoginPv" resultType="long" parameterType="map">
        select count(1)
        from sys_login_log
        where login_date = #{today}
    </select>

    <select id="getTotalLoginIv" resultType="long">
        select count(distinct (request_ip))
        from sys_login_log
    </select>

    <select id="getTodayLoginIv" resultType="long" parameterType="map">
        select count(distinct (request_ip))
        from sys_login_log
        where login_date = #{today}
    </select>

    <select id="findLastTenDaysVisitCount" resultType="map" parameterType="map">
        select login_date , count(1) `count` from
        ( select id, login_date from sys_login_log where create_time >= #{tenDays}
        <if test="account != null and account != ''">
            and account = #{account}
        </if>
        ) as l group by login_date
    </select>

    <select id="findByBrowser" resultType="map">
        select browser, count(id) `count`
        from sys_login_log
        group by browser
    </select>
    <select id="findByOperatingSystem" resultType="map">
        select operating_system, count(id) `count`
        from sys_login_log
        group by operating_system
    </select>

    <delete id="clearLog" parameterType="map">
        delete from sys_login_log
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="clearBeforeTime != null">
                AND create_time <![CDATA[ <= ]]> #{clearBeforeTime}
            </if>
            <if test="clearBeforeNum  != null">
                AND id NOT in(
                SELECT id FROM(
                SELECT id FROM sys_login_log AS t
                ORDER BY t.create_time desc LIMIT 0, #{clearBeforeNum}
                ) t1
                )
            </if>
        </trim>
    </delete>

    <select id="getLoginCountByDate" resultType="java.util.Map">
        select login_date as loginDate, count(1) as loginCount
        from (
                 select
                     login_date
                 from
                     sys_login_log
                 where
                     login_date BETWEEN #{startDate} and #{endDate} group by user_id,login_date) t group by login_date ORDER BY login_date
    </select>

</mapper>
