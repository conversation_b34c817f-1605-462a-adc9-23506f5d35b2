<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jettech.jettong.base.dao.sys.log.OptLogMapper">

    <delete id="clearLog" parameterType="map">
        delete from sys_opt_log
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="clearBeforeTime != null">
                AND `create_time` <![CDATA[ <= ]]> #{clearBeforeTime}
            </if>
            <if test="clearBeforeNum  != null">
                AND `id` NOT in(
                SELECT `id` FROM(
                SELECT `id` FROM sys_opt_log AS t
                ORDER BY t.`create_time` desc LIMIT 0, #{clearBeforeNum}
                ) t1
                )
            </if>
        </trim>
    </delete>

    <select id="getOptCountByDate" resultType="java.util.Map">
        select DATE_FORMAT(`create_time`,"%Y-%m-%d") as optDate, count(1) as optCount from `sys_opt_log` where `create_time` BETWEEN #{startDate} and #{endDate} and `created_by` is not null
        group by DATE_FORMAT(`create_time`,"%Y-%m-%d") ORDER BY `create_time`
    </select>
</mapper>
