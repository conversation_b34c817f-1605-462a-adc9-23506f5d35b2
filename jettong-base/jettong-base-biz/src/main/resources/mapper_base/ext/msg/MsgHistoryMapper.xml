<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jettech.jettong.base.dao.msg.MsgHistoryMapper">


    <select id="queryLetterMsg" resultType="com.jettech.jettong.base.entity.msg.MsgHistory">
        SELECT
            h.id,
            h.msg_engine_type,
            h.msg_event_type,
            h.title,
            h.body,
            h.created_by,
            h.create_time,
            r.is_read
        FROM
            sys_msg_history h
                INNER JOIN sys_msg_history_receive r ON h.id = r.msg_history_id
        WHERE
            r.user_id= #{userId}
          AND h.msg_engine_type = 'LETTER'
        order by h.create_time desc
    </select>

</mapper>
