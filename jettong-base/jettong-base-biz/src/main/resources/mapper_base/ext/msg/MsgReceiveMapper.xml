<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jettech.jettong.base.dao.msg.MsgReceiveMapper">

    <delete id="cleanIsReadByDay" parameterType="int">
        delete
        from `sys_msg_receive`
        where `is_read` = true
          and `read_time` &lt; date_sub(now(), interval #{day,jdbcType=INTEGER} DAY)
    </delete>

</mapper>
