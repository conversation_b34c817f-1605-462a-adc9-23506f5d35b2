<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jettech.jettong.base.dao.msg.MsgMapper">

    <resultMap id="MyMsgResultMap" type="com.jettech.jettong.base.vo.msg.MyMsgResult">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="created_by" jdbcType="BIGINT" property="createdBy"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="updated_by" jdbcType="BIGINT" property="updatedBy"/>
        <result column="biz_id" jdbcType="BIGINT" property="bizId"/>
        <result column="biz_type" jdbcType="VARCHAR" property="bizType"/>
        <result column="msg_type" jdbcType="VARCHAR" property="msgType"/>
        <result column="title" jdbcType="VARCHAR" property="title"/>
        <result column="content" jdbcType="LONGVARCHAR" property="content"/>
        <result column="author_Id" jdbcType="BIGINT" property="authorId"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="is_read" jdbcType="BIT" property="isRead"/>
        <result column="read_time" jdbcType="TIMESTAMP" property="readTime"/>
    </resultMap>

    <delete id="cleanNoHaveReceive">
        delete
        from `sys_msg`
        where not exists(select `msg_id` from `sys_msg_receive` where `id` = `msg_id`)
    </delete>

    <select id="findMyMsgPage" resultMap="MyMsgResultMap">
        select
        info.`id`,info.`create_time`,info.`created_by`,info.`update_time`,info.`updated_by`,
        info.`biz_id`, info.`biz_type`, info.`msg_type`, info.`title`, info.`content`, info.`author_Id`,
        rece.`user_id` user_id,
        CASE WHEN (rece.`is_read` is null ) then 0 else rece.`is_read` end as is_read,
        rece.`read_time` read_time
        FROM sys_msg info LEFT JOIN sys_msg_receive rece on info.id = rece.msg_id
        <if test="cm.userId != null">
            and rece.user_id = #{cm.userId, jdbcType=BIGINT}
        </if>
        <where>
            <if test="cm.userId != null">
                and rece.user_id = #{cm.userId, jdbcType=BIGINT}
            </if>
            <if test="cm.isRead == true">
                and rece.is_read = true
            </if>
            <if test="cm.isRead == false">
                and rece.is_read = false
            </if>
            <if test="cm.msgType != null">
                and info.msg_type = #{cm.msgType, jdbcType=VARCHAR}
            </if>
            <if test="cm.bizType != null">
                and info.biz_type = #{cm.bizType, jdbcType=VARCHAR}
            </if>
            <if test="cm.title != null and cm.title != ''">
                and info.title like #{cm.title, typeHandler=fullLike}
            </if>
            <if test="cm.content != null and cm.content != ''">
                and info.content like #{cm.content, typeHandler=fullLike}
            </if>
        </where>
        ORDER BY is_read asc , info.create_time desc
    </select>

    <select id="findByQuery" resultMap="MyMsgResultMap">
        select
        info.`id`,info.`create_time`,info.`created_by`,info.`update_time`,info.`updated_by`,
        info.`biz_id`, info.`biz_type`, info.`msg_type`, info.`title`, info.`content`, info.`author_Id`,
        rece.`user_id` user_id,
        CASE WHEN (rece.`is_read` is null ) then 0 else rece.`is_read` end as is_read,
        rece.`read_time` read_time
        FROM sys_msg info LEFT JOIN sys_msg_receive rece on info.id = rece.msg_id
        <if test="cm.userId != null">
            and rece.user_id = #{cm.userId, jdbcType=BIGINT}
        </if>
        <where>
            <if test="cm.userId != null">
                and rece.user_id = #{cm.userId, jdbcType=BIGINT}
            </if>
            <if test="cm.isRead == true">
                and rece.is_read = true
            </if>
            <if test="cm.isRead == false">
                and rece.is_read = false
            </if>
            <if test="cm.msgType != null">
                and info.msg_type = #{cm.msgType, jdbcType=VARCHAR}
            </if>
            <if test="cm.bizType != null">
                and info.biz_type = #{cm.bizType, jdbcType=VARCHAR}
            </if>
            <if test="cm.title != null and cm.title != ''">
                and info.title like #{cm.title, typeHandler=fullLike}
            </if>
            <if test="cm.content != null and cm.content != ''">
                and info.content like #{cm.content, typeHandler=fullLike}
            </if>
        </where>
        ORDER BY is_read asc , info.create_time desc
    </select>
</mapper>
