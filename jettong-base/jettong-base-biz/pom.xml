<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>jettong-base</artifactId>
        <groupId>com.jettech.jettong</groupId>
        <version>develop</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <modelVersion>4.0.0</modelVersion>
    <artifactId>jettong-base-biz</artifactId>
    <name>${project.artifactId}</name>
    <description>基础服务-业务模块</description>

    <dependencies>
        <dependency>
            <groupId>com.jettech.jettong</groupId>
            <artifactId>jettong-base-entity</artifactId>
            <version>${jettong-project.version}</version>
        </dependency>

        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
        </dependency>

        <dependency>
            <groupId>com.jettech.basic</groupId>
            <artifactId>jettong-boot-base</artifactId>
        </dependency>

        <dependency>
            <groupId>com.jettech.basic</groupId>
            <artifactId>jettong-databases</artifactId>
        </dependency>

        <dependency>
            <groupId>com.jettech.basic</groupId>
            <artifactId>jettong-cache-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jettech.basic</groupId>
            <artifactId>jettong-echo-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>com.jettech.basic</groupId>
            <artifactId>jettong-security-starter</artifactId>
        </dependency>

        <!-- minio -->
        <dependency>
            <groupId>io.minio</groupId>
            <artifactId>minio</artifactId>
        </dependency>

        <!--        <dependency>-->
        <!--            <groupId>com.huaweicloud</groupId>-->
        <!--            <artifactId>esdk-obs-java-bundle</artifactId>-->
        <!--        </dependency>-->

        <dependency>
            <groupId>eu.bitwalker</groupId>
            <artifactId>UserAgentUtils</artifactId>
        </dependency>

        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-mail</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>com.joyent.util</groupId>
            <artifactId>fast-md5</artifactId>
        </dependency>

        <!-- websocket -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-websocket</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-text</artifactId>
            <version>1.10.0</version>
        </dependency>
        <dependency>
            <groupId>com.google.code.gson</groupId>
            <artifactId>gson</artifactId>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>software.amazon.awssdk</groupId>
            <artifactId>s3</artifactId>
        </dependency>
    </dependencies>
</project>
