package com.jettech.jettong.base.entity.msg;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jettech.basic.model.EchoVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

import static com.baomidou.mybatisplus.annotation.SqlCondition.LIKE;


/**
 * 消息通知事件模板支持变量信息实体类
 * <AUTHOR>
 * @version 1.0
 * @description 消息通知事件模板支持变量信息实体类
 * @projectName jettong
 * @package com.jettech.jettong.base.base.entity.msg
 * @className MsgEventVariable
 * @date 2023-05-22
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sys_msg_event_variable")
@ApiModel(value = "MsgEventVariable", description = "消息通知事件模板支持变量信息")
public class MsgEventVariable implements Serializable, EchoVO
{

    private static final long serialVersionUID = 1L;
    @TableField(exist = false)
    private Map<String, Object> echoMap = new HashMap<>();
    /**
     * 外键，消息通知事件id
     */
    @ApiModelProperty(value = "外键，消息通知事件id")
    @NotNull(message = "请填写外键，消息通知事件id")
    @TableField(value = "`event_id`")
    private Long eventId;

    /**
     * 变量名称
     */
    @ApiModelProperty(value = "变量名称")
    @NotEmpty(message = "请填写变量名称")
    @Size(max = 100, message = "变量名称长度不能超过100")
    @TableField(value = "`name`", condition = LIKE)
    private String name;

    /**
     * 变量描述
     */
    @ApiModelProperty(value = "变量描述")
    @Size(max = 1000, message = "变量描述长度不能超过1000")
    @TableField(value = "`description`", condition = LIKE)
    private String description;

    /**
     * 变量key
     */
    @ApiModelProperty(value = "变量key")
    @NotEmpty(message = "请填写变量key")
    @Size(max = 100, message = "变量key长度不能超过100")
    @TableField(value = "`key`", condition = LIKE)
    private String key;

    @Builder
    public MsgEventVariable(
            Long eventId, String name, String description, String key)
    {
        this.eventId = eventId;
        this.name = name;
        this.description = description;
        this.key = key;
    }

}
