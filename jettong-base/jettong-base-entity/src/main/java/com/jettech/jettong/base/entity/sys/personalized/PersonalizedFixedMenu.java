package com.jettech.jettong.base.entity.sys.personalized;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jettech.basic.base.entity.SuperEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;

/**
 * 用户自定义固定菜单信息实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 用户自定义固定菜单信息实体类
 * @projectName jettong
 * @package com.jettech.jettong.base.entity.sys.personalized
 * @className PersonalizedFixedMenu
 * @date 2021-11-29
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("sys_personalized_fixed_menu")
@ApiModel(value = "PersonalizedFixedMenu", description = "用户自定义固定菜单信息")
@AllArgsConstructor
public class PersonalizedFixedMenu extends SuperEntity<Long>
{

    private static final long serialVersionUID = 1L;

    /**
     * 用户id
     */
    @ApiModelProperty(value = "用户id")
    @NotNull(message = "请填写用户id")
    @TableField(value = "`user_id`")
    @Excel(name = "用户id")
    private Long userId;

    /**
     * 菜单code
     */
    @ApiModelProperty(value = "菜单code")
    @Size(max = 20, message = "菜单code长度不能超过20")
    @TableField(value = "`menu_code`")
    @Excel(name = "菜单code")
    private String menuCode;

    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    @TableField(value = "`sort`")
    @Excel(name = "排序")
    private Integer sort;

    @Builder
    public PersonalizedFixedMenu(Long id, LocalDateTime createTime, Long createdBy,
            Long userId, String menuCode, Integer sort)
    {
        this.id = id;
        this.createTime = createTime;
        this.createdBy = createdBy;
        this.userId = userId;
        this.menuCode = menuCode;
        this.sort = sort;
    }

}
