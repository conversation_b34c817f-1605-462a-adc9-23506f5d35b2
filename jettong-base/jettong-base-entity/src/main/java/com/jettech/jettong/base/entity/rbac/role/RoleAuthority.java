package com.jettech.jettong.base.entity.rbac.role;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jettech.jettong.base.enumeration.rbac.AuthorizeType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.time.LocalDateTime;

import static com.baomidou.mybatisplus.annotation.SqlCondition.LIKE;

/**
 * 角色权限实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 角色权限实体类
 * @projectName jettong
 * @package com.jettech.jettong.base.entity.rbac.role
 * @className RoleAuthority
 * @date 2021/10/14 9:43
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@TableName("sys_role_authority")
@ApiModel(value = "RoleAuthority", description = "角色的资源")
public class RoleAuthority implements Serializable
{

    private static final long serialVersionUID = 1L;

    /**
     * 菜单或功能id
     * #sys_function #sys_menu
     */
    @ApiModelProperty(value = "菜单或功能id")
    @NotNull(message = "菜单或功能id不能为空")
    @TableField("authority_id")
    @Excel(name = "菜单或功能id")
    private Long authorityId;

    /**
     * 权限类型
     * #AuthorizeType{MENU:菜单;FUNCTION:功能;}
     */
    @ApiModelProperty(value = "权限类型")
    @NotNull(message = "权限类型不能为空")
    @TableField("authority_type")
    @Excel(name = "权限类型", replace = {"菜单_MENU", "资源_FUNCTION", "_null"})
    private AuthorizeType authorityType;

    /**
     * 角色id
     * #sys_role
     */
    @ApiModelProperty(value = "角色id")
    @NotNull(message = "角色id不能为空")
    @TableField("role_id")
    @Excel(name = "角色id")
    private Long roleId;


    @ApiModelProperty(value = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    protected LocalDateTime createTime;

    @ApiModelProperty(value = "创建人ID")
    @TableField(value = "created_by", fill = FieldFill.INSERT)
    protected Long createdBy;

    @Builder
    public RoleAuthority(LocalDateTime createTime, Long createdBy,
            Long authorityId, AuthorizeType authorityType, Long roleId,String platform)
    {
        this.createTime = createTime;
        this.createdBy = createdBy;
        this.authorityId = authorityId;
        this.authorityType = authorityType;
        this.roleId = roleId;
    }

}
