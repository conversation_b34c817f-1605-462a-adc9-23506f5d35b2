package com.jettech.jettong.base.dto.rbac.tenant;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.time.LocalDateTime;


/**
 * 租户新增对象
 *
 * <AUTHOR>
 * @version 1.0
 * @description 租户新增对象
 * @projectName jettong
 * @package com.jettech.jettong.base.dto.rbac.tenant
 * @className TenantSaveDTO
 * @date 2021/9/15 9:43
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "TenantSaveDTO", description = "租户")
public class TenantSaveDTO implements Serializable
{

    private static final long serialVersionUID = 1L;

    /**
     * 租户编码
     */
    @ApiModelProperty(value = "租户编码")
    @NotEmpty(message = "租户编码不能为空")
    @Size(max = 20, message = "租户编码长度不能超过20")
    private String code;
    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    @Size(max = 200, message = "名称长度不能超过200")
    private String name;

    /**
     * 责任人
     */
    @ApiModelProperty(value = "责任人")
    @Size(max = 50, message = "责任人长度不能超过50")
    private String duty;

    /**
     * 有效期
     * 为空表示永久
     */
    @ApiModelProperty(value = "有效期")
    private LocalDateTime expirationTime;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    @Size(max = 500, message = "描述长度不能超过500")
    private String description;

}
