package com.jettech.jettong.base.dto.rbac.team;

import com.jettech.basic.base.entity.SuperEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * 团队信息表修改实体类
 * <AUTHOR>
 * @version 1.0
 * @description 团队信息表修改实体类
 * @projectName jettong
 * @package com.jettech.jettong.base.base.dto
 * @className SysTeamUpdateDTO
 * @date 2023-03-13
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "SysTeamUpdateDTO", description = "团队信息表")
public class SysTeamUpdateDTO implements Serializable
{

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @NotNull(message = "请填写主键", groups = SuperEntity.Update.class)
    private Long id;

    /**
     * 团队名称
     */
    @ApiModelProperty(value = "团队名称")
    @NotEmpty(message = "请填写团队名称")
    @Size(max = 100, message = "团队名称长度不能超过100")
    private String name;
    /**
     * 团队描述
     */
    @ApiModelProperty(value = "团队描述")
    @Size(max = 500, message = "团队描述长度不能超过500")
    private String description;
    /**
     * 父级团队
     */
    @ApiModelProperty(value = "父级团队")
    private Long parentId;
    /**
     * 负责人
     */
    @ApiModelProperty(value = "负责人")
    @NotNull(message = "请填写负责人")
    private Long leaderBy;
    /**
     * 所属机构ID
     */
    @ApiModelProperty(value = "所属机构ID")
    private Long orgId;
}
