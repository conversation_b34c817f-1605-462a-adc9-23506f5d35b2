package com.jettech.jettong.base.entity.msg.template;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jettech.basic.annotation.echo.Echo;
import com.jettech.basic.base.entity.Entity;
import com.jettech.basic.model.EchoVO;
import com.jettech.jettong.base.entity.msg.MsgEvent;
import com.jettech.jettong.base.entity.rbac.user.User;
import com.jettech.jettong.common.event.enumeration.MsgEngineType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;

import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.jettech.jettong.common.constant.BaseEchoConstants.USER_ID_CLASS;


/**
 * 消息模版信息
 *
 * <AUTHOR>
 * @version 1.0
 * @description 消息模版信息
 * @projectName jettong
 * @package com.jettech.jettong.base.entity.msg.engine
 * @className MsgEngine
 * @date 2023-5-16
 * @copyright 2023 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Accessors(chain = true)
@TableName("sys_msg_template")
@ApiModel(value = "MsgTemplate", description = "消息模版信息")
@AllArgsConstructor
public class MsgTemplate extends Entity<Long> implements EchoVO, Serializable
{

    private static final long serialVersionUID = 1L;

    @TableField(exist = false)
    private Map<String, Object> echoMap = new HashMap<>();

    /**
     * 引擎名称
     */
    @ApiModelProperty(value = "引擎名称")
    @Size(max = 100, message = "引擎名称长度不能超过100")
    @TableField(value = "`name`")
    private String name;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    @Size(max = 500, message = "描述长度不能超过500")
    @TableField(value = "`description`")
    private String description;

    /**
     * 是否只读
     */
    @ApiModelProperty(value = "是否只读")
    @TableField(value = "`readonly`")
    private Boolean readonly;

    /**
     * 模板标题
     */
    @ApiModelProperty(value = "模板标题")
    @Size(max = 1000, message = "模板标题不能超过1000")
    @TableField(value = "`title`")
    private String title;

    /**
     * 模板内容
     */
    @ApiModelProperty(value = "模板内容")
    @Size(max = 1000, message = "模板内容不能超过1000")
    @TableField(value = "`body`")
    private String body;

    @ApiModelProperty("创建人ID")
    @TableField(value = "created_by", fill = FieldFill.INSERT)
    @Echo(api = USER_ID_CLASS, beanClass = User.class)
    protected Long createdBy;

    /**
     * 关联引擎类型
     */
    @ApiModelProperty(value = "关联引擎类型")
    @TableField(exist = false)
    private List<MsgEngineType> typeList;

    /**
     * 关联事件
     */
    @ApiModelProperty(value = "关联事件")
    @TableField(exist = false)
    private List<MsgEvent> eventList;

    @Builder
    public MsgTemplate(String name, String description, Boolean readonly, String title, String body) {
        this.name = name;
        this.description = description;
        this.readonly = readonly;
        this.title = title;
        this.body = body;
    }
}
