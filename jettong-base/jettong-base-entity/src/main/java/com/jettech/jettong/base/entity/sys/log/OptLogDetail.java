package com.jettech.jettong.base.entity.sys.log;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 操作日志详情实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 操作日志详情实体类
 * @projectName jettong
 * @package com.jettech.jettong.base.entity.sys.log
 * @className OptLogDetail
 * @date 2021/9/15 9:43
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@TableName("sys_opt_log_detail")
@ApiModel(value = "OptLogDetail", description = "系统日志扩展")
public class OptLogDetail implements Serializable
{

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "操作日志id")
    @TableField("opt_log_id")
    @Excel(name = "操作日志id")
    private Long optLogId;

    /**
     * 请求参数
     */
    @ApiModelProperty(value = "请求参数")
    @TableField("params")
    @Excel(name = "请求参数")
    private String params;

    /**
     * 返回值
     */
    @ApiModelProperty(value = "返回值")
    @TableField("result")
    @Excel(name = "返回值")
    private String result;

    /**
     * 异常描述
     */
    @ApiModelProperty(value = "异常描述")
    @TableField("ex_detail")
    @Excel(name = "异常描述")
    private String exDetail;


    @Builder
    public OptLogDetail(String params, String result, String exDetail)
    {
        this.params = params;
        this.result = result;
        this.exDetail = exDetail;
    }

}
