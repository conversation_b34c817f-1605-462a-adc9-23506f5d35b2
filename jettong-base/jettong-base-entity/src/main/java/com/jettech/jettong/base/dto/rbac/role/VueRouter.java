package com.jettech.jettong.base.dto.rbac.role;

import com.jettech.basic.base.entity.TreeEntity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Size;

/**
 * 构建 Vue路由
 *
 * <AUTHOR>
 * @version 1.0
 * @description 构建 Vue路由
 * @projectName jettong
 * @package com.jettech.jettong.base.dto.rbac.role
 * @className VueRouter
 * @date 2021/9/15 9:43
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class VueRouter extends TreeEntity<VueRouter, Long>
{

    private static final long serialVersionUID = -3327478146308500708L;
    @ApiModelProperty(value = "路径")
    private String path;

    @ApiModelProperty(value = "重定向")
    private String redirect;

    @ApiModelProperty(value = "元数据")
    private RouterMeta meta;

    /**
     * 组件
     */
    @ApiModelProperty(value = "组件")
    @Size(max = 200, message = "组件长度不能超过200")
    private String component;

    @Override
    @JsonIgnore
    public Long getId()
    {
        return this.id;
    }

    @Override
    @JsonIgnore
    public Long getParentId()
    {
        return this.parentId;
    }

    public Boolean getAlwaysShow()
    {
        return getChildren() != null && !getChildren().isEmpty();
    }

}
