package com.jettech.jettong.base.entity.rbac.team;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jettech.basic.annotation.echo.Echo;
import com.jettech.basic.base.entity.SuperEntity;
import com.jettech.basic.model.EchoVO;
import com.jettech.jettong.base.entity.rbac.user.User;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

import static com.jettech.jettong.common.constant.BaseEchoConstants.TEAM_SERVICE_ID_CLASS;
import static com.jettech.jettong.common.constant.BaseEchoConstants.USER_ID_CLASS;


/**
 * 团队成员表实体类
 * <AUTHOR>
 * @version 1.0
 * @description 团队成员表实体类
 * @projectName jettong
 * @package com.jettech.jettong.base.base.entity
 * @className SysTeamUser
 * @date 2023-03-13
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("sys_team_user")
@ApiModel(value = "SysTeamUser", description = "团队成员表")
@AllArgsConstructor
public class SysTeamUser extends SuperEntity<Long> implements EchoVO
{

    private static final long serialVersionUID = 1L;
    @TableField(exist = false)
    private Map<String, Object> echoMap = new HashMap<>();
    /**
     * 团队名称
     */
    @ApiModelProperty(value = "团队名称")
    @NotNull(message = "请填写团队名称")
    @TableField(value = "`team_id`")
    @Echo(api = TEAM_SERVICE_ID_CLASS, beanClass = SysTeam.class)
    private Long teamId;

    /**
     * 团队描述
     */
    @ApiModelProperty(value = "团队描述")
    @NotNull(message = "请填写团队描述")
    @TableField(value = "`user_id`")
    @Echo(api = USER_ID_CLASS, beanClass = User.class)
    private Long userId;


    @Builder
    public SysTeamUser(Long id, LocalDateTime createTime, Long createdBy, 
                    Long teamId, Long userId)
    {
        this.id = id;
        this.createTime = createTime;
        this.createdBy = createdBy;
        this.teamId = teamId;
        this.userId = userId;
    }

}
