package com.jettech.jettong.base.entity.sys.personalized;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jettech.basic.annotation.echo.Echo;
import com.jettech.basic.base.entity.Entity;
import com.jettech.basic.model.EchoVO;
import com.jettech.jettong.base.entity.rbac.user.User;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

import static com.baomidou.mybatisplus.annotation.SqlCondition.LIKE;
import static com.jettech.jettong.common.constant.BaseEchoConstants.USER_ID_CLASS;


/**
 * 筛选器信息实体类
 * <AUTHOR>
 * @version 1.0
 * @description 筛选器信息实体类
 * @projectName jettong
 * @package com.jettech.jettong.base.base.entity.sys
 * @className TableView
 * @date 2023-03-23
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("sys_table_view")
@ApiModel(value = "TableView", description = "筛选器信息")
@AllArgsConstructor
public class TableView extends Entity<Long> implements EchoVO
{

    private static final long serialVersionUID = 1L;
    @TableField(exist = false)
    private Map<String, Object> echoMap = new HashMap<>();
    /**
     * 筛选器表
     */
    @ApiModelProperty(value = "筛选器表")
    @Size(max = 20, message = "筛选器表长度不能超过20")
    @NotNull(message = "请填写筛选器表")
    @TableField(value = "`table_type`", condition = LIKE)
    private String tableType;

    /**
     * 筛选器类型
     */
    @ApiModelProperty(value = "筛选器类型")
    @Size(max = 20, message = "筛选器类型长度不能超过20")
    @TableField(value = "`type`", condition = LIKE)
    @NotNull(message = "请填写筛选器类型")
    private String type;

    /**
     * 筛选器范围，0-共用，1-项目下全部成员可用，2-私有，3-最后使用
     */
    @ApiModelProperty(value = "筛选器范围")
    @NotNull(message = "请填写筛选器范围，0-共用，1-项目下全部成员可用，2-私有，3-最后使用")
    @TableField(value = "`scope`")
    private Integer scope;

    /**
     * 所属项目ID
     */
    @ApiModelProperty(value = "所属项目ID")
    @TableField(value = "`owner_project`")
    private Long ownerProject;

    /**
     * 所属用户ID
     */
    @ApiModelProperty(value = "所属用户ID")
    @TableField(value = "`owner_user`")
    @Echo(api = USER_ID_CLASS, beanClass = User.class)
    private Long ownerUser;

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    @NotEmpty(message = "请填写名称")
    @Size(max = 128, message = "名称长度不能超过128")
    @TableField(value = "`name`", condition = LIKE)
    private String name;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    @Size(max = 255, message = "描述长度不能超过255")
    @TableField(value = "`description`")
    private String description;

    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    @NotNull(message = "请填写排序")
    @TableField(value = "`sort`")
    private Integer sort;

    /**
     * 搜索条件
     */
    @ApiModelProperty(value = "搜索条件")
    @TableField(value = "`search`")
    private String search;

    /**
     * 是否已收藏
     */
    @ApiModelProperty(value = "是否已收藏")
    @TableField(exist = false)
    private Boolean collected;

    /**
     * 被收藏数量
     */
    @ApiModelProperty(value = "被收藏数量")
    @TableField(exist = false)
    private int collectedCount;

    /**
     * 是否是共享筛选器
     */
    @ApiModelProperty(value = "是否为共享筛选器")
    @TableField(exist = false)
    private Boolean shared;

    /**
     * 是否是共享筛选器
     */
    @Echo(api = USER_ID_CLASS, beanClass = User.class)
    @ApiModelProperty(value = "创建人ID")
    @TableField(value = "created_by", fill = FieldFill.INSERT)
    private Long createdBy;


    @Builder
    public TableView(Long id, Long createdBy, LocalDateTime createTime, Long updatedBy, LocalDateTime updateTime, 
                    String tableType, String type, Integer scope, Long ownerProject, Long ownerUser, String name,
                    String description, Integer sort, String search, Boolean collected, Boolean shared)
    {
        this.id = id;
        this.createdBy = createdBy;
        this.createTime = createTime;
        this.updatedBy = updatedBy;
        this.updateTime = updateTime;
        this.tableType = tableType;
        this.type = type;
        this.scope = scope;
        this.ownerProject = ownerProject;
        this.ownerUser = ownerUser;
        this.name = name;
        this.description = description;
        this.sort = sort;
        this.search = search;
        this.collected = collected;
        this.shared = shared;
    }

}
