package com.jettech.jettong.base.domain.file;

import io.swagger.annotations.ApiModel;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 文件查询业务对象
 *
 * <AUTHOR>
 * @version 1.0
 * @description 文件查询业务对象
 * @projectName jettong
 * @package com.jettech.jettong.base.domain.file
 * @className FileGetUrlBO
 * @date 2021/9/15 9:43
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "FileGetUrlVO", description = "附件查询")
public class FileGetUrlBO implements Serializable
{
    private static final long serialVersionUID = 1L;

    @NotBlank(message = "请传入文件路径")
    private String path;

    private String originalFileName;

    private String bucket;
}
