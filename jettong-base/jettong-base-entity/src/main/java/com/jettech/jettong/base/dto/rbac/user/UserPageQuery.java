package com.jettech.jettong.base.dto.rbac.user;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * 用户分页查询对象
 *
 * <AUTHOR>
 * @version 1.0
 * @description 用户对象
 * @projectName jettong
 * @package com.jettech.jettong.base.dto.rbac.user
 * @className UserPageQuery
 * @date 2021/9/15 9:43
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "UserPageQuery", description = "用户")
public class UserPageQuery implements Serializable
{

    private static final long serialVersionUID = 1L;

    /**
     * 账号
     */
    @ApiModelProperty(value = "账号")
    private String account;

    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名")
    private String name;

    /**
     * 组织
     * #sys_org
     *
     * @Echo(api = ORG_ID_CLASS,  beanClass = Org.class)
     */
    @ApiModelProperty(value = "组织")
    private Long orgId;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    private String description;

    /**
     * 内置
     */
    @ApiModelProperty(value = "内置")
    private Boolean readonly;

    /**
     * 集团工号
     */
    @ApiModelProperty(value = "集团工号")
    private String idCard;

    /**
     * 邮箱
     */
    @ApiModelProperty(value = "邮箱")
    private String email;

    /**
     * 手机
     */
    @ApiModelProperty(value = "手机")
    private String mobile;

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    private Boolean state;

    /**
     * 用户类型
     */
    @ApiModelProperty(value = "类型")
    private String type;

    @ApiModelProperty(value = "是否报工")
    private Boolean reportWork;

    /**
     * 角色id集合
     */
    @ApiModelProperty(value = "角色id集合")
    private List<Long> roleIds;

    /**
     * 角色id集合
     */
    @ApiModelProperty(value = "团队id集合")
    private List<Long> teamIds;

}
