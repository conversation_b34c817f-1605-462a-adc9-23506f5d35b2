package com.jettech.jettong.base.entity.msg;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jettech.basic.annotation.echo.Echo;
import com.jettech.basic.base.entity.Entity;
import com.jettech.jettong.base.entity.rbac.user.User;
import com.jettech.jettong.common.event.enumeration.MsgType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;

import static com.jettech.jettong.common.constant.BaseEchoConstants.USER_ID_CLASS;

/**
 * 消息表实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 消息表实体类
 * @projectName jettong
 * @package om.jettech.jettong.base.entity.msg
 * @className Msg
 * @date 2021/9/15 9:43
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("sys_msg")
@ApiModel(value = "Msg", description = "消息表")
@AllArgsConstructor
public class Msg extends Entity<Long>
{

    private static final long serialVersionUID = 1L;

    /**
     * 业务ID
     */
    @ApiModelProperty(value = "业务ID")
    @Size(max = 64, message = "业务ID长度不能超过64")
    @TableField(value = "`biz_id`")
    @Excel(name = "业务ID")
    private String bizId;

    /**
     * 业务类型
     */
    @ApiModelProperty(value = "业务类型")
    @TableField("`biz_type`")
    @Excel(name = "业务类型")
    private String bizType;

    /**
     * 消息类型
     * #MsgType{TO_DO:待办;NOTIFY:通知;NOTICE:公告;EARLY_WARNING:预警;}
     */
    @ApiModelProperty(value = "消息类型")
    @NotNull(message = "消息类型不能为空")
    @TableField("`msg_type`")
    @Excel(name = "消息类型", replace = {"待办_WAIT", "通知_NOTIFY", "公告_PUBLICITY", "预警_WARN", "_null"})
    private MsgType msgType;

    /**
     * 标题
     */
    @ApiModelProperty(value = "标题")
    @Size(max = 255, message = "标题长度不能超过255")
    @TableField(value = "`title`")
    @Excel(name = "标题")
    private String title;

    /**
     * 内容
     */
    @ApiModelProperty(value = "内容")
    @Size(max = 65535, message = "内容长度不能超过65535")
    @TableField("`content`")
    @Excel(name = "内容")
    private String content;

    /**
     * 发布人
     */
    @ApiModelProperty(value = "发布人")
    @TableField(value = "`author_id`")
    @Echo(api = USER_ID_CLASS, beanClass = User.class)
    @Excel(name = "发布人")
    private Long authorId;

    @Builder
    public Msg(Long id, LocalDateTime createTime, Long createdBy, LocalDateTime updateTime, Long updatedBy,
            String bizId, String bizType, MsgType msgType, String title, String content,
            Long authorId)
    {
        this.id = id;
        this.createTime = createTime;
        this.createdBy = createdBy;
        this.updateTime = updateTime;
        this.updatedBy = updatedBy;
        this.bizId = bizId;
        this.bizType = bizType;
        this.msgType = msgType;
        this.title = title;
        this.content = content;
        this.authorId = authorId;
    }

}
