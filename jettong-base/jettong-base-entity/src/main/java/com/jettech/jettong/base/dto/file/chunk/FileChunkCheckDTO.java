package com.jettech.jettong.base.dto.file.chunk;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

/**
 * 分片检测参数
 *
 * <AUTHOR>
 * @version 1.0
 * @description 分片检测参数
 * @projectName jettong
 * @package com.jettech.jettong.base.dto.msg
 * @className MsgDTO
 * @date 2021/9/15 9:43
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@ToString
@ApiModel(value = "FileChunkCheck", description = "文件分片信息")
public class FileChunkCheckDTO
{

    @ApiModelProperty(value = "文件大小")
    private Long size;
    @ApiModelProperty(value = "文件唯一名")
    private String name;
    @ApiModelProperty(value = "分片索引")
    private Integer chunkIndex;
}
