package com.jettech.jettong.base.dto.sys.personalized;

import com.baomidou.mybatisplus.annotation.TableField;
import com.jettech.basic.base.entity.SuperEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 用户列表视图信息修改实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 用户列表视图信息修改实体类
 * @projectName jettong
 * @package com.jettech.jettong.base.dto.sys.personalized
 * @className PersonalizedTableViewUpdateDTO
 * @date 2021-11-20
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "PersonalizedTableViewUpdateDTO", description = "用户列表视图信息")
public class PersonalizedTableViewUpdateDTO implements Serializable
{

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @NotNull(message = "请填写主键", groups = SuperEntity.Update.class)
    private Long id;

    /**
     * 过滤器名称
     */
    @ApiModelProperty(value = "视图名称")
    private String name;

    /**
     * 是否公共视图
     */
    @ApiModelProperty(value = "是否公共视图")
    private Boolean isPublic;

    /**
     * 是否保护视图
     */
    @ApiModelProperty(value = "是否保护视图")
    private Boolean isProtect;

    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    private Integer sort;

    /**
     * 查询条件信息
     */
    @ApiModelProperty(value = "视图查询条件，展示字段等信息")
    private String search;

    /**
     * 是否默认视图
     */
    @ApiModelProperty(value = "是否默认视图")
    private Boolean isDefault;

    @ApiModelProperty(value = "用户id")
    private Long userId;
}
