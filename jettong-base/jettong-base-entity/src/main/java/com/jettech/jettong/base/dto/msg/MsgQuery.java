package com.jettech.jettong.base.dto.msg;

import com.jettech.jettong.common.event.enumeration.MsgType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 消息分页参数
 *
 * <AUTHOR>
 * @version 1.0
 * @description 消息分页参数
 * @projectName jettong
 * @package com.jettech.jettong.base.dto.msg
 * @className MsgQuery
 * @date 2021/9/15 9:43
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@ToString
@Accessors(chain = true)
@ApiModel(value = "MsgQuery", description = "消息分页参数")
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class MsgQuery implements Serializable
{
    private static final long serialVersionUID = -2054606159972155030L;

    @ApiModelProperty(value = "接收人ID", hidden = true)
    private Long userId;

    @ApiModelProperty(value = "是否已读")
    private Boolean isRead;

    @ApiModelProperty(value = "消息类型")
    private MsgType msgType;

    @ApiModelProperty(value = "业务类型")
    private String bizType;

    @ApiModelProperty(value = "内容")
    private String content;

    @ApiModelProperty(value = "标题")
    private String title;

    @ApiModelProperty(value = "发送人")
    private Long authorId;

}
