package com.jettech.jettong.base.entity.msg.template;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jettech.basic.base.entity.SuperEntity;
import com.jettech.basic.model.EchoVO;
import com.jettech.jettong.common.event.enumeration.MsgEngineType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

/**
 * 消息通知模板适用引擎类型信息
 *
 * <AUTHOR>
 * @version 1.0
 * @description 消息通知模板适用引擎类型信息
 * @projectName jettong
 * @package com.jettech.jettong.base.entity.msg.engine
 * @className MsgEngine
 * @date 2023-5-16
 * @copyright 2023 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@TableName("sys_msg_template_engine")
@ApiModel(value = "MsgTemplateEngine", description = "消息通知模板适用引擎类型信息")
@AllArgsConstructor
public class MsgTemplateEngine extends SuperEntity<Long> implements EchoVO, Serializable
{

    private static final long serialVersionUID = 1L;

    @TableField(exist = false)
    private Map<String, Object> echoMap = new HashMap<>();

    /**
     * 模版id
     */
    @ApiModelProperty(value = "模版id")
    @NotNull(message = "模版id不能为空")
    @TableField(value = "`template_id`")
    private Long templateId;

    /**
     * 引擎类型
     */
    @ApiModelProperty(value = "引擎类型")
    @NotNull(message = "引擎类型不能为空")
    @TableField(value = "`engine_type`")
    private MsgEngineType engineType;

    @Builder

    public MsgTemplateEngine(Long templateId, MsgEngineType engineType) {
        this.templateId = templateId;
        this.engineType = engineType;
    }
}
