package com.jettech.jettong.base.entity.rbac.role;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jettech.basic.base.entity.TreeEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.Size;
import java.time.LocalDateTime;

import static com.baomidou.mybatisplus.annotation.SqlCondition.LIKE;

/**
 * 菜单实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 菜单实体类
 * @projectName jettong
 * @package com.jettech.jettong.base.entity.rbac.role
 * @className Menu
 * @date 2021/10/14 9:43
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("sys_menu")
@ApiModel(value = "Menu", description = "菜单")
@AllArgsConstructor
public class Menu extends TreeEntity<Menu, Long>
{

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "编码")
    @Size(max = 50, message = "编码长度不能超过50")
    @TableField(value = "code", condition = LIKE)
    @Excel(name = "编码")
    private String code;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    @Size(max = 200, message = "描述长度不能超过200")
    @TableField(value = "description", condition = LIKE)
    @Excel(name = "描述")
    private String description;

    /**
     * 通用菜单
     * True表示无需分配所有人就可以访问的
     */
    @ApiModelProperty(value = "通用菜单")
    @TableField("is_general")
    @Excel(name = "通用菜单", replace = {"是_true", "否_false", "_null"})
    private Boolean isGeneral;

    /**
     * 是否隐藏
     */
    @ApiModelProperty(value = "是否隐藏")
    @TableField("is_hide")
    @Excel(name = "是否隐藏", replace = {"是_true", "否_false", "_null"})
    private Boolean isHide;

    /**
     * 是否显示子级
     */
    @ApiModelProperty(value = "是否显示子级")
    @TableField("show_children")
    @Excel(name = "是否显示子级", replace = {"是_true", "否_false", "_null"})
    private Boolean showChildren;

    /**
     * 是否为按钮
     */
    @ApiModelProperty(value = "是否为按钮")
    @TableField("is_button")
    @Excel(name = "是否为按钮", replace = {"是_true", "否_false", "_null"})
    private Boolean isButton;

    /**
     * 路径
     */
    @ApiModelProperty(value = "路径")
    @Size(max = 1000, message = "路径长度不能超过1000")
    @TableField(value = "path", condition = LIKE)
    @Excel(name = "路径")
    private String path;

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    @TableField("state")
    @Excel(name = "状态", replace = {"是_true", "否_false", "_null"})
    private Boolean state;

    /**
     * 菜单图标
     */
    @ApiModelProperty(value = "菜单图标")
    @Size(max = 255, message = "菜单图标长度不能超过255")
    @TableField(value = "icon", condition = LIKE)
    @Excel(name = "菜单图标")
    private String icon;

    /**
     * 组件
     */
    @ApiModelProperty(value = "组件")
    @Size(max = 200, message = "组件长度不能超过200")
    @TableField(value = "component", condition = LIKE)
    @Excel(name = "菜单图标")
    private String component;

    /**
     * 内置
     */
    @ApiModelProperty(value = "内置")
    @TableField("readonly")
    @Excel(name = "内置", replace = {"是_true", "否_false", "_null"})
    private Boolean readonly;
    /**
     * 外部平台-所属平台
     */
    @ApiModelProperty(value = "外部平台-所属平台")
    @Size(max = 200, message = "外部平台-所属平台不能超过200")
    @TableField(value = "platform", condition = LIKE)
    @Excel(name = "外部平台-所属平台")
    private String platform;

    /**
     * 是否外部菜单
     */
    @ApiModelProperty(value = "是否外部菜单")
    @TableField("is_external")
    @Excel(name = "是否外部菜单", replace = {"是_true", "否_false", "_null"})
    private Boolean isExternal;

    /**
     * 外部菜单url
     */
    @ApiModelProperty(value = "外部菜单url")
    @TableField("external_url")
    @Excel(name = "外部菜单url")
    private String externalUrl;

    @Builder
    public Menu(Long id, String code, String name, Integer sort, Long parentId, Long createdBy,
            LocalDateTime createTime,
            Long updatedBy, LocalDateTime updateTime,
            String description, Boolean isGeneral, Boolean showChildren, Boolean isButton, String path, Boolean state,
            String icon, String component, Boolean readonly, Boolean isExternal, String externalUrl,String platform)
    {
        this.id = id;
        this.code = code;
        this.name = name;
        this.sort = sort;
        this.parentId = parentId;
        this.createdBy = createdBy;
        this.createTime = createTime;
        this.updatedBy = updatedBy;
        this.updateTime = updateTime;
        this.description = description;
        this.isGeneral = isGeneral;
        this.showChildren = showChildren;
        this.isButton = isButton;
        this.path = path;
        this.state = state;
        this.platform = platform;
        this.icon = icon;
        this.component = component;
        this.readonly = readonly;
        this.isExternal = isExternal;
        this.externalUrl = externalUrl;
    }
}
