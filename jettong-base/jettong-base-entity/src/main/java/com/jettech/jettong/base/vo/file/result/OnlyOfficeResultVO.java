package com.jettech.jettong.base.vo.file.result;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * OnlyOffice相关配置
 *
 * <AUTHOR>
 * @version 1.0
 * @description OnlyOffice相关配置
 * @projectName jettong-tm
 * @package com.jettech.jettong.base.vo.file.result
 * @className OnlyOfficeResultVO
 * @date 2022/12/5 10:34
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "OnlyOfficeResultVO", description = "OnlyOffice相关配置")
public class OnlyOfficeResultVO
{

    @ApiModelProperty(value = "是否开启OnlyOffice在线编辑和预览")
    private Boolean edit;

    @ApiModelProperty(value = "OnlyOffice访问地址")
    private String url;

}
