package com.jettech.jettong.base.dto.rbac.role;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * 拥有菜单功能对象
 *
 * <AUTHOR>
 * @version 1.0
 * @description 拥有菜单功能对象
 * @projectName jettong
 * @package com.jettech.jettong.base.dto.rbac.role
 * @className AuthorityFunctionDTO
 * @date 2021/9/15 9:43
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "AuthorityFunctionDTO", description = "权限资源")
public class AuthorityFunctionDTO implements Serializable
{
    @ApiModelProperty(value = "是否启用URI/按钮权限")
    private Boolean enabled;

    @ApiModelProperty(value = "是否区分大小写")
    private Boolean caseSensitive;

    @ApiModelProperty(value = "拥有的功能编码")
    private List<String> functionList;

    @ApiModelProperty(value = "拥有的角色编码")
    private List<String> roleList;
}
