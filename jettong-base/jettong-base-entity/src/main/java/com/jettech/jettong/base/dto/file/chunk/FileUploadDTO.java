package com.jettech.jettong.base.dto.file.chunk;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;


/**
 * 文件分片上传实体
 *
 * <AUTHOR>
 * @version 1.0
 * @description 文件分片上传实体
 * @projectName jettong
 * @package com.jettech.jettong.base.dto.msg
 * @className MsgDTO
 * @date 2021/9/15 9:43
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@ApiModel(value = "FileUpload", description = "文件分片上传实体")
@ToString
public class FileUploadDTO
{
    @ApiModelProperty(value = "md5", notes = "webuploader 自带的md5算法值， 与后端生成的不一致")
    private String md5;
    @ApiModelProperty(value = "大小")
    private Long size;
    @ApiModelProperty(value = "文件唯一名 md5.js生成的, 与后端生成的一致")
    private String name;
    @ApiModelProperty(value = "分片总数")
    private Integer chunks;
    @ApiModelProperty(value = "当前分片")
    private Integer chunk;
    @ApiModelProperty(value = "最后更新时间")
    private String lastModifiedDate;
    @ApiModelProperty(value = "类型")
    private String type;
    @ApiModelProperty(value = "后缀")
    private String ext;
    @ApiModelProperty(value = "文件夹id")
    private Long folderId;
}
