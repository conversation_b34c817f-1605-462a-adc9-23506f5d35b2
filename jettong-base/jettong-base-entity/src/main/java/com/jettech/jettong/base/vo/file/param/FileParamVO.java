package com.jettech.jettong.base.vo.file.param;

import com.jettech.jettong.base.enumeration.file.FileStorageType;
import com.jettech.jettong.common.enumeration.FileType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 文件查询参数
 *
 * <AUTHOR>
 * @version 1.0
 * @description 文件查询参数
 * @projectName jettong
 * @package com.jettech.jettong.base.vo.fail.param
 * @className FileParamVO
 * @date 2021/9/15 9:43
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "FileParamVO", description = "文件查询参数")
public class FileParamVO implements Serializable
{

    private static final long serialVersionUID = 1L;

    /**
     * 业务类型
     */
    @ApiModelProperty(value = "业务类型")
    private String bizType;

    /**
     * 文件类型
     */
    @ApiModelProperty(value = "文件类型")
    private FileType fileType;

    /**
     * 存储类型
     * LOCAL
     */
    @ApiModelProperty(value = "存储类型")
    private FileStorageType storageType;

    /**
     * 桶
     */
    @ApiModelProperty(value = "桶")
    private String bucket;

    /**
     * 文件相对地址
     */
    @ApiModelProperty(value = "文件相对地址")
    private String path;

    /**
     * 唯一文件名
     */
    @ApiModelProperty(value = "唯一文件名")
    private String uniqueFileName;

    /**
     * 文件md5
     */
    @ApiModelProperty(value = "文件md5")
    private String fileMd5;

    /**
     * 原始文件名
     */
    @ApiModelProperty(value = "原始文件名")
    private String originalFileName;

    /**
     * 文件类型
     */
    @ApiModelProperty(value = "文件类型")
    private String contentType;

    /**
     * 后缀
     */
    @ApiModelProperty(value = "后缀")
    private String suffix;

    /**
     * 业务id
     */
    @ApiModelProperty(value = "业务id")
    private Long bizId;

}
