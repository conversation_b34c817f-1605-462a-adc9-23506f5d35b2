package com.jettech.jettong.base.entity.msg;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jettech.basic.annotation.echo.Echo;
import com.jettech.basic.base.entity.Entity;
import com.jettech.basic.model.EchoVO;
import com.jettech.jettong.base.entity.msg.template.MsgTemplate;
import com.jettech.jettong.common.event.enumeration.MsgEngineType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

import static com.baomidou.mybatisplus.annotation.SqlCondition.LIKE;


/**
 * 消息通知方案信息实体类
 * <AUTHOR>
 * @version 1.0
 * @description 消息通知方案信息实体类
 * @projectName jettong
 * @package com.jettech.jettong.base.entity.msg
 * @className MsgScheme
 * @date 2023-05-31
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("sys_msg_scheme")
@ApiModel(value = "MsgScheme", description = "消息通知方案信息")
@AllArgsConstructor
public class MsgScheme extends Entity<Long> implements EchoVO
{

    private static final long serialVersionUID = 1L;
    @TableField(exist = false)
    private Map<String, Object> echoMap = new HashMap<>();
    /**
     * 消息通知方案类型枚举，支持平台级，项目级，流水线、代码库
     */
    @ApiModelProperty(value = "消息通知方案类型枚举，支持平台级，项目级，流水线、代码库")
    @NotEmpty(message = "请填写消息通知方案类型枚举，支持平台级，项目级，流水线、代码库")
    @Size(max = 50, message = "消息通知方案类型枚举，支持平台级，项目级，流水线、代码库长度不能超过50")
    @TableField(value = "`type`", condition = LIKE)
    private String type;

    /**
     * 方案名称
     */
    @ApiModelProperty(value = "方案名称")
    @NotEmpty(message = "请填写方案名称")
    @Size(max = 200, message = "方案名称长度不能超过200")
    @TableField(value = "`name`", condition = LIKE)
    private String name;

    /**
     * 方案描述
     */
    @ApiModelProperty(value = "方案描述")
    @Size(max = 500, message = "方案描述长度不能超过500")
    @TableField(value = "`description`", condition = LIKE)
    private String description;

    /**
     * 启用状态
     */
    @ApiModelProperty(value = "启用状态")
    @NotNull(message = "请填写启用状态")
    @TableField(value = "`state`")
    private Boolean state;

    /**
     * 是否内置方案
     */
    @ApiModelProperty(value = "是否内置方案")
    @NotNull(message = "请填写是否内置方案")
    @TableField(value = "`readonly`")
    private Boolean readonly;

    /**
     * 消息通知引擎类型枚举
     */
    @ApiModelProperty(value = "消息通知引擎类型枚举")
    @NotEmpty(message = "请填写消息通知引擎类型枚举")
    @Size(max = 50, message = "消息通知引擎类型枚举长度不能超过50")
    @TableField(value = "`msg_engine_type`", condition = LIKE)
    private MsgEngineType msgEngineType;

    /**
     * 外键，消息通知事件id
     */
    @ApiModelProperty(value = "外键，消息通知事件id")
    @NotNull(message = "请填写外键，消息通知事件id")
    @TableField(value = "`event_id`")
    @Echo(api = "msgEventService", beanClass = MsgEvent.class)
    private Long eventId;

    /**
     * 外键，消息通知模板id
     */
    @ApiModelProperty(value = "外键，消息通知模板id")
    @NotNull(message = "请填写外键，消息通知模板id")
    @TableField(value = "`template_id`")
    @Echo(api = "msgTemplateService", beanClass = MsgTemplate.class)
    private Long templateId;

    /**
     * 外键，消息接收人规则id
     */
    @ApiModelProperty(value = "外键，消息通知模板id")
    @NotNull(message = "请填写外键，消息通知模板id")
    @TableField(value = "`receive_id`")
    @Echo(api = "msgReceiveService", beanClass = MsgReceive.class)
    private Long receiveId;

    /**
     * 是否定时通知
     */
    @ApiModelProperty(value = "是否定时通知")
    @NotNull(message = "请填写是否定时通知")
    @TableField(value = "`scheduled`")
    private Boolean scheduled;

    /**
     * 定时通知cron表达式
     */
    @ApiModelProperty(value = "定时通知cron表达式")
    @Size(max = 1000, message = "定时通知cron表达式长度不能超过1000")
    @TableField(value = "`cron`", condition = LIKE)
    private String cron;

    @Builder
    public MsgScheme(Long id, LocalDateTime createTime, Long createdBy, LocalDateTime updateTime, Long updatedBy, 
                    String type, String name, String description, Boolean state, Boolean readonly, Long receiveId,
            MsgEngineType msgEngineType, Long eventId, Long templateId, Boolean scheduled, String cron)
    {
        this.id = id;
        this.createTime = createTime;
        this.createdBy = createdBy;
        this.updateTime = updateTime;
        this.updatedBy = updatedBy;
        this.type = type;
        this.name = name;
        this.description = description;
        this.state = state;
        this.readonly = readonly;
        this.msgEngineType = msgEngineType;
        this.eventId = eventId;
        this.templateId = templateId;
        this.receiveId = receiveId;
        this.scheduled = scheduled;
        this.cron = cron;
    }

}
