package com.jettech.jettong.base.dto.rbac.user;

import com.jettech.jettong.base.entity.rbac.user.UserRole;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * 用户新增对象
 *
 * <AUTHOR>
 * @version 1.0
 * @description 用户新增对象
 * @projectName jettong
 * @package com.jettech.jettong.base.dto.rbac.user
 * @className UserSaveDTO
 * @date 2021/9/15 9:43
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "UserSaveDTO", description = "用户")
public class UserSaveDTO implements Serializable
{

    private static final long serialVersionUID = 1L;

    /**
     * 账号
     */
    @ApiModelProperty(value = "账号")
    @NotEmpty(message = "账号不能为空")
    @Size(max = 200, message = "账号长度不能超过200")
    private String account;

    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名")
    @NotEmpty(message = "姓名不能为空")
    @Size(max = 100, message = "姓名长度不能超过100")
    private String name;

    /**
     * 组织
     * #sys_org
     *
     * @Echo(api = ORG_ID_CLASS,  beanClass = Org.class)
     */
    @ApiModelProperty(value = "组织")
    private Long orgId;

    /**
     * 集团工号
     */
    @ApiModelProperty(value = "集团工号")
    @Size(max = 18, message = "集团工号长度不能超过18")
    private String idCard;

    /**
     * 邮箱
     */
    @ApiModelProperty(value = "邮箱")
    @Size(max = 255, message = "邮箱长度不能超过255")
    private String email;

    /**
     * 手机
     */
    @ApiModelProperty(value = "手机")
    @Size(max = 20, message = "手机长度不能超过20")
    private String mobile;

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    private Boolean state;

    /**
     * 是否内置头像
     */
    @ApiModelProperty(value = "是否内置头像")
    private Boolean avatarType;

    /**
     * 头像
     */
    @ApiModelProperty(value = "头像")
    private Long avatar;

    /**
     * 头像地址
     */
    @ApiModelProperty(value = "头像地址")
    @Size(max = 1000, message = "头像地址长度不能超过1000")
    private String avatarPath;

    /**
     * 工作描述
     */
    @ApiModelProperty(value = "描述")
    @Size(max = 200, message = "描述长度不能超过255")
    private String description;

    /**
     * 密码
     */
    @ApiModelProperty(value = "密码")
    @Size(min = 6, max = 64, message = "密码长度不能小于6或超过64")
    private String password;

    /**
     * 用户角色信息
     */
    @ApiModelProperty(value = "用户角色信息")
    private List<UserRole> userRoles;

    @ApiModelProperty(value = "类型")
    @NotEmpty(message = "类型不能为空")
    private String type;

    @ApiModelProperty(value = "是否报工")
    private Boolean reportWork;
}
