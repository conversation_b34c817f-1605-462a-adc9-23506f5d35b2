package com.jettech.jettong.base.entity.rbac.team;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jettech.basic.annotation.echo.Echo;
import com.jettech.basic.base.entity.TreeEntity;
import com.jettech.basic.model.EchoVO;
import com.jettech.jettong.base.entity.rbac.org.Org;
import com.jettech.jettong.base.entity.rbac.user.User;
import com.jettech.jettong.common.constant.DictionaryBaseType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

import static com.baomidou.mybatisplus.annotation.SqlCondition.LIKE;
import static com.jettech.jettong.common.constant.BaseEchoConstants.*;


/**
 * 团队信息表实体类
 * <AUTHOR>
 * @version 1.0
 * @description 团队信息表实体类
 * @projectName jettong
 * @package com.jettech.jettong.base.base.entity
 * @className SysTeam
 * @date 2023-03-13
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("sys_team")
@ApiModel(value = "SysTeam", description = "团队信息表")
@AllArgsConstructor
public class SysTeam extends TreeEntity<SysTeam, Long> implements EchoVO
{

    private static final long serialVersionUID = 1L;
    @TableField(exist = false)
    private Map<String, Object> echoMap = new HashMap<>();
    /**
     * 团队名称
     */
    @ApiModelProperty(value = "团队名称")
    @NotEmpty(message = "请填写团队名称")
    @Size(max = 100, message = "团队名称长度不能超过100")
    @TableField(value = "`name`", condition = LIKE)
    private String name;

    /**
     * 团队描述
     */
    @ApiModelProperty(value = "团队描述")
    @Size(max = 500, message = "团队描述长度不能超过500")
    @TableField(value = "`description`")
    private String description;

    /**
     * 父级团队
     */
    @ApiModelProperty(value = "父级团队")
    @TableField(value = "`parent_id`")
    @Echo(api = TEAM_SERVICE_ID_CLASS, beanClass = SysTeam.class)
    private Long parentId;

    /**
     * 负责人
     */
    @ApiModelProperty(value = "负责人")
    @NotNull(message = "请填写负责人")
    @TableField(value = "`leader_by`")
    @Echo(api = USER_ID_CLASS, beanClass = User.class)
    private Long leaderBy;

    /**
     * 所属机构ID
     */
    @ApiModelProperty(value = "所属机构ID")
    @TableField(value = "`org_id`")
    @Echo(api = ORG_ID_CLASS, beanClass = Org.class)
    private Long orgId;

    /**
     * 团队类型
     */
    @ApiModelProperty(value = "团队类型")
    @NotEmpty(message = "请填写团队类型")
    @Size(max = 20, message = "团队类型长度不能超过20")
    @Echo(api = DICTIONARY_ITEM_CLASS, dictType = DictionaryBaseType.TEAM_TYPE)
    @TableField(value = "`type`")
    private String type;


    @Builder
    public SysTeam(Long id, LocalDateTime createTime, Long createdBy, 
                    String name, String description, Long parentId, Long leaderBy, Long orgId, 
                    String type, LocalDateTime updateTime, Long updatedBy)
    {
        this.id = id;
        this.createTime = createTime;
        this.createdBy = createdBy;
        this.name = name;
        this.description = description;
        this.parentId = parentId;
        this.leaderBy = leaderBy;
        this.orgId = orgId;
        this.type = type;
        this.updateTime = updateTime;
        this.updatedBy = updatedBy;
    }

}
