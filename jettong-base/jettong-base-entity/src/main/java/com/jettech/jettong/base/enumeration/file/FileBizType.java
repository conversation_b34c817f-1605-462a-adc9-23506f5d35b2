package com.jettech.jettong.base.enumeration.file;

import com.jettech.basic.base.BaseEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.stream.Stream;

/**
 * 文件上传业务类型枚举
 *
 * <AUTHOR>
 * @version 1.0
 * @description 文件上传业务类型枚举
 * @projectName jettong-enterprises
 * @package com.jettech.jettong.base.enumeration.file
 * @className BizType
 * @date 2021/11/8 15:13
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "FileBizType", description = "文件上传业务类型")
public enum FileBizType implements BaseEnum
{
    /**
     * 用户头像上传
     */
    USER_AVATAR_UPLOAD("用户头像上传"),
    /**
     * 流水线脚本上传
     */
    PIPELINE_SCRIPT_UPLOAD("脚本上传"),
    /**
     * 构建参数文件上传
     */
    CMDB_APPLICATION_BUILD_FILE_UPLOAD("构建参数文件上传"),
    /**
     * 制品包上传
     */
    PACK_UPLOAD("制品包上传"),
    /**
     * 用户需求附件上传
     */
    IDEA_FILE_UPLOAD("用户需求附件上传"),
    /**
     * 项目附件上传
     */
    PROJECT_FILE_UPLOAD("项目附件上传"),
    /**
     * 需求附件上传
     */
    ISSUE_FILE_UPLOAD("需求附件上传"),

    /**
     * 测试需求附件上传
     */
    TESTREQ_FILE_UPLOAD("测试需求附件上传"),

    /**
     * 任务附件上传
     */
    TASK_FILE_UPLOAD("任务附件上传"),
    /**
     * 缺陷附件上传
     */
    BUG_FILE_UPLOAD("缺陷附件上传"),
    /**
     * 测试管理下测试用例附件上传
     */
    TEST_PRODUCT_CASE_FILE_UPLOAD("测试管理下测试用例附件上传"),
    /**
     * 测试管理下测试用例意图附件上传
     */
    TEST_PRODUCT_CASE_INTENT_FILE_UPLOAD("测试管理下测试用例意图附件上传"),
    /**
     * 测试管理下测试用例前置条件附件上传
     */
    TEST_PRODUCT_CASE_PREREQUISITE_FILE_UPLOAD("测试管理下测试用例前置条件附件上传"),
    /**
     * 测试管理下测试用例测试步骤附件上传
     */
    TEST_PRODUCT_CASE_TESTSTEP_FILE_UPLOAD("测试管理下测试用例测试步骤附件上传"),
    /**
     * 测试管理下历史测试用例附件上传
     */
    TEST_PRODUCT_CASE_HISTORY_FILE_UPLOAD("测试管理下历史测试用例附件上传"),
    /**
     * 风险附件上传
     */
    RISK_FILE_UPLOAD("风险附件上传"),

    /**
     * 评论附件上传
     */
    DETECTION_FILE_UPLOAD("评论附件上传"),

    /**
     * VSMP价值流背景图片上传
     */
    VSMP_FILE_UPLOAD("价值流背景图片上传"),

    /**
     * 发布计划附件上传
     */
    RELEASE_PIAN_FILE_UPLOAD("发布计划附件上传"),

    /**
     * 发布版本附件上传
     */
    RELEASE_VERSION_FILE_UPLOAD("发布版本附件上传"),

    /**
     * 发布辅办工单附件上传
     */
    RELEASE_WORKOEDER_PEODUCT_FILE_UPLOAD("发布辅办工单附件上传"),

    /**
     * 公告附件上传
     */
    NOTICE_FILE_UPLOAD("公告附件上传"),

    /**
     * 产品集资料库上传
     */
    PRODUCTSET_DOCUMENTS_UPLOAD("产品集资料库上传"),

    /**
     * 执行用例附件上传
     */
    EXCUTPLANCASE_FILE_UPLOAD("执行用例附件上传"),

    /**
     * 公告附件上传
     */
    PRODUCT_DOCUMENTS_UPLOAD("公告附件上传");

    @ApiModelProperty(value = "描述")
    private String desc;

    /**
     * 根据当前枚举的name匹配
     */
    public static FileBizType match(String val, FileBizType def)
    {
        return Stream.of(values()).parallel().filter(item -> item.name().equalsIgnoreCase(val)).findAny().orElse(def);
    }

    public static FileBizType get(String val)
    {
        return match(val, null);
    }

    public boolean eq(FileBizType val)
    {
        return val != null && eq(val.name());
    }

    @Override
    @ApiModelProperty(value = "数据字典类型",
            allowableValues = "USER_AVATAR_UPLOAD,PIPELINE_SCRIPT_UPLOAD,PACK_UPLOAD,IDEA_FILE_UPLOAD," +
                    "ISSUE_FILE_UPLOAD,PROJECT_FILE_UPLOAD,TESTREQ_FILE_UPLOAD,TASK_FILE_UPLOAD,BUG_FILE_UPLOAD,RISK_FILE_UPLOAD,VSMP_FILE_UPLOAD",
            example = "USER_AVATAR_UPLOAD")
    public String getCode()
    {
        return this.name();
    }

}
