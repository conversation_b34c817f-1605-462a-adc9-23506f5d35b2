package com.jettech.jettong.base.dto.rbac.tenant;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * 平台配置信息（如：logo，名称，版权信息等）新增实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 平台配置信息（如：logo，名称，版权信息等）新增实体类
 * @projectName jettong
 * @package com.jettech.jettong.base.dto
 * @className TenantPlatformConfigSaveDTO
 * @date 2021-11-20
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "TenantPlatformConfigSaveDTO", description = "平台配置信息（如：logo，名称，版权信息等）")
public class TenantPlatformConfigSaveDTO implements Serializable
{

    private static final long serialVersionUID = 1L;

    /**
     * 配置名称，例如：logo，平台名称等，主要用于明确配置的信息
     */
    @ApiModelProperty(value = "配置名称，例如：logo，平台名称等，主要用于明确配置的信息")
    @NotNull(message = "请输入配置名称")
    @Size(max = 50, message = "配置名称长度不能超过50")
    private String name;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    @NotNull(message = "请输入配置code")
    @Size(max = 20, message = "code长度不能超过20")
    private String code;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    @Size(max = 200, message = "描述长度不能超过200")
    private String description;

    /**
     * 配置存储信息
     */
    @ApiModelProperty(value = "配置存储信息")
    private String value;

    /**
     * 内置，内置数据不让删除，可以修改
     */
    @ApiModelProperty(value = "内置，内置数据不让删除，可以修改")
    private Boolean readonly;

}
