package com.jettech.jettong.base.dto.rbac.org;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 组织机构分页查询对象
 *
 * <AUTHOR>
 * @version 1.0
 * @description 组织机构分页查询对象
 * @projectName jettong
 * @package com.jettech.jettong.base.dto.rbac.org
 * @className OrgPageQuery
 * @date 2021/9/15 9:43
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "OrgPageQuery", description = "组织")
public class OrgPageQuery implements Serializable
{

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "机构编码")
    private String code;

    @ApiModelProperty(value = "名称")
    private String name;

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    private Boolean state;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    private String description;

    @ApiModelProperty(value = "父ID")
    private Long parentId;

    @ApiModelProperty(value = "排序号")
    private Integer sort;

    /**
     * 负责人
     */
    @ApiModelProperty(value = "负责人")
    private Long leadingBy;

    /**
     * 类型
     */
    @ApiModelProperty(value = "类型")
    private String type;

}
