package com.jettech.jettong.base.vo.rbac.org;

import com.baomidou.mybatisplus.annotation.TableField;
import com.jettech.basic.base.entity.TreeEntity;
import com.jettech.basic.model.EchoVO;
import io.swagger.annotations.ApiModel;
import lombok.*;
import lombok.experimental.Accessors;

import java.util.HashMap;
import java.util.Map;

/**
 * 组织机构及用户
 *
 * <AUTHOR>
 * @version 1.0
 * @projectname jettong-base-cloud
 * 2023/2/14 16:07
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "OrgUserVO", description = "机构用户展示实体类")
public class OrgUserVO extends TreeEntity<OrgUserVO, Long> implements EchoVO
{

    private static final long serialVersionUID = 1L;
    @TableField(exist = false)
    private Map<String, Object> echoMap = new HashMap<>();
    private Long id;
    private String name;
    private Long parentId;

    private Integer sort;

    private String type;
    private String avatarPath;
    private Long userId;
}
