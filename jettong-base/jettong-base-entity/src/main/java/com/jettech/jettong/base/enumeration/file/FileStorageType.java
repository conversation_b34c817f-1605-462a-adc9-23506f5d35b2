package com.jettech.jettong.base.enumeration.file;

import com.jettech.basic.base.BaseEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.stream.Stream;

/**
 * 文件存储类型-枚举
 *
 * <AUTHOR>
 * @version 1.0
 * @description 文件存储类型-枚举
 * @projectName jettong
 * @package com.jettech.jettong.base.enumeration.file
 * @className FileStorageType
 * @date 2021/9/15 9:43
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "FileStorageType", description = "文件存储类型")
public enum FileStorageType implements BaseEnum
{
    /**
     * 本地
     */
    LOCAL("本地"),
    /**
     * minIO
     */
    MIN_IO("MinIO"),
    /**
     * 华为云OSS
     */
    HUAWEI_OSS("华为云OSS"),
    /**
     * S3
     */
    S3("S3"),
    ;

    @ApiModelProperty(value = "描述")
    private String desc;

    /**
     * 根据当前枚举的name匹配
     */
    public static FileStorageType match(String val, FileStorageType def)
    {
        return Stream.of(values()).parallel().filter(item -> item.name().equalsIgnoreCase(val)).findAny().orElse(def);
    }

    public static FileStorageType get(String val)
    {
        return match(val, null);
    }

    @Override
    @ApiModelProperty(value = "编码", allowableValues = "LOCAL,FAST_DFS,MIN_IO,ALI,QINIU", example = "LOCAL")
    public String getCode()
    {
        return this.name();
    }


    public boolean eq(FileStorageType type)
    {
        return type != null && this.name().equals(type.name());
    }
}
