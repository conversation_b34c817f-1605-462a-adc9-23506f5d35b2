package com.jettech.jettong.base.dto.sys.dictionary;

import com.jettech.jettong.base.entity.sys.dictionary.DictionaryExtended;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * 数据字典信息表新增实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 数据字典信息表新增实体类
 * @projectName jettong
 * @package com.jettech.jettong.base.dto
 * @className DictionarySaveDTO
 * @date 2021-10-15
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "DictionarySaveDTO", description = "数据字典信息表")
public class DictionarySaveDTO implements Serializable
{

    private static final long serialVersionUID = 1L;

    /**
     * 类型
     */
    @ApiModelProperty(value = "类型")
    @NotEmpty(message = "请填写类型")
    @Size(max = 255, message = "类型长度不能超过255")
    private String type;

    /**
     * 类型标签
     */
    @ApiModelProperty(value = "类型标签")
    @NotEmpty(message = "请填写类型标签")
    @Size(max = 255, message = "类型标签长度不能超过255")
    private String label;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    @NotEmpty(message = "请填写编码")
    @Size(max = 64, message = "编码长度不能超过64")
    private String code;

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    @NotEmpty(message = "请填写名称")
    @Size(max = 64, message = "名称长度不能超过64")
    private String name;
    @ApiModelProperty(value = "所属平台")
    protected String platform;
    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    private Boolean state;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    @Size(max = 255, message = "描述长度不能超过255")
    private String description;

    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    private Integer sort;

    /**
     * 内置
     */
    @ApiModelProperty(value = "内置")
    private Boolean readonly;

    /**
     * 父级字典id
     */
    @ApiModelProperty(value = "父级字典id")
    private Long parentId;

    /**
     * 数据字典扩展字段
     */
    @ApiModelProperty(value = "数据字典扩展字段")
    private List<DictionaryExtended> dictionaryExtendeds;
}
