package com.jettech.jettong.base.entity.msg.engine;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jettech.basic.annotation.echo.Echo;
import com.jettech.basic.base.entity.SuperEntity;
import com.jettech.basic.model.EchoVO;
import com.jettech.jettong.base.entity.rbac.user.User;
import com.jettech.jettong.common.event.enumeration.MsgEngineType;
import com.jettech.jettong.common.constant.BaseEchoConstants;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 消息引擎信息实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 消息引擎信息实体类
 * @projectName jettong
 * @package com.jettech.jettong.base.entity.msg.engine
 * @className MsgEngine
 * @date 2023-5-16
 * @copyright 2023 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@TableName("sys_msg_engine")
@ApiModel(value = "MsgEngine", description = "消息引擎信息")
@AllArgsConstructor
public class MsgEngine implements EchoVO, Serializable
{

    private static final long serialVersionUID = 1L;

    @TableField(exist = false)
    private Map<String, Object> echoMap = new HashMap<>();

    @TableId(value = "`id`", type = IdType.INPUT)
    @ApiModelProperty(value = "主键")
    @NotNull(message = "id不能为空", groups = SuperEntity.Update.class)
    protected Long id;

    /**
     * 引擎名称
     */
    @ApiModelProperty(value = "引擎名称")
    @Size(max = 100, message = "引擎名称长度不能超过100")
    @TableField(value = "`name`")
    private String name;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    @Size(max = 500, message = "描述长度不能超过500")
    @TableField(value = "`description`")
    private String description;

    /**
     * 连接状态
     */
    @ApiModelProperty(value = "连接状态")
    @TableField(value = "`state`")
    private Boolean state;

    /**
     * 启用状态
     */
    @ApiModelProperty(value = "启用状态")
    @TableField(value = "`status`")
    private Boolean status;

    /**
     * 是否只读
     */
    @ApiModelProperty(value = "是否只读")
    @TableField(value = "`readonly`")
    private Boolean readonly;

    /**
     * 引擎分类
     */
    @ApiModelProperty(value = "引擎分类")
    @NotNull(message = "请选择引擎分类")
    @TableField(value = "`type`")
    private MsgEngineType type;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "`create_time`", fill = FieldFill.INSERT)
    protected LocalDateTime createTime;

    @ApiModelProperty(value = "创建人ID")
    @TableField(value = "`created_by`", fill = FieldFill.INSERT)
    @Echo(api = BaseEchoConstants.USER_ID_CLASS, beanClass = User.class)
    protected Long createdBy;

    @ApiModelProperty(value = "最后修改时间")
    @TableField(value = "`update_time`", fill = FieldFill.INSERT_UPDATE)
    protected LocalDateTime updateTime;

    @ApiModelProperty(value = "最后修改人ID")
    @TableField(value = "`updated_by`", fill = FieldFill.INSERT_UPDATE)
    @Echo(api = BaseEchoConstants.USER_ID_CLASS, beanClass = User.class)
    protected Long updatedBy;

    /**
     * 引擎扩展属性
     */
    @ApiModelProperty(value = "引擎扩展属性")
    @TableField(exist = false)
    private List<MsgEngineExtended> engineExtendeds;

    /**
     * 维护人
     */
    @ApiModelProperty(value = "引擎维护人")
    @TableField(exist = false)
    private List<MsgEngineMaintainer> engineMaintainers;

    @Builder
    public MsgEngine(String name, String description, Boolean state, Boolean status, Boolean readonly, MsgEngineType type,
            List<MsgEngineExtended> engineExtendeds, List<MsgEngineMaintainer> engineMaintainers) {
        this.name = name;
        this.description = description;
        this.state = state;
        this.status = status;
        this.readonly = readonly;
        this.type = type;
        this.engineExtendeds = engineExtendeds;
        this.engineMaintainers = engineMaintainers;
    }
}
