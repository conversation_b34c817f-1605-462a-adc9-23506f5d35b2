package com.jettech.jettong.base.dto.msg;

import com.jettech.jettong.base.entity.msg.MsgEvent;
import com.jettech.jettong.common.event.enumeration.MsgEngineType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * 消息通知模板信息分页实体类
 * <AUTHOR>
 * @version 1.0
 * @description 消息通知模板信息分页实体类
 * @projectName jettong
 * @package com.jettech.jettong.base.base.dto.msg
 * @className MsgTemplate
 * @date 2023-05-24
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "MsgTemplatePageQuery", description = "消息通知模板信息")
public class MsgTemplatePageQuery implements Serializable
{

    private static final long serialVersionUID = 1L;

    /**
     * 模板名称
     */
    @ApiModelProperty(value = "模板名称")
    private String name;
    /**
     * 模板描述
     */
    @ApiModelProperty(value = "模板描述")
    private String description;
    /**
     * 是否内置
     */
    @ApiModelProperty(value = "是否内置")
    private Boolean readonly;
    /**
     * 模板标题
     */
    @ApiModelProperty(value = "模板标题")
    private String title;
    /**
     * 模板内容
     */
    @ApiModelProperty(value = "模板内容")
    private String body;

    /**
     * 关联引擎类型
     */
    @ApiModelProperty(value = "关联引擎类型")
    private List<MsgEngineType> typeList;

    /**
     * 关联事件
     */
    @ApiModelProperty(value = "关联事件")
    private List<MsgEvent> eventList;

}
