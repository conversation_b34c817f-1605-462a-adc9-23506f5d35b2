package com.jettech.jettong.base.domain.file;

import com.jettech.jettong.base.enumeration.file.FileStorageType;
import lombok.Builder;
import lombok.Data;

/**
 * 文件删除
 *
 * <AUTHOR>
 * @date 2019/05/07
 */

/**
 * 文件删除业务对象
 *
 * <AUTHOR>
 * @version 1.0
 * @description 文件删除业务对象
 * @projectName jettong
 * @package com.jettech.jettong.base.domain.file
 * @className FileDeleteBO
 * @date 2021/9/15 9:43
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@Builder
public class FileDeleteBO
{
    /**
     * 桶
     */
    private String bucket;

    /**
     * 相对路径
     */
    private String path;

    /**
     * 存储类型
     */
    private FileStorageType storageType;
}
