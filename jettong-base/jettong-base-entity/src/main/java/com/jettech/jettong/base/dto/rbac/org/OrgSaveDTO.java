package com.jettech.jettong.base.dto.rbac.org;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * 组织机构新增对象
 *
 * <AUTHOR>
 * @version 1.0
 * @description 组织机构新增实体类
 * @projectName jettong
 * @package com.jettech.jettong.base.dto.rbac.org
 * @className OrgSaveDTO
 * @date 2021/9/15 9:43
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "OrgSaveDTO", description = "组织")
public class OrgSaveDTO implements Serializable
{

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "编码")
    @NotEmpty(message = "编码不能为空")
    @Size(max = 50, message = "编码长度不能超过50")
    private String code;

    @ApiModelProperty(value = "名称")
    @NotEmpty(message = "名称不能为空")
    @Size(max = 100, message = "名称长度不能超过100")
    private String name;

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    private Boolean state;

    @ApiModelProperty(value = "父ID")
    private Long parentId;

    @ApiModelProperty(value = "父编码")
    @Size(max = 50, message = "父编码长度不能超过50")
    private String parentCode;

    @ApiModelProperty(value = "排序号")
    private Integer sort;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    @Size(max = 200, message = "描述长度不能超过200")
    private String description;

    /**
     * logo
     */
    @ApiModelProperty(value = "logo")
    @Size(max = 5 * 1024 * 1024, message = "图片大小不超过5MB")
    @TableField(value = "logo")
    private String logo;

    /**
     * 负责人
     */
    @ApiModelProperty(value = "负责人")
    private Long leadingBy;

    /**
     * 类型
     */
    @ApiModelProperty(value = "类型")
    @NotEmpty(message = "类型不能为空")
    private String type;
}
