package com.jettech.jettong.base.entity.file;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jettech.basic.annotation.echo.Echo;
import com.jettech.basic.base.entity.SuperEntity;
import com.jettech.basic.model.EchoVO;
import com.jettech.jettong.base.entity.rbac.user.User;
import com.jettech.jettong.base.enumeration.file.FileBizType;
import com.jettech.jettong.base.enumeration.file.FileStorageType;
import com.jettech.jettong.common.enumeration.FileType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

import static com.jettech.jettong.common.constant.BaseEchoConstants.USER_ID_CLASS;

/**
 * 上传文件实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 上传文件实体类
 * @projectName jettong
 * @package com.jettech.jettong.base.entity.file
 * @className File
 * @date 2021/9/15 9:43
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@TableName("sys_file")
@ApiModel(value = "File", description = "附件信息")
@AllArgsConstructor
public class File implements EchoVO, Serializable
{

    private static final long serialVersionUID = 1L;
    @TableField(exist = false)
    private Map<String, Object> echoMap = new HashMap<>();

    @TableId(value = "`id`", type = IdType.INPUT)
    @ApiModelProperty(value = "主键")
    @NotNull(message = "id不能为空", groups = SuperEntity.Update.class)
    protected Long id;
    /**
     * 业务类型
     */
    @ApiModelProperty(value = "业务类型")
    @TableField(value = "`biz_type`")
    private FileBizType bizType;

    /**
     * 业务类型
     */
    @ApiModelProperty(value = "业务id")
    @TableField(value = "`biz_id`")
    private Long bizId;

    /**
     * 文件类型
     */
    @ApiModelProperty(value = "文件类型")
    @TableField(value = "`file_type`")
    private FileType fileType;

    /**
     * 存储类型
     * LOCAL
     */
    @ApiModelProperty(value = "存储类型")
    @TableField(value = "`storage_type`")
    private FileStorageType storageType;

    /**
     * 桶
     */
    @ApiModelProperty(value = "桶")
    @TableField(value = "`bucket`")
    private String bucket;

    /**
     * 文件相对地址
     */
    @ApiModelProperty(value = "文件相对地址")
    @TableField(value = "`path`")
    private String path;

    /**
     * 唯一文件名
     */
    @ApiModelProperty(value = "唯一文件名")
    @TableField(value = "`unique_file_name`")
    private String uniqueFileName;

    /**
     * 文件md5
     */
    @ApiModelProperty(value = "文件md5")
    @TableField(value = "`file_md5`")
    private String fileMd5;

    /**
     * 原始文件名
     */
    @ApiModelProperty(value = "原始文件名")
    @TableField(value = "`original_file_name`")
    private String originalFileName;

    /**
     * 文件类型
     */
    @ApiModelProperty(value = "文件类型")
    @TableField(value = "`content_type`")
    private String contentType;

    /**
     * 后缀
     */
    @ApiModelProperty(value = "后缀")
    @TableField(value = "`suffix`")
    private String suffix;

    /**
     * 大小
     */
    @ApiModelProperty(value = "大小")
    @TableField(value = "`size`")
    private Long size;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "`create_time`", fill = FieldFill.INSERT)
    protected LocalDateTime createTime;

    @ApiModelProperty(value = "创建人ID")
    @TableField(value = "`created_by`", fill = FieldFill.INSERT)
    @Echo(api = USER_ID_CLASS, beanClass = User.class)
    protected Long createdBy;

    @ApiModelProperty(value = "最后修改时间")
    @TableField(value = "`update_time`", fill = FieldFill.INSERT_UPDATE)
    protected LocalDateTime updateTime;

    @ApiModelProperty(value = "最后修改人ID")
    @TableField(value = "`updated_by`", fill = FieldFill.INSERT_UPDATE)
    protected Long updatedBy;

    @Builder
    public File(Long id, LocalDateTime createTime, Long createdBy, LocalDateTime updateTime, Long updatedBy,
            FileBizType bizType, Long bizId, FileType fileType, FileStorageType storageType, String bucket,
            String path, String uniqueFileName, String fileMd5, String originalFileName, String contentType,
            String suffix, Long size)
    {
        this.id = id;
        this.createTime = createTime;
        this.createdBy = createdBy;
        this.updateTime = updateTime;
        this.updatedBy = updatedBy;
        this.bizType = bizType;
        this.bizId = bizId;
        this.fileType = fileType;
        this.storageType = storageType;
        this.bucket = bucket;
        this.path = path;
        this.uniqueFileName = uniqueFileName;
        this.fileMd5 = fileMd5;
        this.originalFileName = originalFileName;
        this.contentType = contentType;
        this.suffix = suffix;
        this.size = size;
    }

}
