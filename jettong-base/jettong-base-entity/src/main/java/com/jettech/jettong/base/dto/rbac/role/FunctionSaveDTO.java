package com.jettech.jettong.base.dto.rbac.role;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * 菜单功能新增对象
 *
 * <AUTHOR>
 * @version 1.0
 * @description 菜单功能新增对象
 * @projectName jettong
 * @package com.jettech.jettong.base.dto.rbac.role
 * @className FunctionSaveDTO
 * @date 2021/9/15 9:43
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "FunctionSaveDTO", description = "资源")
public class FunctionSaveDTO implements Serializable
{

    private static final long serialVersionUID = 1L;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    @Size(max = 500, message = "编码长度不能超过500")
    private String code;

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    @NotEmpty(message = "名称不能为空")
    @Size(max = 100, message = "名称长度不能超过100")
    private String name;
    /**
     * 菜单ID
     * #sys_menu
     */
    @ApiModelProperty(value = "菜单ID")
    private Long menuId;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    @Size(max = 200, message = "描述长度不能超过200")
    private String description;

}
