package com.jettech.jettong.base.vo.msg.engine;

import com.jettech.jettong.common.enumeration.ExtendType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 引擎扩展属性配置信息
 *
 * <AUTHOR>
 * @version 1.0
 * @description 引擎扩展属性配置信息
 * @projectName jettong
 * @package com.jettech.jettong.base.vo.msg
 * @className MsgEngineExtendAttributesVO
 * @date 2023-5-16
 * @copyright 2023 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "MsgEngineExtendAttributesVO", description = "消息引擎扩展属性信息")
public class MsgEngineExtendAttributesVO implements Serializable
{
    /**
     * 扩展属性key
     */
    @ApiModelProperty(value = "扩展属性key")
    private String key;
    /**
     * 扩展属性名称
     */
    @ApiModelProperty(value = "扩展属性名称")
    private String name;
    /**
     * 扩展属性类型
     */
    @ApiModelProperty(value = "扩展属性类型")
    private ExtendType inputType;
    /**
     * 是否启用正则校验
     */
    @ApiModelProperty(value = "是否启用正则校验")
    private Boolean isCheck;
    /**
     * 正则
     */
    @ApiModelProperty(value = "正则")
    private String regexp;
    /**
     * 最大长度
     */
    @ApiModelProperty(value = "最大长度")
    private Integer maxlength;
    /**
     * 未通过正则提示
     */
    @ApiModelProperty(value = "未通过正则提示")
    private String message;

    /**
     * 是否有版本号
     */
    @ApiModelProperty(value = "是否有版本")
    private Boolean isVersion;
    /**
     * 是否添加更多
     */
    @ApiModelProperty(value = "是否添加多个")
    private Boolean isMore;
    /**
     * 是否必填
     */
    @ApiModelProperty(value = "是否必填")
    private Boolean notEmpty;

    @Override
    public String toString() {
        final StringBuffer sb = new StringBuffer("MsgEngineExtendAttributesVO{");
        sb.append("key='").append(key).append('\'');
        sb.append(", name='").append(name).append('\'');
        sb.append(", inputType=").append(inputType);
        sb.append(", isCheck=").append(isCheck);
        sb.append(", regexp='").append(regexp).append('\'');
        sb.append(", maxlength=").append(maxlength);
        sb.append(", message='").append(message).append('\'');
        sb.append(", isVersion=").append(isVersion);
        sb.append(", isMore=").append(isMore);
        sb.append(", notEmpty=").append(notEmpty);
        sb.append('}');
        return sb.toString();
    }
}
