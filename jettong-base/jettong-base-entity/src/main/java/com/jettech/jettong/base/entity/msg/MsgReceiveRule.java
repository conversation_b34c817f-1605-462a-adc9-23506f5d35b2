package com.jettech.jettong.base.entity.msg;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jettech.basic.model.EchoVO;
import com.jettech.jettong.common.event.enumeration.MsgReceiveRuleType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;


/**
 * 消息通知接收人规则信息实体类
 * <AUTHOR>
 * @version 1.0
 * @description 消息通知接收人规则信息实体类
 * @projectName jettong
 * @package com.jettech.jettong.base.entity.msg
 * @className MsgReceiveRule
 * @date 2023-05-31
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sys_msg_receive_rule")
@ApiModel(value = "MsgReceiveRule", description = "消息通知接收人规则信息")
public class MsgReceiveRule implements Serializable, EchoVO
{

    private static final long serialVersionUID = 1L;
    @TableField(exist = false)
    private Map<String, Object> echoMap = new HashMap<>();

    /**
     * 外键，消息通知接收人id
     */
    @ApiModelProperty(value = "外键，消息通知接收人id")
    @NotNull(message = "请填写外键，消息通知接收人id")
    @TableField(value = "`receive_id`")
    private Long receiveId;

    /**
     * 接收人规则类型枚举
     */
    @ApiModelProperty(value = "接收人规则类型枚举")
    @NotEmpty(message = "请填写接收人规则类型枚举")
    @Size(max = 50, message = "接收人规则类型枚举长度不能超过50")
    @TableField(value = "`rule_type`")
    private MsgReceiveRuleType ruleType;

    /**
     * 接收人id,用户id、平台角色id、项目角色id、机构id
     */
    @ApiModelProperty(value = "接收人id,用户id、平台角色id、项目角色id、机构id、MsgReceiveRuleType的CODE")
    @TableField(value = "`biz_id`")
    private String bizId;

    @Builder
    public MsgReceiveRule(
            Long receiveId, MsgReceiveRuleType ruleType, String bizId)
    {
        this.receiveId = receiveId;
        this.ruleType = ruleType;
        this.bizId = bizId;
    }

}
