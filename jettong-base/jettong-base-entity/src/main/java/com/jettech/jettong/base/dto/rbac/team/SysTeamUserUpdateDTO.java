package com.jettech.jettong.base.dto.rbac.team;

import com.jettech.basic.base.entity.SuperEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 团队成员表修改实体类
 * <AUTHOR>
 * @version 1.0
 * @description 团队成员表修改实体类
 * @projectName jettong
 * @package com.jettech.jettong.base.base.dto
 * @className SysTeamUserUpdateDTO
 * @date 2023-03-13
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "SysTeamUserUpdateDTO", description = "团队成员表")
public class SysTeamUserUpdateDTO implements Serializable
{

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @NotNull(message = "请填写主键", groups = SuperEntity.Update.class)
    private Long id;

    /**
     * 团队名称
     */
    @ApiModelProperty(value = "团队名称")
    @NotNull(message = "请填写团队名称")
    private Long teamId;
    /**
     * 团队描述
     */
    @ApiModelProperty(value = "团队描述")
    @NotNull(message = "请填写团队描述")
    private Long userId;
}
