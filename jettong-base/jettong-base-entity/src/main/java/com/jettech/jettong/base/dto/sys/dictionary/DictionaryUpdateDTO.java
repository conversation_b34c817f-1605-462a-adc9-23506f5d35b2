package com.jettech.jettong.base.dto.sys.dictionary;

import com.jettech.basic.base.entity.SuperEntity;
import com.jettech.jettong.base.entity.sys.dictionary.DictionaryExtended;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * 数据字典信息表修改实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 数据字典信息表修改实体类
 * @projectName jettong
 * @package com.jettech.jettong.base.dto
 * @className DictionaryUpdateDTO
 * @date 2021-10-15
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "DictionaryUpdateDTO", description = "数据字典信息表")
public class DictionaryUpdateDTO implements Serializable
{

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @NotNull(message = "请填写主键", groups = SuperEntity.Update.class)
    private Long id;
    @ApiModelProperty(value = "所属平台")
    protected String platform;
    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    @NotEmpty(message = "请填写名称")
    @Size(max = 64, message = "名称长度不能超过64")
    private String name;

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    private Boolean state;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    @Size(max = 255, message = "描述长度不能超过255")
    private String description;

    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    private Integer sort;

    /**
     * 数据字典扩展字段
     */
    @ApiModelProperty(value = "数据字典扩展字段")
    private List<DictionaryExtended> dictionaryExtendeds;

}
