package com.jettech.jettong.base.dto.msg;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 消息通知接收人信息分页实体类
 * <AUTHOR>
 * @version 1.0
 * @description 消息通知接收人信息分页实体类
 * @projectName jettong
 * @package com.jettech.jettong.base.dto.msg
 * @className MsgReceive
 * @date 2023-05-31
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "MsgReceivePageQuery", description = "消息通知接收人信息")
public class MsgReceivePageQuery implements Serializable
{

    private static final long serialVersionUID = 1L;

    /**
     * 接收人名称
     */
    @ApiModelProperty(value = "接收人名称")
    private String name;
    /**
     * 接收人描述
     */
    @ApiModelProperty(value = "接收人描述")
    private String description;
    /**
     * 是否内置
     */
    @ApiModelProperty(value = "是否内置")
    private Boolean readonly;


}
