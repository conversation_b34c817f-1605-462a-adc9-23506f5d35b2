package com.jettech.jettong.base.dto.rbac.user;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 用户角色新增对象
 *
 * <AUTHOR>
 * @version 1.0
 * @description 用户角色新增对象
 * @projectName jettong
 * @package com.jettech.jettong.base.dto.rbac.user
 * @className UserRoleSaveDTO
 * @date 2021/9/15 9:43
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "UserRoleSaveDTO", description = "角色分配 账号角色绑定")
public class UserRoleSaveDTO implements Serializable
{

    private static final long serialVersionUID = 1L;

    /**
     * 角色ID
     */
    @ApiModelProperty(value = "角色ID")
    @NotNull(message = "请选择角色")
    private Long roleId;

    /**
     * 用户ID
     */
    @ApiModelProperty(value = "用户ID")
    private List<Long> userIdList;

}
