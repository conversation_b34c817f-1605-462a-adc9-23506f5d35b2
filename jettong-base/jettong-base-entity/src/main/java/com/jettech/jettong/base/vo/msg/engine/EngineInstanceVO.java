package com.jettech.jettong.base.vo.msg.engine;

import com.jettech.jettong.common.event.enumeration.MsgEngineType;

import java.util.List;

/**
 * 消息引擎配置ymal解析类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 消息引擎配置ymal解析类
 * @projectName jettong-base-cloud
 * @package com.jettech.jettong.base.vo.msg.engine
 * @className EngineIns
 * @date 2023/5/16 19:28
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
public class EngineInstanceVO {

    private MsgEngineType engineInstance;
    private List<MsgEngineExtendAttributesVO> attributes;

    public MsgEngineType getEngineInstance() {
        return engineInstance;
    }

    public void setEngineInstance(MsgEngineType engineInstance) {
        this.engineInstance = engineInstance;
    }

    public List<MsgEngineExtendAttributesVO> getAttributes() {
        return attributes;
    }

    public void setAttributes(List<MsgEngineExtendAttributesVO> attributes) {
        this.attributes = attributes;
    }

    @Override
    public String toString() {
        final StringBuffer sb = new StringBuffer("EngineInstanceVO{");
        sb.append("engineInstance=").append(engineInstance);
        sb.append(", attributes=").append(attributes);
        sb.append('}');
        return sb.toString();
    }
}
