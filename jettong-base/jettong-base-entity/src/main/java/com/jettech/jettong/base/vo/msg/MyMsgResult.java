package com.jettech.jettong.base.vo.msg;

import com.jettech.basic.annotation.echo.Echo;
import com.jettech.basic.base.entity.Entity;
import com.jettech.basic.model.EchoVO;
import com.jettech.jettong.base.entity.rbac.user.User;
import com.jettech.jettong.common.event.enumeration.MsgType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

import static com.jettech.jettong.common.constant.BaseEchoConstants.USER_ID_CLASS;

/**
 * 我的消息对象
 *
 * <AUTHOR>
 * @version 1.0
 * @description 我的消息对象
 * @projectName jettong
 * @package com.jettech.jettong.base.vo.msg
 * @className MyMsgResult
 * @date 2021/9/15 9:43
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@EqualsAndHashCode
@ApiModel(value = "MyMsgResult", description = "我的消息")
@ToString(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class MyMsgResult extends Entity<Long> implements EchoVO
{

    private Map<String, Object> echoMap = new HashMap<>();

    /**
     * 业务ID
     */
    @ApiModelProperty(value = "业务ID")
    private String bizId;

    /**
     * 业务类型
     */
    @ApiModelProperty(value = "业务类型")
    private String bizType;

    /**
     * 消息类型
     * #MsgType{TO_DO:待办;NOTIFY:通知;NOTICE:公告;EARLY_WARNING:预警;}
     */
    @ApiModelProperty(value = "消息类型")
    private MsgType msgType;

    /**
     * 标题
     */
    @ApiModelProperty(value = "标题")
    private String title;

    /**
     * 内容
     */
    @ApiModelProperty(value = "内容")
    private String content;

    /**
     * 发布人
     */
    @ApiModelProperty(value = "发布人")
    @Echo(api = USER_ID_CLASS, beanClass = User.class)
    private Long authorId;

    /**
     * 接收人ID
     * #sys_user
     */
    @ApiModelProperty(value = "接收人ID")
    private Long userId;

    /**
     * 是否已读
     */
    @ApiModelProperty(value = "是否已读")
    private Boolean isRead;

    /**
     * 读取时间
     */
    @ApiModelProperty(value = "读取时间")
    private LocalDateTime readTime;

}
