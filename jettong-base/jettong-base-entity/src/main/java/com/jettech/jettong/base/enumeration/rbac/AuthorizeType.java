package com.jettech.jettong.base.enumeration.rbac;

import com.jettech.basic.base.BaseEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.stream.Stream;

/**
 * 角色的权限类型枚举 菜单或功能
 *
 * <AUTHOR>
 * @version 1.0
 * @description 角色的权限类型枚举
 * @projectName jettong
 * @package com.jettech.jettong.base.enumeration.rbac
 * @className AuthorizeType
 * @date 2021/9/15 9:43
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "AuthorizeType", description = "权限类型-枚举")
public enum AuthorizeType implements BaseEnum
{

    /**
     * MENU="菜单"
     */
    MENU("菜单"),
    /**
     * FUNCTION="功能"
     */
    FUNCTION("功能"),
    ;

    @ApiModelProperty(value = "描述")
    private String desc;


    /**
     * 根据当前枚举的name匹配
     */
    public static AuthorizeType match(String val, AuthorizeType def)
    {
        return Stream.of(values()).parallel().filter(item -> item.name().equalsIgnoreCase(val)).findAny().orElse(def);
    }

    public static AuthorizeType get(String val)
    {
        return match(val, null);
    }

    public boolean eq(AuthorizeType val)
    {
        return val != null && eq(val.name());
    }

    @Override
    @ApiModelProperty(value = "编码", allowableValues = "MENU,FUNCTION", example = "MENU")
    public String getCode()
    {
        return this.name();
    }

}
