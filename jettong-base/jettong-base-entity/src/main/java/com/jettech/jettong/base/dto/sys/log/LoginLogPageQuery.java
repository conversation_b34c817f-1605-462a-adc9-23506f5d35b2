package com.jettech.jettong.base.dto.sys.log;

import com.jettech.jettong.common.dto.PeriodQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 登录日志分页查询对象
 *
 * <AUTHOR>
 * @version 1.0
 * @description 登录日志分页查询对象
 * @projectName jettong
 * @package com.jettech.jettong.base.dto.sys.log
 * @className LoginLogPageQuery
 * @date 2021/9/15 9:43
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "LoginLogPageQuery", description = "登录日志")
public class LoginLogPageQuery implements Serializable
{

    private static final long serialVersionUID = 1L;

    /**
     * 登录IP
     */
    @ApiModelProperty(value = "登录IP，模糊查询")
    private String requestIp;

    /**
     * 登录人姓名
     */
    @ApiModelProperty(value = "登录人姓名，模糊查询")
    private String userName;

    /**
     * 登录人账号
     */
    @ApiModelProperty(value = "登录人账号，模糊查询")
    private String account;

    /**
     * 登录日期 筛选时间段
     */
    @ApiModelProperty(value = "登录日期 筛选时间段")
    private PeriodQuery<LocalDateTime> loginDate;

}
