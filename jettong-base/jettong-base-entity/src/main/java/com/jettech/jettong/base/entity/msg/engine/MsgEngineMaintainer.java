package com.jettech.jettong.base.entity.msg.engine;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jettech.basic.annotation.echo.Echo;
import com.jettech.basic.model.EchoVO;
import com.jettech.jettong.base.entity.rbac.user.User;
import com.jettech.jettong.common.constant.BaseEchoConstants;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

/**
 * 消息引擎维护人信息实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 消息引擎维护人信息实体类
 * @projectName jettong
 * @package com.jettech.jettong.base.entity.msg.engine
 * @className MsgEngine
 * @date 2023-5-16
 * @copyright 2023 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@TableName("sys_msg_engine_maintainer")
@ApiModel(value = "MsgEngineMaintainer", description = "消息引擎维护人信息")
@AllArgsConstructor
public class MsgEngineMaintainer implements EchoVO, Serializable
{

    private static final long serialVersionUID = 1L;

    @TableField(exist = false)
    private Map<String, Object> echoMap = new HashMap<>();
    /**
     * 用户id
     */
    @ApiModelProperty(value = "用户id")
    @NotNull(message = "请填写用户id")
    @TableField(value = "`user_id`")
    @Echo(api = BaseEchoConstants.USER_ID_CLASS, beanClass = User.class)
    private Long userId;

    /**
     * 引擎id
     */
    @ApiModelProperty(value = "引擎id")
    @NotNull(message = "请填写引擎id")
    @TableField(value = "`engine_id`")
    private Long engineId;

    @Builder
    public MsgEngineMaintainer(Long userId, Long engineId)
    {
        this.userId = userId;
        this.engineId = engineId;
    }

}
