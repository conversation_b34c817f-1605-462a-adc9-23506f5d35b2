package com.jettech.jettong.base.dto.sys.log;

import com.jettech.jettong.base.entity.sys.log.OptLog;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;


/**
 * 操作日志对象，包含详情
 *
 * <AUTHOR>
 * @version 1.0
 * @description 操作日志对象，包含详情
 * @projectName jettong
 * @package com.jettech.jettong.base.dto.sys.log
 * @className OptLogResult
 * @date 2021/9/15 9:43
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "OptLogResult", description = "系统日志扩展")
public class OptLogResult extends OptLog implements Serializable
{

    private static final long serialVersionUID = 1L;

    /**
     * 请求参数
     */
    @ApiModelProperty(value = "请求参数")
    private String params;

    /**
     * 返回值
     */
    @ApiModelProperty(value = "返回值")
    private String result;

    /**
     * 异常描述
     */
    @ApiModelProperty(value = "异常描述")
    private String exDetail;

}
