package com.jettech.jettong.base.dto.rbac.user;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;

/**
 * 登录参数对象
 *
 * <AUTHOR>
 * @version 1.0
 * @description 登录参数对象
 * @projectName jettong
 * @package com.jettech.jettong.base.dto.rbac.user
 * @className LoginParamDTO
 * @date 2021/9/15 9:43
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "LoginParamDTO", description = "登录参数")
public class LoginParamDTO
{
    @ApiModelProperty(value = "验证码KEY")
    private String key;

    @ApiModelProperty(value = "验证码,wx方式，该参数为微信返回code,oa方式该参数为oa系统返回的token")
    private String code;

    @ApiModelProperty(value = "账号,oa方式，该参数为OA系统返回的IDnumber")
    private String account;

    @ApiModelProperty(value = "密码")
    private String password;

    /**
     * password: 账号密码
     * refresh_token: 刷新token
     * captcha: 验证码
     */
    @ApiModelProperty(value = "授权类型", example = "captcha", allowableValues = "captcha,refresh_token,password,wx,oa")
    @NotEmpty(message = "授权类型不能为空")
    private String grantType;

    /**
     * 前端界面点击清空缓存时调用
     */
    @ApiModelProperty(value = "刷新token")
    private String refreshToken;

}
