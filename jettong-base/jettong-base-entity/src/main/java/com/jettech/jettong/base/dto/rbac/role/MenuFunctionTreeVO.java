package com.jettech.jettong.base.dto.rbac.role;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.jettech.basic.base.entity.TreeEntity;
import com.jettech.jettong.base.enumeration.rbac.AuthorizeType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.Size;

import static com.baomidou.mybatisplus.annotation.SqlCondition.LIKE;

/**
 * 菜单功能树对象
 *
 * <AUTHOR>
 * @version 1.0
 * @description 菜单功能树对象
 * @projectName jettong
 * @package com.jettech.jettong.base.dto.rbac.role
 * @className MenuFunctionTreeVO
 * @date 2021/9/15 9:43
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@ToString(callSuper = true)
public class MenuFunctionTreeVO extends TreeEntity<MenuFunctionTreeVO, Long>
{
    private AuthorizeType type;

    private String code;

    private String icon;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    @Size(max = 200, message = "描述长度不能超过200")
    @TableField(value = "description", condition = LIKE)
    @Excel(name = "描述")
    private String description;

}
