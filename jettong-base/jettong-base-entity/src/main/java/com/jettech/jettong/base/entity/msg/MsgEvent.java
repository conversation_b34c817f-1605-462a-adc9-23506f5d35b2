package com.jettech.jettong.base.entity.msg;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jettech.basic.base.entity.Entity;
import com.jettech.basic.model.EchoVO;
import com.jettech.jettong.common.event.enumeration.MsgEventType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.util.HashMap;
import java.util.Map;

import static com.baomidou.mybatisplus.annotation.SqlCondition.LIKE;


/**
 * 消息通知事件信息实体类
 * <AUTHOR>
 * @version 1.0
 * @description 消息通知事件信息实体类
 * @projectName jettong
 * @package com.jettech.jettong.base.base.entity.msg
 * @className MsgEvent
 * @date 2023-05-22
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("sys_msg_event")
@ApiModel(value = "MsgEvent", description = "消息通知事件信息")
@AllArgsConstructor
public class MsgEvent extends Entity<Long> implements EchoVO
{

    private static final long serialVersionUID = 1L;
    @TableField(exist = false)
    private Map<String, Object> echoMap = new HashMap<>();
    /**
     * 编号
     */
    @ApiModelProperty(value = "编号")
    @NotEmpty(message = "请填写编号")
    @Size(max = 50, message = "编号长度不能超过50")
    @TableField(value = "`code`", condition = LIKE)
    private String code;

    /**
     * 事件类型枚举,项目、流水线、代码库
     */
    @ApiModelProperty(value = "事件类型枚举,项目、流水线、代码库")
    @NotEmpty(message = "请填写事件类型枚举,项目、流水线、代码库")
    @Size(max = 50, message = "事件类型枚举,项目、流水线、代码库长度不能超过50")
    @TableField(value = "`type`")
    private MsgEventType type;

    /**
     * 事件名称
     */
    @ApiModelProperty(value = "事件名称")
    @NotEmpty(message = "请填写事件名称")
    @Size(max = 200, message = "事件名称长度不能超过200")
    @TableField(value = "`name`", condition = LIKE)
    private String name;

    /**
     * 事件描述
     */
    @ApiModelProperty(value = "事件描述")
    @Size(max = 1000, message = "事件描述长度不能超过1000")
    @TableField(value = "`description`", condition = LIKE)
    private String description;

    /**
     * 启用状态
     */
    @ApiModelProperty(value = "启用状态")
    @TableField(value = "`status`")
    private Boolean status;

    @Builder
    public MsgEvent(String code, MsgEventType type, String name, String description, Boolean status)
    {
        this.code = code;
        this.type = type;
        this.name = name;
        this.description = description;
        this.status = status;
    }

}
