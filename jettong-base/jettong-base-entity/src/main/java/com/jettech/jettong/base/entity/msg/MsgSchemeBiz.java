package com.jettech.jettong.base.entity.msg;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jettech.basic.model.EchoVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

import static com.baomidou.mybatisplus.annotation.SqlCondition.LIKE;


/**
 * 消息通知方案业务实例信息实体类
 * <AUTHOR>
 * @version 1.0
 * @description 消息通知方案业务实例信息实体类
 * @projectName jettong
 * @package com.jettech.jettong.base.entity.msg
 * @className MsgSchemeBiz
 * @date 2023-05-31
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sys_msg_scheme_biz")
@ApiModel(value = "MsgSchemeBiz", description = "消息通知方案业务实例信息")
public class MsgSchemeBiz implements Serializable, EchoVO
{

    private static final long serialVersionUID = 1L;
    @TableField(exist = false)
    private Map<String, Object> echoMap = new HashMap<>();
    /**
     * 外键，方案id
     */
    @ApiModelProperty(value = "外键，方案id")
    @NotNull(message = "请填写外键，方案id")
    @TableField(value = "`scheme_id`")
    private Long schemeId;

    /**
     * 业务类型，项目、流水线、代码库
     */
    @ApiModelProperty(value = "业务类型，项目、流水线、代码库")
    @NotEmpty(message = "请填写业务类型，项目、流水线、代码库")
    @Size(max = 10, message = "业务类型，项目、流水线、代码库长度不能超过10")
    @TableField(value = "`biz_type`", condition = LIKE)
    private String bizType;

    /**
     * 消息通知方案状态
     */
    @ApiModelProperty(value = "消息通知方案状态")
    @NotNull(message = "请填写消息通知方案状态")
    @TableField(value = "`state`")
    private Boolean state;

    /**
     * 业务id,项目id、流水线id、代码库id
     */
    @ApiModelProperty(value = "业务id,项目id、流水线id、代码库id")
    @NotNull(message = "请填写业务id,项目id、流水线id、代码库id")
    @TableField(value = "`biz_id`")
    private Long bizId;

    @Builder
    public MsgSchemeBiz(
            Long schemeId, String bizType, Boolean state, Long bizId)
    {
        this.schemeId = schemeId;
        this.bizType = bizType;
        this.state = state;
        this.bizId = bizId;
    }

}
