package com.jettech.jettong.base.entity.rbac.tenant;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jettech.basic.base.entity.Entity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.Size;
import java.time.LocalDateTime;


/**
 * 平台配置信息（如：logo，名称，版权信息等）实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 平台配置信息（如：logo，名称，版权信息等）实体类
 * @projectName jettong
 * @package com.jettech.jettong.base.entity.rbac.tenant
 * @className TenantPlatformConfig
 * @date 2021-11-20
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("sys_tenant_platform_config")
@ApiModel(value = "TenantPlatformConfig", description = "平台配置信息（如：logo，名称，版权信息等）")
@AllArgsConstructor
public class TenantPlatformConfig extends Entity<Long>
{

    private static final long serialVersionUID = 1L;

    /**
     * 配置名称，例如：logo，平台名称等，主要用于明确配置的信息
     */
    @ApiModelProperty(value = "配置名称，例如：logo，平台名称等，主要用于明确配置的信息")
    @Size(max = 50, message = "配置名称，例如：logo，平台名称等，主要用于明确配置的信息长度不能超过50")
    @TableField(value = "`name`")
    @Excel(name = "配置名称，例如：logo，平台名称等，主要用于明确配置的信息")
    private String name;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    @Size(max = 20, message = "编码长度不能超过20")
    @TableField(value = "`code`")
    @Excel(name = "编码")
    private String code;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    @Size(max = 200, message = "描述长度不能超过200")
    @TableField(value = "`description`")
    @Excel(name = "描述")
    private String description;

    /**
     * 配置存储信息
     */
    @ApiModelProperty(value = "配置存储信息")

    @TableField(value = "`value`")
    @Excel(name = "配置存储信息")
    private String value;

    /**
     * 内置，内置数据不让删除，可以修改
     */
    @ApiModelProperty(value = "内置，内置数据不让删除，可以修改")
    @TableField(value = "`readonly`")
    @Excel(name = "内置，内置数据不让删除，可以修改", replace = {"是_true", "否_false", "_null"})
    private Boolean readonly;

    @Builder
    public TenantPlatformConfig(Long id, LocalDateTime createTime, Long createdBy, LocalDateTime updateTime,
            Long updatedBy,
            String name, String code, String description, String value, Boolean readonly)
    {
        this.id = id;
        this.createTime = createTime;
        this.createdBy = createdBy;
        this.updateTime = updateTime;
        this.updatedBy = updatedBy;
        this.name = name;
        this.code = code;
        this.description = description;
        this.value = value;
        this.readonly = readonly;
    }

}
