package com.jettech.jettong.base.entity.msg;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jettech.basic.annotation.echo.Echo;
import com.jettech.basic.base.entity.Entity;
import com.jettech.basic.model.EchoVO;
import com.jettech.jettong.base.entity.file.File;
import com.jettech.jettong.base.entity.rbac.user.User;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.baomidou.mybatisplus.annotation.SqlCondition.LIKE;
import static com.jettech.jettong.common.constant.BaseEchoConstants.USER_ID_CLASS;


/**
 * 通知公告信息实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 通知公告信息实体类
 * @projectName jettong
 * @package com.jettech.jettong.base.entity.msg
 * @className Notice
 * @date 2023-05-15
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("sys_notice")
@ApiModel(value = "Notice", description = "通知公告信息")
@AllArgsConstructor
public class Notice extends Entity<Long> implements EchoVO
{

    private static final long serialVersionUID = 1L;
    @TableField(exist = false)
    private Map<String, Object> echoMap = new HashMap<>();
    /**
     * 编号
     */
    @ApiModelProperty(value = "编号")
    @Size(max = 100, message = "编号长度不能超过100")
    @TableField(value = "`code`", condition = LIKE)
    private String code;

    /**
     * 公告标题
     */
    @ApiModelProperty(value = "公告标题")
    @NotEmpty(message = "请填写公告标题")
    @Size(max = 500, message = "公告标题长度不能超过500")
    @TableField(value = "`title`", condition = LIKE)
    private String title;

    /**
     * 主题分类字典
     */
    @ApiModelProperty(value = "主题分类字典")
    @Size(max = 50, message = "主题分类字典长度不能超过50")
    @TableField(value = "`notice_type`", condition = LIKE)
    private String noticeType;

    /**
     * 公告内容
     */
    @ApiModelProperty(value = "公告内容")
    @Size(max = 65535, message = "公告内容长度不能超过65535")
    @TableField(value = "`body`", condition = LIKE)
    private String body;

    /**
     * 状态，0-暂存，1-已发布，2-已撤销
     */
    @ApiModelProperty(value = "状态，0-暂存，1-已发布，2-已撤销")
    @NotNull(message = "请填写状态，0-暂存，1-已发布，2-已撤销")
    @TableField(value = "`state`")
    private Integer state;

    /**
     * 联系人
     */
    @ApiModelProperty(value = "联系人")
    @TableField(value = "`liaisoner`")
    @Echo(api = USER_ID_CLASS, beanClass = User.class)
    private Long liaisoner;

    /**
     * 联系电话
     */
    @ApiModelProperty(value = "联系电话")
    @Size(max = 50, message = "联系电话长度不能超过50")
    @TableField(value = "`phone`", condition = LIKE)
    private String phone;

    /**
     * 附件
     */
    @ApiModelProperty(value = "附件")
    @TableField(exist = false)
    private List<File> files;

    @ApiModelProperty(value = "创建人ID")
    @TableField(value = "`created_by`", fill = FieldFill.INSERT)
    @Echo(api = USER_ID_CLASS, beanClass = User.class)
    protected Long createdBy;

    @ApiModelProperty(value = "最后修改人ID")
    @Echo(api = USER_ID_CLASS, beanClass = User.class)
    @TableField(value = "`updated_by`", fill = FieldFill.INSERT_UPDATE)
    protected Long updatedBy;

    @Builder
    public Notice(Long id, LocalDateTime createTime, Long createdBy, LocalDateTime updateTime, Long updatedBy,
                    String code, String title, String noticeType, String body, Integer state,
                    Long liaisoner, String phone)
    {
        this.id = id;
        this.createTime = createTime;
        this.createdBy = createdBy;
        this.updateTime = updateTime;
        this.updatedBy = updatedBy;
        this.code = code;
        this.title = title;
        this.noticeType = noticeType;
        this.body = body;
        this.state = state;
        this.liaisoner = liaisoner;
        this.phone = phone;
    }

}
