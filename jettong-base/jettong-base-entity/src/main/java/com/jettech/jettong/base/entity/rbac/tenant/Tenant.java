package com.jettech.jettong.base.entity.rbac.tenant;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jettech.basic.base.entity.Entity;
import com.jettech.jettong.base.enumeration.rbac.TenantStatus;
import com.jettech.jettong.base.enumeration.rbac.TenantType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;

import static com.jettech.basic.utils.DateUtils.DEFAULT_DATE_TIME_FORMAT;

/**
 * 租户信息实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 租户信息实体类
 * @projectName jettong
 * @package com.jettech.jettong.base.entity.rbac.tenant
 * @className Function
 * @date 2021/10/14 9:43
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("sys_tenant")
@ApiModel(value = "Tenant", description = "租户信息")
@AllArgsConstructor
public class Tenant extends Entity<Long>
{

    private static final long serialVersionUID = 1L;

    /**
     * 租户编码
     */
    @ApiModelProperty(value = "租户编码")
    @NotEmpty(message = "租户编码不能为空")
    @Size(max = 20, message = "租户编码长度不能超过20")
    @TableField(value = "code")
    @Excel(name = "租户编码", width = 20)
    private String code;

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    @Size(max = 200, message = "名称长度不能超过200")
    @TableField(value = "name")
    @Excel(name = "名称", width = 20)
    private String name;

    /**
     * 类型
     * #{CREATE:创建;REGISTER:注册}
     */
    @ApiModelProperty(value = "类型")
    @TableField("`type`")
    @Excel(name = "类型", width = 20, replace = {"创建_CREATE", "注册_REGISTER", "_null"})
    private TenantType type;

    /**
     * 状态
     * #{NORMAL:正常;WAIT_INIT:待初始化;FORBIDDEN:禁用;WAITING:待审核;REFUSE:拒绝;DELETE:已删除}
     */
    @ApiModelProperty(value = "状态")
    @TableField("`status`")
    @Excel(name = "状态", width = 20,
            replace = {"正常_NORMAL", "待初始化_WAIT_INIT", "禁用_FORBIDDEN", "待审核_WAITING", "拒绝_REFUSE", "已删除_DELETE",
                    "_null"})
    private TenantStatus status;

    /**
     * 内置
     */
    @ApiModelProperty(value = "内置")
    @TableField("`readonly`")
    @Excel(name = "内置", replace = {"是_true", "否_false", "_null"})
    private Boolean readonly;

    /**
     * 责任人
     */
    @ApiModelProperty(value = "责任人")
    @Size(max = 50, message = "责任人长度不能超过50")
    @TableField(value = "`duty`")
    @Excel(name = "责任人")
    private String duty;

    /**
     * 有效期
     * 为空表示永久
     */
    @ApiModelProperty(value = "有效期")
    @TableField(value = "`expiration_time`", updateStrategy = FieldStrategy.IGNORED)
    @Excel(name = "有效期", format = DEFAULT_DATE_TIME_FORMAT, width = 20)
    private LocalDateTime expirationTime;

    /**
     * 租户简介
     */
    @ApiModelProperty(value = "描述")
    @Size(max = 500, message = "描述长度不能超过500")
    @TableField(value = "description")
    @Excel(name = "描述", width = 20)
    private String description;


    @Builder
    public Tenant(Long id, LocalDateTime createTime, Long createdBy, LocalDateTime updateTime, Long updatedBy,
            String code, String name, TenantType type, TenantStatus status,
            Boolean readonly, String duty, LocalDateTime expirationTime, String logo, String description)
    {
        this.id = id;
        this.createTime = createTime;
        this.createdBy = createdBy;
        this.updateTime = updateTime;
        this.updatedBy = updatedBy;
        this.code = code;
        this.name = name;
        this.type = type;
        this.status = status;
        this.readonly = readonly;
        this.duty = duty;
        this.expirationTime = expirationTime;
        this.description = description;
    }

}
