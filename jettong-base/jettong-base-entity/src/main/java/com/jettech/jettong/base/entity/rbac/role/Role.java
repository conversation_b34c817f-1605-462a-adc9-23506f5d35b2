package com.jettech.jettong.base.entity.rbac.role;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jettech.basic.annotation.echo.Echo;
import com.jettech.basic.base.entity.Entity;
import com.jettech.basic.database.mybatis.auth.DataScopeType;
import com.jettech.basic.model.EchoVO;
import com.jettech.jettong.base.entity.rbac.org.Org;
import com.jettech.jettong.base.entity.rbac.user.User;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

import static com.baomidou.mybatisplus.annotation.SqlCondition.LIKE;
import static com.jettech.jettong.common.constant.BaseEchoConstants.ORG_ID_CLASS;
import static com.jettech.jettong.common.constant.BaseEchoConstants.USER_ID_CLASS;

/**
 * 角色实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 角色实体类
 * @projectName jettong
 * @package com.jettech.jettong.base.entity.rbac.role
 * @className Role
 * @date 2021/10/14 9:43
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("sys_role")
@ApiModel(value = "Role", description = "角色")
@AllArgsConstructor
public class Role extends Entity<Long> implements EchoVO
{

    private static final long serialVersionUID = 1L;
    @TableField(exist = false)
    private Map<String, Object> echoMap = new HashMap<>();

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    @NotEmpty(message = "名称不能为空")
    @Size(max = 30, message = "名称长度不能超过30")
    @TableField(value = "name", condition = LIKE)
    @Excel(name = "名称")
    private String name;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    @Size(max = 20, message = "编码长度不能超过20")
    @TableField(value = "code", condition = LIKE)
    @Excel(name = "编码")
    private String code;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    @Size(max = 100, message = "描述长度不能超过100")
    @TableField(value = "description", condition = LIKE)
    @Excel(name = "描述")
    private String description;

    /**
     * 所属机构
     */
    @ApiModelProperty(value = "所属机构")
    @TableField(value = "org_id")
    @Excel(name = "所属机构")
    @Echo(api = ORG_ID_CLASS, beanClass = Org.class)
    private Long orgId;

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    @TableField("state")
    @Excel(name = "状态", replace = {"是_true", "否_false", "_null"})
    private Boolean state;

    /**
     * 内置角色
     */
    @ApiModelProperty(value = "内置角色")
    @TableField("readonly")
    @Excel(name = "内置角色", replace = {"是_true", "否_false", "_null"})
    private Boolean readonly;

    /**
     * 数据权限
     * #DataScopeType{ALL:1,全部;THIS_LEVEL:2,本级;THIS_LEVEL_CHILDREN:3,本级以及子级;CUSTOMIZE:4,自定义;SELF:5,个人;}
     */
    @ApiModelProperty(value = "数据权限")
    @TableField("ds_type")
    @Excel(name = "数据权限",
            replace = {"全部_ALL", "本级_THIS_LEVEL", "本级以及子级_THIS_LEVEL_CHILDREN", "自定义_CUSTOMIZE", "个人_SELF", "_null"})
    private DataScopeType dsType;

    @ApiModelProperty(value = "创建人ID")
    @Echo(api = USER_ID_CLASS, beanClass = User.class)
    @TableField(value = "`created_by`", fill = FieldFill.INSERT)
    protected Long createdBy;

    @ApiModelProperty(value = "最后修改人ID")
    @Echo(api = USER_ID_CLASS, beanClass = User.class)
    @TableField(value = "`updated_by`", fill = FieldFill.INSERT_UPDATE)
    protected Long updatedBy;


    @Builder
    public Role(Long id, Long createdBy, LocalDateTime createTime, Long updatedBy, LocalDateTime updateTime,
            String name, String code, String description, Boolean state, Boolean readonly, DataScopeType dsType,
            Long orgId)
    {
        this.id = id;
        this.createdBy = createdBy;
        this.createTime = createTime;
        this.updatedBy = updatedBy;
        this.updateTime = updateTime;
        this.name = name;
        this.code = code;
        this.description = description;
        this.orgId = orgId;
        this.state = state;
        this.readonly = readonly;
        this.dsType = dsType;
    }

}
