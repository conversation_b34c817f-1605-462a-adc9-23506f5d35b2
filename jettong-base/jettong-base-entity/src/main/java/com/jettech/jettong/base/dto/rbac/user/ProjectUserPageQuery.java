package com.jettech.jettong.base.dto.rbac.user;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * 用户分页查询对象
 *
 * <AUTHOR>
 * @version 1.0
 * @description 用户对象
 * @projectName jettong
 * @package com.jettech.jettong.base.dto.rbac.user
 * @className UserPageQuery
 * @date 2021/9/15 9:43
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "ProjectUserPageQuery", description = "用户")
public class ProjectUserPageQuery implements Serializable
{

    private static final long serialVersionUID = 1L;

    /**
     * 角色
     */
    @ApiModelProperty(value = "角色")
    private List<Long> roleIds;

    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名")
    private String name;

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    private Long projectId;

}
