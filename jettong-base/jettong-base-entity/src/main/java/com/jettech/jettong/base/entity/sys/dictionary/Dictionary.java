package com.jettech.jettong.base.entity.sys.dictionary;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jettech.basic.base.entity.Entity;
import com.jettech.basic.model.EchoVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import static com.baomidou.mybatisplus.annotation.SqlCondition.LIKE;


/**
 * 数据字典信息表实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 数据字典信息表实体类
 * @projectName jettong
 * @package com.jettech.jettong.base.entity.sys.dictionary
 * @className Dictionary
 * @date 2021-10-15
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("sys_dictionary")
@ApiModel(value = "Dictionary", description = "数据字典信息表")
@AllArgsConstructor
public class Dictionary extends Entity<Long> implements EchoVO
{

    private static final long serialVersionUID = 1L;
    @TableField(exist = false)
    private Map<String, Object> echoMap = new HashMap<>();

    /**
     * 类型
     */
    @ApiModelProperty(value = "类型")
    @NotEmpty(message = "请填写类型")
    @Size(max = 255, message = "类型长度不能超过255")
    @TableField(value = "`type`")
    private String type;

    /**
     * 类型标签
     */
    @ApiModelProperty(value = "类型标签")
    @NotEmpty(message = "请填写类型标签")
    @Size(max = 255, message = "类型标签长度不能超过255")
    @TableField(value = "`label`")
    private String label;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    @NotEmpty(message = "请填写编码")
    @Size(max = 64, message = "编码长度不能超过64")
    @TableField(value = "`code`")
    private String code;

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    @NotEmpty(message = "请填写名称")
    @Size(max = 64, message = "名称长度不能超过64")
    @TableField(value = "`name`")
    private String name;

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    @TableField(value = "`state`")
    private Boolean state;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    @Size(max = 255, message = "描述长度不能超过255")
    @TableField(value = "`description`")
    private String description;

    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    @TableField(value = "`sort`")
    private Integer sort;

    /**
     * 内置
     */
    @ApiModelProperty(value = "内置")
    @TableField(value = "`readonly`")
    private Boolean readonly;
    @ApiModelProperty(value = "外部平台-所属平台")
    @Size(max = 200, message = "外部平台-所属平台不能超过200")
    @TableField(value = "platform", condition = LIKE)
    @Excel(name = "外部平台-所属平台")
    private String platform;
    /**
     * 父级字典id
     */
    @ApiModelProperty(value = "父级字典id")
    @TableField(value = "`parent_id`")
    private Long parentId;

    /**
     * 父级字典code
     */
    @ApiModelProperty(value = "父级字典code")
    @TableField(exist = false)
    private String parentCode;

    @ApiModelProperty(value = "数据字典扩展信息")
    @TableField(exist = false)
    private List<DictionaryExtended> dictionaryExtendeds = new LinkedList<>();

    @Builder
    public Dictionary(Long id, LocalDateTime createTime, Long updatedBy, LocalDateTime updateTime,
            String type, String label, String code, String name, Boolean state,
            String description, Integer sort, Boolean readonly, Long parentId,
            List<DictionaryExtended> dictionaryExtendeds)
    {
        this.id = id;
        this.createTime = createTime;
        this.updatedBy = updatedBy;
        this.updateTime = updateTime;
        this.type = type;
        this.label = label;
        this.code = code;
        this.name = name;
        this.state = state;
        this.description = description;
        this.sort = sort;
        this.readonly = readonly;
        this.parentId = parentId;
        this.dictionaryExtendeds = dictionaryExtendeds;
    }

}
