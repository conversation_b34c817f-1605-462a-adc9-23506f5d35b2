package com.jettech.jettong.base.entity.rbac.user;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jettech.basic.annotation.echo.Echo;
import com.jettech.basic.model.EchoVO;
import com.jettech.jettong.base.entity.rbac.role.Role;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

import static com.jettech.jettong.common.constant.BaseEchoConstants.ROLE_ID_CLASS;

/**
 * 用户角色实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 用户角色实体类
 * @projectName jettong
 * @package com.jettech.jettong.base.entity.rbac.user
 * @className UserRole
 * @date 2021/10/14 9:43
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@TableName("sys_user_role")
@ApiModel(value = "UserRole", description = "角色分配")
public class UserRole implements EchoVO
{

    private static final long serialVersionUID = 1L;
    @TableField(exist = false)
    private Map<String, Object> echoMap = new HashMap<>();

    /**
     * 角色ID
     * #sys_role
     */
    @ApiModelProperty(value = "角色ID")
    @NotNull(message = "角色ID不能为空")
    @TableField("role_id")
    @Echo(api = ROLE_ID_CLASS, beanClass = Role.class)
    @Excel(name = "角色ID")
    private Long roleId;

    /**
     * 用户ID
     * #sys_user
     */
    @ApiModelProperty(value = "用户ID")
    @NotNull(message = "用户ID不能为空")
    @TableField("user_id")
    @Excel(name = "用户ID")
    private Long userId;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    protected LocalDateTime createTime;

    @ApiModelProperty(value = "创建人ID")
    @TableField(value = "created_by", fill = FieldFill.INSERT)
    protected Long createdBy;

    @Builder
    public UserRole(Long createdBy, LocalDateTime createTime,
            Long roleId, Long userId)
    {
        this.createdBy = createdBy;
        this.createTime = createTime;
        this.roleId = roleId;
        this.userId = userId;
    }

}
