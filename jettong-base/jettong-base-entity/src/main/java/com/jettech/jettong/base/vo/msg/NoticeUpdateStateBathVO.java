package com.jettech.jettong.base.vo.msg;

import com.jettech.jettong.common.event.enumeration.MsgEngineType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 通知批量修改状态VO
 *
 * <AUTHOR>
 * @version 1.0
 * @description 通知批量修改状态VO
 * @projectName jettong-tm
 * @package com.jettech.jettong.base.vo.msg
 * @className NoticeUpdateStateBathVO
 * @date 2023/5/15 15:57
 * @copyright 2023 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@Valid
public class NoticeUpdateStateBathVO {

    /**
     * 通知主键集合
     */
    @ApiModelProperty(value = "通知主键集合")
    @NotEmpty(message = "通知主键集合不能为空")
    private List<Long> ids;

    /**
     * 状态，0-暂存，1-已发布，2-已撤销
     */
    @ApiModelProperty(value = "状态，0-暂存，1-已发布，2-已撤销")
    @NotNull(message = "状态不能为空")
    private Integer state;

    @ApiModelProperty(value = "消息通知引擎类型")
    private MsgEngineType engineType;

    @ApiModelProperty(value = "用户id")
    private Long receiveId;
}
