package com.jettech.jettong.base.dto.msg;

import com.baomidou.mybatisplus.annotation.TableField;
import com.jettech.basic.base.entity.SuperEntity;
import com.jettech.jettong.base.entity.msg.engine.MsgEngineExtended;
import com.jettech.jettong.base.entity.msg.engine.MsgEngineMaintainer;
import com.jettech.jettong.common.event.enumeration.MsgEngineType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * 消息引擎信息实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 消息引擎信息实体类
 * @projectName jettong
 * @package com.jettech.jettong.base.entity.msg.engine
 * @className MsgEngine
 * @date 2023-5-16
 * @copyright 2023 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@ApiModel(value = "MsgEngineUpdateDTO", description = "消息引擎信息")
public class MsgEngineUpdateDTO implements Serializable
{
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @NotNull(message = "id不能为空", groups = SuperEntity.Update.class)
    private Long id;

    /**
     * 引擎名称
     */
    @ApiModelProperty(value = "引擎名称")
    @Size(max = 100, message = "引擎名称长度不能超过100")
    private String name;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    @Size(max = 500, message = "描述长度不能超过500")
    private String description;

    /**
     * 连接状态
     */
    @ApiModelProperty(value = "连接状态")
    private Boolean state;

    /**
     * 启用状态
     */
    @ApiModelProperty(value = "启用状态")
    private Boolean status;

    /**
     * 是否只读
     */
    @ApiModelProperty(value = "是否只读")
    private Boolean readonly;

    /**
     * 引擎分类
     */
    @ApiModelProperty(value = "引擎分类")
    @NotNull(message = "请选择引擎分类")
    private MsgEngineType type;

    /**
     * 引擎扩展属性
     */
    @ApiModelProperty(value = "引擎扩展属性")
    @TableField(exist = false)
    private List<MsgEngineExtended> engineExtendeds;

    /**
     * 维护人
     */
    @ApiModelProperty(value = "引擎维护人")
    @TableField(exist = false)
    private List<MsgEngineMaintainer> engineMaintainers;

    @Builder
    public MsgEngineUpdateDTO(String name, String description, Boolean state, Boolean status, Boolean readonly, MsgEngineType type,
            List<MsgEngineExtended> engineExtendeds, List<MsgEngineMaintainer> engineMaintainers) {
        this.name = name;
        this.description = description;
        this.state = state;
        this.status = status;
        this.readonly = readonly;
        this.type = type;
        this.engineExtendeds = engineExtendeds;
        this.engineMaintainers = engineMaintainers;
    }
}
