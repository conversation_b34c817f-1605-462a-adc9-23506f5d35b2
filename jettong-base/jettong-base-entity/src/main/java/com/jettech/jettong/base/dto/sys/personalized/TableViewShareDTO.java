package com.jettech.jettong.base.dto.sys.personalized;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * 筛选器信息新增实体类
 * <AUTHOR>
 * @version 1.0
 * @description 筛选器信息新增实体类
 * @projectName jettong
 * @package com.jettech.jettong.base.base.dto.sys
 * @className TableViewSaveDTO
 * @date 2023-03-23
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "TableViewSaveDTO", description = "筛选器信息")
public class TableViewShareDTO implements Serializable
{

    private static final long serialVersionUID = 1L;
    /**
     * 被分享的用户ID
     */
    @ApiModelProperty(value = "被分享的用户")
    @NotEmpty
    private List<Long> userIds;

    /**
     * 筛选器ID
     */
    @ApiModelProperty(value = "筛选器列表")
    @NotEmpty
    private List<Long> viewIds;

}
