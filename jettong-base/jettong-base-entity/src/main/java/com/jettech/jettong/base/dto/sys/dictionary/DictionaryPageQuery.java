package com.jettech.jettong.base.dto.sys.dictionary;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

/**
 * 数据字典信息表分页实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 数据字典信息表分页实体类
 * @projectName jettong
 * @package com.jettech.jettong.base.dto.sys.dictionary
 * @className Dictionary
 * @date 2021-10-15
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "DictionaryPageQuery", description = "数据字典信息表")
public class DictionaryPageQuery implements Serializable
{

    private static final long serialVersionUID = 1L;

    /**
     * 类型
     */
    @ApiModelProperty(value = "字典类型，如果参数中包含parentId和parentCoded时，传父级字典的类型且该字段必填")
    @NotEmpty(message = "请传递字典类型")
    private String type;
    @ApiModelProperty(value = "所属平台")
    protected String platform;
    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    private String code;

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    private String name;

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    private Boolean state;

    /**
     * 父级字典id
     */
    @ApiModelProperty(value = "父级字典id,和父级字典code只能同时使用一个")
    private Long parentId;

    /**
     * 父级字典code
     */
    @ApiModelProperty(value = "父级字典code,和父级字典id只能同时使用一个")
    private String parentCode;
}
