package com.jettech.jettong.base.vo.rbac.user;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.handler.inter.IExcelDataModel;
import cn.afterturn.easypoi.handler.inter.IExcelModel;
import io.swagger.annotations.ApiModel;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 用户Excel对象
 *
 * <AUTHOR>
 * @version 1.0
 * @description 用户Excel对象
 * @projectName jettong
 * @package com.jettech.jettong.base.vo.rbac.user
 * @className UserExcelVO
 * @date 2021/9/15 9:43
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "UserExcelVO", description = "用户")
public class UserExcelVO implements Serializable, IExcelModel, IExcelDataModel
{

    private String errorMsg;

    private Integer rowNum;

    /**
     * 姓名
     */
    @Excel(name = "名称*", orderNum = "0")
    private String name;

    /**
     * 账号
     */
    @Excel(name = "登录账号*", orderNum = "1")
    private String account;

    /**
     * 集团工号
     */
    @Excel(name = "集团工号", orderNum = "2")
    private String idCard;

    /**
     * 手机
     */
    @Excel(name = "手机*", orderNum = "3")
    private String mobile;

    /**
     * 邮箱
     */
    @Excel(name = "邮箱*", orderNum = "4")
    private String email;

    /**
     * 状态
     */
    @Excel(name = "状态*", replace = {"正常_true", "禁用_false", "_null"}, orderNum = "5")
    private Boolean state;

    /**
     * 组织
     * #sys_org
     */
    @Excel(name = "所属机构*", dict = "org", orderNum = "6")
    private String orgId;

    /**
     * 类型
     */
    @Excel(name = "类型*", dict = "type", orderNum = "7")
    private String type;
    /**
     * 角色
     */
    @Excel(name = "角色", dict = "role", orderNum = "8")
    private String roleId;

    /**
     * 描述
     */
    @Excel(name = "描述", orderNum = "9")
    private String description;


    /**
     * 获取行号
     *
     * @return Integer
     */
    @Override
    public Integer getRowNum()
    {
        return rowNum;
    }

    /**
     * 设置行号
     *
     * @param rowNum 行号
     */
    @Override
    public void setRowNum(Integer rowNum)
    {
        this.rowNum = rowNum;
    }

    /**
     * 获取错误数据
     *
     * @return String
     */
    @Override
    public String getErrorMsg()
    {
        return errorMsg;
    }

    /**
     * 设置错误信息
     *
     * @param errorMsg 错误信息
     */
    @Override
    public void setErrorMsg(String errorMsg)
    {
        this.errorMsg = errorMsg;
    }

}
