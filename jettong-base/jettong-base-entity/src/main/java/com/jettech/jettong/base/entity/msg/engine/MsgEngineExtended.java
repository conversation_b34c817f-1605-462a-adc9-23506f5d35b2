package com.jettech.jettong.base.entity.msg.engine;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * 消息引擎扩展信息实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 消息引擎扩展信息实体类
 * @projectName jettong
 * @package com.jettech.jettong.base.entity.msg.engine
 * @className MsgEngineExtended
 * @date 2023-5-16
 * @copyright 2023 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@TableName("sys_msg_engine_extended")
@ApiModel(value = "MsgEngineExtended", description = "消息引擎扩展信息表")
public class MsgEngineExtended implements Serializable
{

    private static final long serialVersionUID = 1L;

    /**
     * 引擎id
     */
    @ApiModelProperty(value = "引擎id")
    @NotNull(message = "请填写引擎id")
    @TableField(value = "`engine_id`")
    private Long engineId;

    /**
     * 扩展属性key
     */
    @ApiModelProperty(value = "扩展属性key")
    @Size(max = 255, message = "扩展属性key长度不能超过255")
    @TableField(value = "`key`")
    private String key;

    /**
     * 扩展属性值
     */
    @ApiModelProperty(value = "扩展属性值")
    @Size(max = 255, message = "扩展属性值长度不能超过255")
    @TableField(value = "`value`")
    private String value;


    @Builder
    public MsgEngineExtended(
            Long engineId, String key, String value)
    {
        this.engineId = engineId;
        this.key = key;
        this.value = value;
    }

}
