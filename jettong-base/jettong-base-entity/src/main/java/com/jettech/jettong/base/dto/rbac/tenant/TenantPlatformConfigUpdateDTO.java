package com.jettech.jettong.base.dto.rbac.tenant;

import com.jettech.basic.base.entity.SuperEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * 平台配置信息（如：logo，名称，版权信息等）修改实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 平台配置信息（如：logo，名称，版权信息等）修改实体类
 * @projectName jettong
 * @package com.jettech.jettong.base.dto
 * @className TenantPlatformConfigUpdateDTO
 * @date 2021-11-20
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "TenantPlatformConfigUpdateDTO", description = "平台配置信息（如：logo，名称，版权信息等）")
public class TenantPlatformConfigUpdateDTO implements Serializable
{

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @NotNull(message = "请填写主键", groups = SuperEntity.Update.class)
    private Long id;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    @Size(max = 20, message = "code长度不能超过20")
    private String code;

    /**
     * 配置名称，例如：logo，平台名称等，主要用于明确配置的信息
     */
    @ApiModelProperty(value = "配置名称，例如：logo，平台名称等，主要用于明确配置的信息")
    @Size(max = 50, message = "配置名称，例如：logo，平台名称等，主要用于明确配置的信息长度不能超过50")
    private String name;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    @Size(max = 200, message = "描述长度不能超过200")
    private String description;

    /**
     * 配置存储信息
     */
    @ApiModelProperty(value = "配置存储信息")
    private String value;

}
