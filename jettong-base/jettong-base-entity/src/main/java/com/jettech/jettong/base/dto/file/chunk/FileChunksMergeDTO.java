package com.jettech.jettong.base.dto.file.chunk;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

/**
 * 分片合并DTO
 *
 * <AUTHOR>
 * @version 1.0
 * @description 分片合并DTO
 * @projectName jettong
 * @package com.jettech.jettong.base.dto.msg
 * @className MsgDTO
 * @date 2021/9/15 9:43
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@ToString
@ApiModel(value = "FileChunksMerge", description = "文件合并实体")
public class FileChunksMergeDTO
{

    @ApiModelProperty(value = "文件唯一名 md5.js 生成的, 与后端生成的一致")
    private String name;
    @ApiModelProperty(value = "原始文件名")
    private String submittedFileName;

    @ApiModelProperty(value = "md5", notes = "webuploader 自带的md5算法值， 与后端生成的不一致")
    private String md5;

    @ApiModelProperty(value = "分片总数")
    private Integer chunks;
    @ApiModelProperty(value = "后缀")
    private String ext;
    @ApiModelProperty(value = "文件夹id")
    private Long folderId;

    @ApiModelProperty(value = "大小")
    private Long size;
    @ApiModelProperty(value = "类型")
    private String contextType;
}
