package com.jettech.jettong.base.dto.msg;

import com.jettech.basic.base.entity.SuperEntity;
import com.jettech.jettong.base.entity.msg.MsgReceiveRule;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * 消息通知接收人信息修改实体类
 * <AUTHOR>
 * @version 1.0
 * @description 消息通知接收人信息修改实体类
 * @projectName jettong
 * @package com.jettech.jettong.base.dto.msg
 * @className MsgReceiveUpdateDTO
 * @date 2023-05-31
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "MsgReceiveUpdateDTO", description = "消息通知接收人信息")
public class MsgReceiveUpdateDTO implements Serializable
{

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @NotNull(message = "请填写主键", groups = SuperEntity.Update.class)
    private Long id;

    /**
     * 接收人名称
     */
    @ApiModelProperty(value = "接收人名称")
    @NotEmpty(message = "请填写接收人名称")
    @Size(max = 100, message = "接收人名称长度不能超过100")
    private String name;
    /**
     * 接收人描述
     */
    @ApiModelProperty(value = "接收人描述")
    @Size(max = 500, message = "接收人描述长度不能超过500")
    private String description;
    /**
     * 是否内置
     */
    @ApiModelProperty(value = "是否内置")
    @NotNull(message = "请填写是否内置")
    private Boolean readonly;

    /**
     * 规则列表
     */
    @ApiModelProperty(value = "规则列表")
    private List<MsgReceiveRule> ruleList;
}
