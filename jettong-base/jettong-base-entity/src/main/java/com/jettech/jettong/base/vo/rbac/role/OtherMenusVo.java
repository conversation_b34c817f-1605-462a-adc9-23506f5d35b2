package com.jettech.jettong.base.vo.rbac.role;



import com.jettech.basic.base.entity.TreeEntity;
import com.jettech.jettong.base.entity.rbac.role.Function;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description TODO
 * @projectName jettong
 * @package com.jettech.jettong.base.vo.rbac.role
 * @className OtherMenusVo
 * @date 2025/8/11 16:50
 * @copyright 2021 www.jettech.cn Inc. All rights reserved.
 * @ 注意：本内容仅限于捷科智诚北京有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
public class OtherMenusVo extends TreeEntity<OtherMenusVo, Long>
{


    private String code;
    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    private String description;

    /**
     * 通用菜单
     * True表示无需分配所有人就可以访问的
     */
    @ApiModelProperty(value = "通用菜单")
    private Boolean isGeneral;

    /**
     * 是否隐藏
     */
    @ApiModelProperty(value = "是否隐藏")
    private Boolean isHide;

    /**
     * 是否显示子级
     */
    @ApiModelProperty(value = "是否显示子级")
    private Boolean showChildren;

    /**
     * 是否为按钮
     */
    @ApiModelProperty(value = "是否为按钮")
    private Boolean isButton;

    /**
     * 路径
     */
    @ApiModelProperty(value = "路径")
    private String path;

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    private Boolean state;

    /**
     * 菜单图标
     */
    @ApiModelProperty(value = "菜单图标")
    private String icon;

    /**
     * 组件
     */
    @ApiModelProperty(value = "组件")
    private String component;

    /**
     * 内置
     */
    @ApiModelProperty(value = "内置")
    private Boolean readonly;
    /**
     * 外部平台-所属平台
     */
    @ApiModelProperty(value = "外部平台-所属平台")
    private String platform;

    /**
     * 是否外部菜单
     */
    @ApiModelProperty(value = "是否外部菜单")
    private Boolean isExternal;

    /**
     * 外部菜单url
     */
    @ApiModelProperty(value = "外部菜单url")
    private String externalUrl;

}
