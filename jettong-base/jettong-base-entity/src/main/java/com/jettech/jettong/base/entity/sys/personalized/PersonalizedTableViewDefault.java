package com.jettech.jettong.base.entity.sys.personalized;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 用户列表默认视图信息实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 用户列表默认视图信息实体类
 * @projectName jettong
 * @package com.jettech.jettong.base.entity.sys.personalized
 * @className PersonalizedTableViewDefault
 * @date 2021-11-20
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@TableName("sys_personalized_table_view_default")
@ApiModel(value = "PersonalizedTableViewDefault", description = "用户列表默认视图信息")
public class PersonalizedTableViewDefault implements Serializable
{

    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    protected LocalDateTime createTime;
    @ApiModelProperty(value = "创建人ID")
    @TableField(value = "created_by", fill = FieldFill.INSERT)
    protected Long createdBy;
    /**
     * 视图id
     */
    @ApiModelProperty(value = "视图id")
    @NotNull(message = "请填写视图id")
    @TableField(value = "`table_view_id`")
    private Long tableViewId;
    /**
     * 列表唯一code
     */
    @ApiModelProperty(value = "列表唯一code")
    @NotEmpty(message = "请填写列表唯一code")
    @Size(max = 100, message = "列表唯一code长度不能超过100")
    @TableField(value = "`table_code`")
    private String tableCode;
    /**
     * 用户id
     */
    @ApiModelProperty(value = "用户id")
    @TableField(value = "`user_id`")
    private Long userId;

    @Builder
    public PersonalizedTableViewDefault(Long tableViewId, String tableCode, Long userId, LocalDateTime createTime,
            Long createdBy)
    {
        this.tableViewId = tableViewId;
        this.tableCode = tableCode;
        this.userId = userId;
        this.createTime = createTime;
        this.createdBy = createdBy;
    }
}
