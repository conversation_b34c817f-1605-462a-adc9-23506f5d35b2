package com.jettech.jettong.base.dto.msg;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 通知公告信息分页实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 通知公告信息分页实体类
 * @projectName jettong
 * @package com.jettech.jettong.base.dto.sys.msg
 * @className Notice
 * @date 2023-05-15
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "NoticePageQuery", description = "通知公告信息")
public class NoticePageQuery implements Serializable
{

    private static final long serialVersionUID = 1L;

    /**
     * 编号
     */
    @ApiModelProperty(value = "编号")
    private String code;
    /**
     * 公告标题
     */
    @ApiModelProperty(value = "公告标题")
    private String title;
    /**
     * 主题分类字典
     */
    @ApiModelProperty(value = "主题分类字典")
    private String noticeType;
    /**
     * 公告内容
     */
    @ApiModelProperty(value = "公告内容")
    private String body;
    /**
     * 状态，0-暂存，1-已发布，2-已撤销
     */
    @ApiModelProperty(value = "状态，0-暂存，1-已发布，2-已撤销")
    private Integer state;
    /**
     * 联系人
     */
    @ApiModelProperty(value = "联系人")
    private Long liaisoner;
    /**
     * 联系电话
     */
    @ApiModelProperty(value = "联系电话")
    private String phone;

}
