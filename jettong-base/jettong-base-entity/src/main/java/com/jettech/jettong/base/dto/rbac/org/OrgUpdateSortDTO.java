package com.jettech.jettong.base.dto.rbac.org;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 组织机构修改排序对象
 *
 * <AUTHOR>
 * @version 1.0
 * @description 组织机构修改排序对象
 * @projectName jettong
 * @package com.jettech.jettong.base.dto.rbac.org
 * @className OrgUpdateDTO
 * @date 2021/9/15 9:43
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "OrgUpdateSortDTO", description = "组织机构修改排序对象")
public class OrgUpdateSortDTO implements Serializable
{

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "父级机构id")
    @NotNull(message = "请填写父级机构id")
    private Long parentId;

    @ApiModelProperty(value = "父级机构编码")
//    @NotNull(message = "请填写父级机构编码")
    private String parentCode;

    @ApiModelProperty(value = "要修改的机构id")
    @NotNull(message = "请填写修改机构id")
    private Long orgId;

    @ApiModelProperty(value = "上一个机构id,为null时表示第一个")
    protected Long preOrgId;

}
