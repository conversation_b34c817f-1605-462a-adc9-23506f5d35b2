package com.jettech.jettong.base.dto.rbac.role;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * Vue路由 Meta
 *
 * <AUTHOR>
 * @version 1.0
 * @description Vue路由 Meta
 * @projectName jettong
 * @package com.jettech.jettong.base.dto.rbac.role
 * @className RouterMeta
 * @date 2021/9/15 9:43
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class RouterMeta implements Serializable
{

    private static final long serialVersionUID = 5499925008927195914L;

    @ApiModelProperty(value = "菜单id")
    private Long menuId;

    @ApiModelProperty(value = "父级菜单id")
    private Long parentMenuId;

    @ApiModelProperty(value = "编号")
    private String code;

    @ApiModelProperty(value = "标题")
    private String title;

    @ApiModelProperty(value = "图标")
    private String icon = "";

    @ApiModelProperty(value = "面包屑")
    private Boolean breadcrumb = true;

    @ApiModelProperty(value = "是否忽略KeepAlive缓存")
    private Boolean ignoreKeepAlive;

    @ApiModelProperty(value = "隐藏该路由在面包屑上面的显示")
    private Boolean hideBreadcrumb = false;

    @ApiModelProperty(value = "当前路由不再菜单显示")
    private Boolean hideMenu = false;

    @ApiModelProperty(value = "是否显示子级")
    private Boolean showChildren = false;

    @ApiModelProperty(value = "是否为按钮")
    private Boolean isButton = false;

    @ApiModelProperty(value = "是否外部菜单")
    private Boolean isExternal;

    @ApiModelProperty(value = "外部菜单url")
    private String externalUrl;
}
