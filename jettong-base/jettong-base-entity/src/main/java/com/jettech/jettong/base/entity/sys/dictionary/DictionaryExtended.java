package com.jettech.jettong.base.entity.sys.dictionary;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

import static com.baomidou.mybatisplus.annotation.SqlCondition.LIKE;


/**
 * 数据字典扩展信息表实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 数据字典扩展信息表实体类
 * @projectName jettong
 * @package com.jettech.jettong.base.entity.sys.dictionary
 * @className DictionaryExtended
 * @date 2021-10-15
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@TableName("sys_dictionary_extended")
@ApiModel(value = "DictionaryExtended", description = "数据字典扩展信息表")
public class DictionaryExtended implements Serializable
{

    private static final long serialVersionUID = 1L;

    /**
     * 数据字典id
     */
    @ApiModelProperty(value = "数据字典id")
    @NotNull(message = "请填写数据字典id")
    @TableField(value = "`dict_id`")
    @Excel(name = "数据字典id")
    private Long dictId;

    /**
     * 扩展属性key
     */
    @ApiModelProperty(value = "扩展属性key")
    @Size(max = 255, message = "扩展属性key长度不能超过255")
    @TableField(value = "`key`", condition = LIKE)
    @Excel(name = "扩展属性key")
    private String key;

    /**
     * 扩展属性值
     */
    @ApiModelProperty(value = "扩展属性值")
    @Size(max = 255, message = "扩展属性值长度不能超过255")
    @TableField(value = "`value`", condition = LIKE)
    @Excel(name = "扩展属性值")
    private String value;


    @Builder
    public DictionaryExtended(
            Long dictId, String key, String value)
    {
        this.dictId = dictId;
        this.key = key;
        this.value = value;
    }

}
