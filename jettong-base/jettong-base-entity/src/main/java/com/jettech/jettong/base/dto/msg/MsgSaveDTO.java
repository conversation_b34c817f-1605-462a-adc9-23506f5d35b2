package com.jettech.jettong.base.dto.msg;

import com.google.common.collect.Sets;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Set;

/**
 * 消息新增对象
 *
 * <AUTHOR>
 * @version 1.0
 * @description 消息新增对象
 * @projectName jettong
 * @package com.jettech.jettong.base.dto.msg
 * @className MsgSaveDTO
 * @date 2021/9/15 9:43
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "MsgSaveDTO", description = "消息中心")
public class MsgSaveDTO implements Serializable
{

    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "消息内容")
    @NotNull(message = "消息内容不能为空")
    @Valid
    private MsgDTO msgDTO;

    /**
     * 接收人集合
     */
    @ApiModelProperty(value = "接收人id集合")
    private Set<Long> userIdList;

    /**
     * 角色编码
     */
    @ApiModelProperty(value = "角色编码")
    private Set<String> roleCodeList;


    public static MsgSaveDTO buildPersonal(MsgDTO msgDTO, Long userId)
    {
        return MsgSaveDTO.builder()
                .msgDTO(msgDTO)
                .userIdList(Sets.newHashSet(userId)).build();
    }

    public static MsgSaveDTO buildPersonal(MsgDTO msgDTO, Set<Long> userIdList)
    {
        return MsgSaveDTO.builder()
                .msgDTO(msgDTO)
                .userIdList(userIdList).build();
    }

    public static MsgSaveDTO buildRole(MsgDTO msgDTO, String roleCode)
    {
        return MsgSaveDTO.builder()
                .msgDTO(msgDTO)
                .roleCodeList(Sets.newHashSet(roleCode)).build();
    }

    public static MsgSaveDTO buildRole(MsgDTO msgDTO, Set<String> roleCodeList)
    {
        return MsgSaveDTO.builder()
                .msgDTO(msgDTO)
                .roleCodeList(roleCodeList).build();
    }
}
