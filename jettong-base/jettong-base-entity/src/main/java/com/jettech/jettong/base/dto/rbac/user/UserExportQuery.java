package com.jettech.jettong.base.dto.rbac.user;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 用户导出查询条件对象
 *
 * <AUTHOR>
 * @version 1.0
 * @description 用户导出查询条件对象
 * @projectName jettong
 * @package com.jettech.jettong.base.dto.rbac.user
 * @className UserExportQuery
 * @date 2021/9/15 9:43
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "UserExportQuery", description = "用户")
public class UserExportQuery implements Serializable
{

    private static final long serialVersionUID = 1L;

    /**
     * 账号
     */
    @ApiModelProperty(value = "账号")
    private String account;

    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名")
    private String name;

    /**
     * 所属机构
     */
    @ApiModelProperty(value = "所属机构")
    private Long orgId;

    /**
     * 内置
     */
    @ApiModelProperty(value = "内置")
    private Boolean readonly;

    /**
     * 集团工号
     */
    @ApiModelProperty(value = "集团工号")
    private String idCard;

    /**
     * 邮箱
     */
    @ApiModelProperty(value = "邮箱")
    private String email;

    /**
     * 手机
     */
    @ApiModelProperty(value = "手机")
    private String mobile;

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    private Boolean state;

    @ApiModelProperty(value = "类型")
    private String type;

}
