package com.jettech.jettong.base.entity.rbac.org;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jettech.basic.annotation.echo.Echo;
import com.jettech.basic.base.entity.TreeEntity;
import com.jettech.basic.model.EchoVO;
import com.jettech.jettong.base.entity.rbac.user.User;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.baomidou.mybatisplus.annotation.SqlCondition.LIKE;
import static com.jettech.jettong.common.constant.BaseEchoConstants.USER_ID_CLASS;

/**
 * 组织机构实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 组织机构实体类
 * @projectName jettong
 * @package com.jettech.jettong.base.rbac.entity
 * @className Org
 * @date 2021/9/15 9:43
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("sys_org")
@ApiModel(value = "Org", description = "组织")
@AllArgsConstructor
public class Org extends TreeEntity<Org, Long> implements EchoVO
{

    private static final long serialVersionUID = 1L;
    @TableField(exist = false)
    private Map<String, Object> echoMap = new HashMap<>();

    /**
     * 组织主编吗
     */
    @ApiModelProperty(value = "组织主编码")
    @TableField("`code`")
    private String code;

    /**
     * 组织父编码
     */
    @ApiModelProperty(value = "组织父编码")
    @TableField("`parent_code`")
    private String parentCode;

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    @TableField("`state`")
    @Excel(name = "状态", replace = {"新增_1", "更新_2", "删除_3"})
    private Integer state;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    @Size(max = 200, message = "描述长度不能超过200")
    @TableField(value = "description", condition = LIKE)
    @Excel(name = "描述")
    private String description;

    /**
     * logo
     * 此字段为图片，数据较大，默认不查询此字段。
     * 当前仅在登录进入平台页面时，渲染平台logo时使用
     */
    @ApiModelProperty(value = "`logo`")
    @Size(max = 50 * 1024, message = "图片大小不超过50KB")
    @TableField(value = "logo", select = false)
    private String logo;

    /**
     * 用户信息
     */
    @ApiModelProperty(value = "用户信息")
    @TableField(exist = false)
    private List<User> userList;

    /**
     * 负责人
     */
    @ApiModelProperty(value = "负责人")
    @TableField(value = "`leading_by`")
    @Echo(api = USER_ID_CLASS, beanClass = User.class)
    private Long leadingBy;

    /**
     * 类型
     */
    @ApiModelProperty(value = "类型")
    @TableField(value = "`type`")
    private String type;

    @Builder
    public Org(Long id, String name, Long parentId, Integer sort, LocalDateTime createTime, Long createdBy,
            LocalDateTime updateTime, Long updatedBy, Integer state, String code, String parentCode, String description, List<User> userList,
            String logo, Long leadingBy, String type)
    {
        this.id = id;
        this.name = name;
        this.parentId = parentId;
        this.sort = sort;
        this.createTime = createTime;
        this.createdBy = createdBy;
        this.updateTime = updateTime;
        this.updatedBy = updatedBy;
        this.code = code;
        this.parentCode = parentCode;
        this.state = state;
        this.description = description;
        this.userList = userList;
        this.logo = logo;
        this.leadingBy = leadingBy;
        this.type = type;
    }

}
