package com.jettech.jettong.base.enumeration.rbac;

import com.jettech.basic.base.BaseEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.stream.Stream;


/**
 * 租户状态枚举
 *
 * <AUTHOR>
 * @version 1.0
 * @description 租户状态枚举
 * @projectName jettong
 * @package com.jettech.jettong.base.enumeration.rbac
 * @className TenantStatus
 * @date 2021/9/15 9:43
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "TenantStatusEnum", description = "状态-枚举")
public enum TenantStatus implements BaseEnum
{

    /**
     * NORMAL="正常"
     */
    NORMAL("正常"),
    /**
     * WAIT_INIT="待初始化"
     */
    WAIT_INIT("待初始化"),
    /**
     * FORBIDDEN="禁用"
     */
    FORBIDDEN("禁用"),
    /**
     * WAITING="待审核"
     */
    WAITING("待审核"),
    /**
     * REFUSE="拒绝"
     */
    REFUSE("拒绝"),
    /**
     * DELETE="已删除"
     */
    DELETE("已删除"),
    ;

    @ApiModelProperty(value = "描述")
    private String desc;


    /**
     * 根据当前枚举的name匹配
     */
    public static TenantStatus match(String val, TenantStatus def)
    {
        return Stream.of(values()).parallel().filter(item -> item.name().equalsIgnoreCase(val)).findAny().orElse(def);
    }

    public static TenantStatus get(String val)
    {
        return match(val, null);
    }

    public boolean eq(TenantStatus val)
    {
        return val != null && eq(val.name());
    }

    @Override
    @ApiModelProperty(value = "编码", allowableValues = "NORMAL,WAIT_INIT,FORBIDDEN,WAITING,REFUSE,DELETE",
            example = "NORMAL")
    public String getCode()
    {
        return this.name();
    }

}
