package com.jettech.jettong.base.entity.msg;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jettech.basic.model.EchoVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;


/**
 * 消息通知模板适用事件信息实体类
 * <AUTHOR>
 * @version 1.0
 * @description 消息通知模板适用事件信息实体类
 * @projectName jettong
 * @package com.jettech.jettong.base.base.entity.msg
 * @className MsgTemplateEvent
 * @date 2023-05-22
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sys_msg_template_event")
@ApiModel(value = "MsgTemplateEvent", description = "消息通知模板适用事件信息")
public class MsgTemplateEvent implements Serializable, EchoVO
{

    private static final long serialVersionUID = 1L;
    @TableField(exist = false)
    private Map<String, Object> echoMap = new HashMap<>();
    /**
     * 外键，消息通知模板id
     */
    @ApiModelProperty(value = "外键，消息通知模板id")
    @NotNull(message = "请填写外键，消息通知模板id")
    @TableField(value = "`template_id`")
    private Long templateId;

    /**
     * 模板适用事件id
     */
    @ApiModelProperty(value = "模板适用事件id")
    @NotNull(message = "请填写模板适用事件id")
    @TableField(value = "`event_id`")
    private Long eventId;

    @Builder
    public MsgTemplateEvent(
            Long templateId, Long eventId)
    {
        this.templateId = templateId;
        this.eventId = eventId;
    }

}
