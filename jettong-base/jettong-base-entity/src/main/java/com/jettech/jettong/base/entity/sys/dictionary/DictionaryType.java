package com.jettech.jettong.base.entity.sys.dictionary;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.io.Serializable;


/**
 * 数据字典信息表实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 数据字典信息表实体类
 * @projectName jettong
 * @package com.jettech.jettong.base.entity.sys.dictionary
 * @className Dictionary
 * @date 2021-10-15
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@ApiModel(value = "DictionaryType", description = "数据字典类型信息表")
public class DictionaryType implements Serializable
{

    private static final long serialVersionUID = 1L;

    /**
     * 类型
     */
    @ApiModelProperty(value = "类型")
    private String type;

    /**
     * 类型标签
     */
    @ApiModelProperty(value = "类型标签")
    private String label;

    @ApiModelProperty(value = "字典数量")
    private Integer dictionaryNum;

    @Builder
    public DictionaryType(String type, String label, Integer dictionaryNum)
    {
        this.type = type;
        this.label = label;
        this.dictionaryNum = dictionaryNum;
    }
}
