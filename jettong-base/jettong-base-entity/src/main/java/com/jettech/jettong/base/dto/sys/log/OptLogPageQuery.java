package com.jettech.jettong.base.dto.sys.log;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.jettech.basic.log.entity.OptLogTypeEnum;
import com.jettech.jettong.common.dto.PeriodQuery;
import com.jettech.jettong.common.enumeration.HttpMethod;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 操作日志分页查询对象
 *
 * <AUTHOR>
 * @version 1.0
 * @description 操作日志分页查询对象
 * @projectName jettong
 * @package com.jettech.jettong.base.dto.sys.log
 * @className OptLogPageQuery
 * @date 2021/9/15 9:43
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "OptLogPageQuery", description = "操作日志")
public class OptLogPageQuery implements Serializable
{

    private static final long serialVersionUID = 1L;

    /**
     * 操作IP
     */
    @ApiModelProperty(value = "操作IP，模糊查询")
    private String requestIp;

    /**
     * 日志类型
     */
    @ApiModelProperty(value = "日志类型")
    private List<OptLogTypeEnum> types;

    /**
     * 操作人
     */
    @ApiModelProperty(value = "操作人Id")
    private Long userId;

    /**
     * 操作描述
     */
    @ApiModelProperty(value = "操作描述，模糊查询")
    private String description;

    /**
     * 请求地址
     */
    @ApiModelProperty(value = "请求地址，模糊查询")
    private String requestUri;

    /**
     * 请求类型
     * #HttpMethod{GET:GET请求;POST:POST请求;PUT:PUT请求;DELETE:DELETE请求;PATCH:PATCH请求;TRACE:TRACE请求;HEAD:HEAD请求;
     * OPTIONS:OPTIONS请求;}
     */
    @ApiModelProperty(value = "请求类型")
    @TableField("http_method")
    @Excel(name = "请求类型",
            replace = {"GET请求_GET", "POST请求_POST", "PUT请求_PUT", "DELETE请求_DELETE", "PATCH请求_PATCH", "TRACE请求_TRACE",
                    "HEAD请求_HEAD", "OPTIONS请求_OPTIONS", "_null"})
    private List<HttpMethod> httpMethods;

    /**
     * 消耗时间
     */
    @ApiModelProperty(value = "消耗时间，超过毫秒数")
    private Long consumingTime;

//    /**
//     * 操作日期-开始日期
//     */
//    @ApiModelProperty(value = "操作日期-开始日期")
//    private LocalDate startOptDate;
//
//    /**
//     * 操作日期-结束日期
//     */
//    @ApiModelProperty(value = "操作日期-结束日期")
//    private LocalDate endOptDate;

    @ApiModelProperty(value = "操作日期-结束日期")
    private PeriodQuery<LocalDateTime> date;


}
