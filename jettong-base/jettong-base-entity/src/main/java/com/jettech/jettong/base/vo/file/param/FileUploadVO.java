package com.jettech.jettong.base.vo.file.param;


import com.jettech.jettong.base.enumeration.file.FileBizType;
import com.jettech.jettong.base.enumeration.file.FileStorageType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 上传文件对象
 *
 * <AUTHOR>
 * @version 1.0
 * @description 上传文件对象
 * @projectName jettong
 * @package com.jettech.jettong.base.vo.fail.param
 * @className FileUploadVO
 * @date 2021/9/15 9:43
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@ToString
@Accessors(chain = true)
@ApiModel(value = "FileUploadVO", description = "附件上传")
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class FileUploadVO implements Serializable
{

    @ApiModelProperty(value = "业务类型")
    @NotNull(message = "请填写业务类型")
    private FileBizType bizType;

    @ApiModelProperty(value = "桶")
    private String bucket;

    @ApiModelProperty(value = "存储类型")
    private FileStorageType storageType;
}
