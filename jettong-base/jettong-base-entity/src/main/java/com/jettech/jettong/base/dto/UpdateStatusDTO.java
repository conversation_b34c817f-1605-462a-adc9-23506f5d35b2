package com.jettech.jettong.base.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 批量修改状态DTO
 *
 * <AUTHOR>
 * @version 1.0
 * @description 批量修改状态DTO
 * @projectName jettong-base-cloud
 * @package com.jettech.jettong.base.controller.msg
 * @className UpdateStatusDTO
 * @date 2023/5/24 11:41
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@Valid
@ApiModel(value = "UpdateStatusDTO", description = "批量修改状态DTO")
public class UpdateStatusDTO {

    @NotEmpty
    @ApiModelProperty("主键集合")
    private List<Long> ids;

    @ApiModelProperty("状态")
    @NotNull
    private Boolean status;
}
