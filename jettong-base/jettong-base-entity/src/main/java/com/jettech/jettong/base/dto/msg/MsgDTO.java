package com.jettech.jettong.base.dto.msg;

import com.jettech.jettong.common.event.enumeration.MsgType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * 消息对象
 *
 * <AUTHOR>
 * @version 1.0
 * @description 消息对象
 * @projectName jettong
 * @package com.jettech.jettong.base.dto.msg
 * @className MsgDTO
 * @date 2021/9/15 9:43
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@Data
@Accessors(chain = true)
@ApiModel(value = "MsgDTO", description = "消息")
public class MsgDTO implements Serializable
{

    private static final long serialVersionUID = 2499447636270422316L;

    /**
     * 业务ID
     * 业务表的唯一id
     */
    @ApiModelProperty(value = "业务ID")
    private Long bizId;

    /**
     * 业务类型
     */
    @ApiModelProperty(value = "业务类型")
    private String bizType;

    /**
     * 消息类型
     * #MsgType{TO_DO:待办;NOTIFY:通知;NOTICE:公告;EARLY_WARNING:预警;}
     */
    @ApiModelProperty(value = "消息类型")
    @NotNull(message = "消息类型不能为空")
    private MsgType msgType;

    /**
     * 标题
     */
    @ApiModelProperty(value = "标题")
    @Size(max = 255, message = "标题长度不能超过255")
    private String title;

    /**
     * 内容
     */
    @ApiModelProperty(value = "内容")
    @Size(max = 65535, message = "内容长度不能超过65,535")
    private String content;

    /**
     * 发送人
     */
    @ApiModelProperty(value = "发送人")
    private Long authorId;

    /**
     * 构建 通知类型的 消息中心
     *
     * @param bizType 业务类型
     * @param bizId 业务id
     * @param title 标题
     * @param content 内容
     * @return 消息参数
     */
    public static MsgDTO buildNotify(String bizType, Long bizId, String title, String content)
    {
        return MsgDTO.builder()
                .bizType(bizType).bizId(bizId)
                .msgType(MsgType.NOTIFY)
                .title(title).content(content)
                .build();
    }

    /**
     * 构建 代办类型的 消息中心
     *
     * @param bizType 业务类型
     * @param bizId 业务id
     * @param title 标题
     * @param content 内容
     * @return 消息参数
     */
    public static MsgDTO buildWait(String bizType, Long bizId,
            String title, String content)
    {
        return MsgDTO.builder()
                .bizType(bizType).bizId(bizId)
                .msgType(MsgType.TO_DO)
                .title(title).content(content)
                .build();
    }

    /**
     * 构建 预警类型的 消息中心
     *
     * @param bizType 业务类型
     * @param bizId 业务id
     * @param title 标题
     * @param content 内容
     * @return 消息参数
     */
    public static MsgDTO buildWarn(String bizType, Long bizId,
            String title, String content)
    {
        return MsgDTO.builder()
                .bizType(bizType).bizId(bizId)
                .msgType(MsgType.EARLY_WARNING)
                .title(title).content(content)
                .build();
    }

    /**
     * 构建 公示公告类型的 消息中心
     *
     * @param bizType 业务类型
     * @param bizId 业务id
     * @param title 标题
     * @param content 内容
     * @return 消息参数
     */
    public static MsgDTO buildPublicity(String bizType, Long bizId,
            String title, String content)
    {
        return MsgDTO.builder()
                .bizType(bizType).bizId(bizId)
                .msgType(MsgType.NOTICE)
                .title(title).content(content)
                .build();
    }
}
