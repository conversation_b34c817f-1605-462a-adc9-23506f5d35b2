package com.jettech.jettong.base.entity.sys.personalized;

import cn.hutool.core.convert.Convert;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jettech.basic.base.BaseEnum;
import com.jettech.basic.base.entity.Entity;
import com.jettech.basic.base.request.PageParams;
import com.jettech.basic.exception.BizException;
import com.jettech.basic.jackson.JsonUtil;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.module.SimpleModule;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.Optional;


/**
 * 用户列表视图信息实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 用户列表视图信息实体类
 * @projectName jettong
 * @package com.jettech.jettong.base.entity.sys.personalized
 * @className PersonalizedTableView
 * @date 2021-11-20
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("sys_personalized_table_view")
@ApiModel(value = "PersonalizedTableView", description = "用户列表视图信息")
@AllArgsConstructor
public class PersonalizedTableView extends Entity<Long>
{

    private static final long serialVersionUID = 1L;

    /**
     * 视图名称
     */
    @ApiModelProperty(value = "视图名称")
    @NotEmpty(message = "请填写视图名称")
    @Size(max = 50, message = "视图名称长度不能超过50")
    @TableField(value = "`name`")
    private String name;

    /**
     * 用户id
     */
    @ApiModelProperty(value = "用户id")
    @TableField(value = "`user_id`")
    private Long userId;

    /**
     * 列表唯一code
     */
    @ApiModelProperty(value = "列表唯一code")
    @NotEmpty(message = "请填写列表唯一code")
    @Size(max = 100, message = "列表唯一code长度不能超过100")
    @TableField(value = "`table_code`")
    private String tableCode;

    /**
     * 是否公共视图
     */
    @ApiModelProperty(value = "是否公共视图")
    @TableField(value = "`is_public`")
    private Boolean isPublic;

    /**
     * 是否保护视图
     */
    @ApiModelProperty(value = "是否保护视图")
    @TableField(value = "`is_protect`")
    private Boolean isProtect;

    /**
     * 是否内置视图
     */
    @ApiModelProperty(value = "是否内置视图")
    @TableField(value = "`is_built_in`")
    private Boolean isBuiltIn;

    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    @TableField(value = "`sort`")
    private Integer sort;

    /**
     * 视图查询条件，展示字段等信息
     */
    @ApiModelProperty(value = "视图查询条件，展示字段等信息")
    @TableField(value = "`search`")
    private String search;

    /**
     * 是否默认视图，非数据库字段
     */
    @TableField(exist = false)
    private Boolean isDefault;

    /**
     * 最后一次查询的视图ID
     */
    @TableField(exist = false)
    private Long tableId;

    @Builder
    public PersonalizedTableView(Long id, Long createdBy, LocalDateTime createTime, Long updatedBy,
            LocalDateTime updateTime,
            String name, Long userId, String tableCode, Boolean isPublic, Boolean isProtect, Boolean isBuiltIn,
            Integer sort,
            String search, Boolean isDefault)
    {
        this.id = id;
        this.createdBy = createdBy;
        this.createTime = createTime;
        this.updatedBy = updatedBy;
        this.updateTime = updateTime;
        this.name = name;
        this.userId = userId;
        this.tableCode = tableCode;
        this.isPublic = isPublic;
        this.isProtect = isProtect;
        this.isBuiltIn = isBuiltIn;
        this.sort = sort;
        this.search = search;
        this.isDefault = isDefault;
    }

    /**
     * 定制化的ObjectMapper
     */
    private static final JsonUtil.JacksonObjectMapper MAPPER = new JsonUtil.JacksonObjectMapper() {{
        // 为空的字段不序列化
        this.setDefaultPropertyInclusion(JsonInclude.Include.NON_NULL);
        SimpleModule module = new SimpleModule();
        // 枚举类型序列化为code
        module.addSerializer(BaseEnum.class, new EnumCodeSerializer());
        this.registerModule(module);
    }};

    public static PersonalizedTableView lastView(Long userId, String tableCode, PageParams<?> pageParams) {

        if (pageParams == null || userId == null) {
            return null;
        }

        Boolean tableSave = Optional.of(pageParams)
                .map(PageParams::getExtra)
                .map(extra -> extra.get("tableSave"))
                .map(val -> Convert.toBool(val.toString()))
                .orElse(false);
        // 如果不需要保存筛选器，则直接返回
        if (!tableSave) {
            return null;
        }

        final String tableViewCode = Optional.of(pageParams)
                .map(PageParams::getExtra)
                .map(extra -> extra.get("tableCode"))
                .map(Object::toString)
                .orElse(tableCode);

        try {
            String search = MAPPER.writeValueAsString(pageParams);
            PersonalizedTableView build = PersonalizedTableView.builder()
                    .userId(userId)
                    .search(search)
                    .tableCode(tableViewCode)
                    .sort(Integer.MIN_VALUE)
                    .name("上次查询")
                    .isPublic(false)
                    .isProtect(true)
                    .isBuiltIn(false)
                    .isDefault(true)
                    .build();

            Long tableId = Optional.of(pageParams)
                    .map(PageParams::getExtra)
                    .map(extra -> extra.get("tableId"))
                    .map(val -> Convert.toLong(val.toString()))
                    .orElse(null);
            build.setTableId(tableId);

            return build;

        } catch (JsonProcessingException e) {
            throw BizException.wrap("序列化查询参数失败", e);
        } catch (Exception e) {
            throw BizException.wrap("保存最后一次查询失败", e);
        }
    }

    private static class EnumCodeSerializer extends JsonSerializer<BaseEnum> {
        @Override
        public void serialize(BaseEnum value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
            gen.writeString(value.getCode());
        }
    }

}
