package com.jettech.jettong.base.entity.msg;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jettech.basic.model.EchoVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;


/**
 * 消息通知发送历史接收人信息实体类
 * <AUTHOR>
 * @version 1.0
 * @description 消息通知发送历史接收人信息实体类
 * @projectName jettong
 * @package com.jettech.jettong.base.entity.msg
 * @className MsgHistoryReceive
 * @date 2023-06-07
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sys_msg_history_receive")
@ApiModel(value = "MsgHistoryReceive", description = "消息通知发送历史接收人信息")
public class MsgHistoryReceive implements Serializable, EchoVO
{

    private static final long serialVersionUID = 1L;
    @TableField(exist = false)
    private Map<String, Object> echoMap = new HashMap<>();
    /**
     * 外键，消息通知历史id
     */
    @ApiModelProperty(value = "外键，消息通知历史id")
    @NotNull(message = "请填写外键，消息通知历史id")
    @TableId(value = "`msg_history_id`", type = IdType.INPUT)
    private Long msgHistoryId;

    /**
     * 接收人ID 
     * #sys_user
     */
    @ApiModelProperty(value = "接收人ID")
    @NotNull(message = "请填写接收人ID")
    @TableField(value = "`user_id`")
    private Long userId;

    /**
     * 是否已读
     */
    @ApiModelProperty(value = "是否已读")
    @TableField(value = "`is_read`")
    private Boolean isRead;

    /**
     * 读取时间
     */
    @ApiModelProperty(value = "读取时间")
    @TableField(value = "`read_time`")
    private LocalDateTime readTime;

    @Builder
    public MsgHistoryReceive(
            Long msgHistoryId, Long userId, Boolean isRead, LocalDateTime readTime)
    {
        this.msgHistoryId = msgHistoryId;
        this.userId = userId;
        this.isRead = isRead;
        this.readTime = readTime;
    }

}
