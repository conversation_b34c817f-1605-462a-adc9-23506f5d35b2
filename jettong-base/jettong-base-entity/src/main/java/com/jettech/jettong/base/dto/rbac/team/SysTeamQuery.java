package com.jettech.jettong.base.dto.rbac.team;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 团队信息表查询实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 团队信息表查询实体类
 * @projectName jettong
 * @package com.jettech.jettong.base.base.dto
 * @className SysTeam
 * @date 2023-03-13
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "SysTeamQuery", description = "团队信息表")
public class SysTeamQuery implements Serializable
{

    private static final long serialVersionUID = 1L;

    /**
     * 团队名称
     */
    @ApiModelProperty(value = "团队名称,模糊匹配")
    private String name;

    /**
     * 父级团队
     */
    @ApiModelProperty(value = "父级团队")
    private Long parentId;
    /**
     * 负责人
     */
    @ApiModelProperty(value = "负责人")
    private Long leaderBy;
    /**
     * 所属机构ID
     */
    @ApiModelProperty(value = "所属机构ID")
    private Long orgId;
    /**
     * 团队类型
     */
    @ApiModelProperty(value = "团队类型")
    private String type;

}
