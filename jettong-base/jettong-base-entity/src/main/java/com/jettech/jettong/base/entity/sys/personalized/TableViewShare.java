package com.jettech.jettong.base.entity.sys.personalized;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jettech.basic.base.entity.SuperEntity;
import com.jettech.basic.model.EchoVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;


/**
 * 筛选器收藏夹实体类
 * <AUTHOR>
 * @version 1.0
 * @description 筛选器收藏夹实体类
 * @projectName jettong
 * @package com.jettech.jettong.base.base.entity.sys
 * @className TableViewCollection
 * @date 2023-03-23
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("sys_table_view_share")
@ApiModel(value = "TableViewShare", description = "筛选器共享信息表")
@AllArgsConstructor
public class TableViewShare extends SuperEntity<Long> implements EchoVO
{

    private static final long serialVersionUID = 1L;
    @TableField(exist = false)
    private Map<String, Object> echoMap = new HashMap<>();
    /**
     * 筛选器ID
     */
    @ApiModelProperty(value = "筛选器ID")
    @NotNull(message = "请填写筛选器ID")
    @TableField(value = "`view_id`")
    private Long viewId;

    /**
     * 所属用户ID
     */
    @ApiModelProperty(value = "所属用户ID")
    @NotNull(message = "请填写所属用户ID")
    @TableField(value = "`user_id`")
    private Long userId;


    @Builder
    public TableViewShare(Long id, Long createdBy, LocalDateTime createTime, Long updatedBy, LocalDateTime updateTime,
                    Long viewId, Long userId)
    {
        this.id = id;
        this.createdBy = createdBy;
        this.createTime = createTime;
        this.viewId = viewId;
        this.userId = userId;
    }

}
