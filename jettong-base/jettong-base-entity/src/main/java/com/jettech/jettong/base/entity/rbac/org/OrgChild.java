package com.jettech.jettong.base.entity.rbac.org;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @description TODO
 * @projectName HBSC
 * @package com.jettech.jettong.base.entity.rbac.org
 * @className OrgChild
 * @date 2024/4/23 16:24
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@TableName("sys_org_child")
@ApiModel(value = "OrgChild", description = "组织子组织")
public class OrgChild implements Serializable
{

    /**
     * 组织编码
     */
    @ApiModelProperty(value = "组织id")
    @TableField("`parent_id`")
    private Long parentId;

    /**
     * 组织编码
     */
    @ApiModelProperty(value = "子组织id")
    @TableField("`child_id`")
    private Long childId;

    @Builder
    public OrgChild(Long parentId, Long childId)
    {
        this.parentId = parentId;
        this.childId = childId;
    }
}
