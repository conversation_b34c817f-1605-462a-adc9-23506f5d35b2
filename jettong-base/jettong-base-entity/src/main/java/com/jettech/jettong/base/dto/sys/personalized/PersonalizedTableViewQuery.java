package com.jettech.jettong.base.dto.sys.personalized;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

/**
 * 用户列表视图信息查询实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 用户列表视图信息查询实体类
 * @projectName jettong
 * @package com.jettech.jettong.base.dto.sys.personalized
 * @className PersonalizedTableViewQuery
 * @date 2021-11-20
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "PersonalizedTableViewQuery", description = "用户列表视图信息查询")
public class PersonalizedTableViewQuery implements Serializable
{

    private static final long serialVersionUID = 1L;

    /**
     * 列表唯一code
     */
    @ApiModelProperty(value = "列表唯一code")
    @NotEmpty(message = "请输入列表唯一code")
    private String tableCode;

}
