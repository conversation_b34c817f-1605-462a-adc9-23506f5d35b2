package com.jettech.jettong.base.dto.sys.personalized;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * 用户自定义固定菜单信息修改实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 用户自定义固定菜单信息修改实体类
 * @projectName jettong
 * @package com.jettech.jettong.base.dto.sys.personalized
 * @className PersonalizedFixedMenuUpdateDTO
 * @date 2021-11-29
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "PersonalizedFixedMenuUpdateDTO", description = "用户自定义固定菜单信息")
public class PersonalizedFixedMenuUpdateDTO implements Serializable
{

    private static final long serialVersionUID = 1L;

    /**
     * 菜单code
     */
    @ApiModelProperty(value = "菜单code")
    @Size(max = 50, message = "菜单code长度不能超过50")
    @NotNull(message = "请输入菜单code")
    private String menuCode;

    @ApiModelProperty(value = "排序")
    @NotNull(message = "请输入排序")
    private Integer sort;
}
