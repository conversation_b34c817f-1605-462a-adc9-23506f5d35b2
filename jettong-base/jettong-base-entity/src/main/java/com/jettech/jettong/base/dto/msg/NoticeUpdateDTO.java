package com.jettech.jettong.base.dto.msg;

import com.jettech.basic.base.entity.SuperEntity;
import com.jettech.jettong.base.entity.file.File;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * 通知公告信息修改实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 通知公告信息修改实体类
 * @projectName jettong
 * @package com.jettech.jettong.base.dto.sys.msg
 * @className NoticeUpdateDTO
 * @date 2023-05-15
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "NoticeUpdateDTO", description = "通知公告信息")
public class NoticeUpdateDTO implements Serializable
{

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @NotNull(message = "请填写主键", groups = SuperEntity.Update.class)
    private Long id;

    /**
     * 编号
     */
    @ApiModelProperty(value = "编号")
    @Size(max = 100, message = "编号长度不能超过100")
    private String code;
    /**
     * 公告标题
     */
    @ApiModelProperty(value = "公告标题")
    @NotEmpty(message = "请填写公告标题")
    @Size(max = 500, message = "公告标题长度不能超过500")
    private String title;
    /**
     * 主题分类字典
     */
    @ApiModelProperty(value = "主题分类字典")
    @Size(max = 50, message = "主题分类字典长度不能超过50")
    private String noticeType;
    /**
     * 公告内容
     */
    @ApiModelProperty(value = "公告内容")
    @Size(max = 65535, message = "公告内容长度不能超过65,535")
    private String body;
    /**
     * 状态，0-暂存，1-已发布，2-已撤销
     */
    @ApiModelProperty(value = "状态，0-暂存，1-已发布，2-已撤销")
    @NotNull(message = "请填写状态，0-暂存，1-已发布，2-已撤销")
    private Integer state;
    /**
     * 联系人
     */
    @ApiModelProperty(value = "联系人")
    private Long liaisoner;
    /**
     * 联系电话
     */
    @ApiModelProperty(value = "联系电话")
    @Size(max = 50, message = "联系电话长度不能超过50")
    private String phone;


    /**
     * 附件
     */
    @ApiModelProperty(value = "附件")
    private List<File> files;
}
