package com.jettech.jettong.base.entity.rbac.role;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jettech.basic.base.entity.Entity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;

import static com.baomidou.mybatisplus.annotation.SqlCondition.LIKE;

/**
 * 菜单功能实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 菜单功能实体类
 * @projectName jettong
 * @package com.jettech.jettong.base.entity.rbac.role
 * @className Function
 * @date 2021/10/14 9:43
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("sys_function")
@ApiModel(value = "Function", description = "功能")
@AllArgsConstructor
public class Function extends Entity<Long>
{

    private static final long serialVersionUID = 1L;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    @Size(max = 500, message = "编码长度不能超过500")
    @TableField(value = "code", condition = LIKE)
    @Excel(name = "编码")
    private String code;

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    @NotEmpty(message = "名称不能为空")
    @Size(max = 255, message = "名称长度不能超过255")
    @TableField(value = "name", condition = LIKE)
    @Excel(name = "名称")
    private String name;


    /**
     * 菜单ID
     * #sys_menu
     */
    @ApiModelProperty(value = "菜单ID")
    @TableField("menu_id")
    @Excel(name = "菜单ID")
    private Long menuId;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    @Size(max = 255, message = "描述长度不能超过255")
    @TableField(value = "description", condition = LIKE)
    @Excel(name = "描述")
    private String description;

    @Builder
    public Function(Long id, Long createdBy, LocalDateTime createTime, Long updatedBy, LocalDateTime updateTime,
            String code, String name, Long menuId, String description)
    {
        this.id = id;
        this.createdBy = createdBy;
        this.createTime = createTime;
        this.updatedBy = updatedBy;
        this.updateTime = updateTime;
        this.code = code;
        this.name = name;
        this.menuId = menuId;
        this.description = description;
    }

}
