package com.jettech.jettong.base.entity.rbac.user;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jettech.basic.annotation.echo.Echo;
import com.jettech.basic.base.entity.Entity;
import com.jettech.basic.model.EchoVO;
import com.jettech.jettong.base.entity.rbac.org.Org;
import com.jettech.jettong.common.constant.DictionaryBaseType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.jettech.jettong.common.constant.BaseEchoConstants.DICTIONARY_ITEM_CLASS;
import static com.jettech.jettong.common.constant.BaseEchoConstants.ORG_ID_CLASS;

/**
 * 用户实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 用户实体类
 * @projectName jettong
 * @package com.jettech.jettong.base.entity.rbac.user
 * @className User
 * @date 2021/10/14 9:43
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("sys_user")
@ApiModel(value = "User", description = "用户")
@AllArgsConstructor
public class User extends Entity<Long> implements EchoVO
{

    private static final long serialVersionUID = 1L;
    @TableField(exist = false)
    private Map<String, Object> echoMap = new HashMap<>();

    /**
     * 账号
     */
    @ApiModelProperty(value = "账号")
    @NotEmpty(message = "账号不能为空")
    @Size(max = 200, message = "账号长度不能超过200")
    @TableField(value = "`account`")
    private String account;

    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名")
    @NotEmpty(message = "姓名不能为空")
    @Size(max = 100, message = "姓名长度不能超过100")
    @TableField(value = "`name`")
    private String name;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    @Size(max = 200, message = "描述长度不能超过200")
    @TableField(value = "`description`")
    private String description;

    /**
     * 组织
     * #sys_org
     */
    @ApiModelProperty(value = "组织")
    @TableField("`org_id`")
    @Echo(api = ORG_ID_CLASS, beanClass = Org.class)
    private Long orgId;

    /**
     * 内置
     */
    @ApiModelProperty(value = "内置")
    @NotNull(message = "内置不能为空")
    private Boolean readonly;



    /**
     * 邮箱
     */
    @ApiModelProperty(value = "邮箱")
    @Size(max = 255, message = "邮箱长度不能超过255")
    @TableField(value = "`email`")
    private String email;

    /**
     * 手机
     */
    @ApiModelProperty(value = "集团工号")
    @Size(max = 18, message = "集团工号长度不能超过18")
    @TableField(value = "`id_card`")
    private String idCard;

    /**
     * 手机
     */
    @ApiModelProperty(value = "手机")
    @Size(max = 20, message = "手机长度不能超过20")
    @TableField(value = "`mobile`")
    private String mobile;

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    @TableField("`state`")
    private Boolean state;

    /**
     * 状态
     */
    @ApiModelProperty(value = "是否内置头像")
    @TableField("`avatar_type`")
    private Boolean avatarType;

    /**
     * 头像
     */
    @ApiModelProperty(value = "头像")
    @TableField(value = "`avatar`")
    private Long avatar;

    /**
     * 头像
     */
    @ApiModelProperty(value = "头像相对地址")
    @TableField(value = "`avatar_path`")
    private String avatarPath;

    /**
     * 最后一次输错密码时间
     */
    @ApiModelProperty(value = "最后一次输错密码时间")
    @TableField("`password_error_last_time`")
    private LocalDateTime passwordErrorLastTime;

    /**
     * 密码错误次数
     */
    @ApiModelProperty(value = "密码错误次数")
    @TableField("`password_error_num`")
    private Integer passwordErrorNum;

    /**
     * 密码
     */
    @ApiModelProperty(value = "密码")
    @NotEmpty(message = "密码不能为空")
    @Size(max = 64, message = "密码长度不能超过64")
    @TableField(value = "`password`")
    private String password;

    /**
     * 密码盐
     */
    @ApiModelProperty(value = "密码盐")
    @NotEmpty(message = "密码盐不能为空")
    @Size(max = 20, message = "密码盐长度不能超过20")
    @TableField(value = "`salt`")
    private String salt;

    /**
     * 是否上报工作
     */
    @ApiModelProperty(value = "是否报工")
    @TableField("`report_work`")
    private Boolean reportWork;

    /**
     * 用户角色信息
     */
    @ApiModelProperty(value = "用户角色信息")
    @TableField(exist = false)
    private List<UserRole> userRoles;

    /**
     * 用户所属组织机构层级，包含父级
     */
    @ApiModelProperty(value = "用户所属组织机构层级")
    @TableField(exist = false)
    private List<Org> orgs;

    /**
     * 类型
     */
    @ApiModelProperty(value = "类型")
    @TableField(value = "`type`")
    @Echo(api = DICTIONARY_ITEM_CLASS, dictType = DictionaryBaseType.USER_TYPE)
    private String type;

    @ApiModelProperty(value = "微信UserId")
    @TableField(value = "`wx_user_id`")
    private String wxUserId;

    @Builder
    public User(Long id, Long createdBy, LocalDateTime createTime, Long updatedBy, LocalDateTime updateTime,
            String account, String name, String description, Long orgId, Boolean readonly, String idCard,
            String email, String mobile, Boolean state, Boolean avatarType, Long avatar,
            LocalDateTime passwordErrorLastTime,
            Integer passwordErrorNum,
            String password, String salt, List<UserRole> userRoles, List<Org> orgs,
            String type, String wxUserId)
    {
        this.id = id;
        this.createdBy = createdBy;
        this.createTime = createTime;
        this.updatedBy = updatedBy;
        this.updateTime = updateTime;
        this.account = account;
        this.name = name;
        this.description = description;
        this.orgId = orgId;
        this.readonly = readonly;
        this.idCard = idCard;
        this.email = email;
        this.mobile = mobile;
        this.state = state;
        this.avatarType = avatarType;
        this.avatar = avatar;
        this.passwordErrorLastTime = passwordErrorLastTime;
        this.passwordErrorNum = passwordErrorNum;
        this.password = password;
        this.salt = salt;
        this.userRoles = userRoles;
        this.orgs = orgs;
        this.type = type;
        this.wxUserId = wxUserId;
    }

}
