package com.jettech.jettong.base.dto.msg.event;

import com.jettech.jettong.common.event.enumeration.MsgEventType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * 消息通知事件信息新增实体类
 * <AUTHOR>
 * @version 1.0
 * @description 消息通知事件信息新增实体类
 * @projectName jettong
 * @package com.jettech.jettong.base.base.dto.msg
 * @className MsgEventSaveDTO
 * @date 2023-05-22
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "MsgEventSaveDTO", description = "消息通知事件信息")
public class MsgEventSaveDTO implements Serializable
{

    private static final long serialVersionUID = 1L;

    /**
     * 编号
     */
    @ApiModelProperty(value = "编号")
    @NotEmpty(message = "请填写编号")
    @Size(max = 50, message = "编号长度不能超过50")
    private String code;
    /**
     * 事件类型枚举,项目、流水线、代码库
     */
    @ApiModelProperty(value = "事件类型枚举,项目、流水线、代码库")
    @NotEmpty(message = "请填写事件类型枚举,项目、流水线、代码库")
    @Size(max = 50, message = "事件类型枚举,项目、流水线、代码库长度不能超过50")
    private MsgEventType type;
    /**
     * 事件名称
     */
    @ApiModelProperty(value = "事件名称")
    @NotEmpty(message = "请填写事件名称")
    @Size(max = 200, message = "事件名称长度不能超过200")
    private String name;
    /**
     * 事件描述
     */
    @ApiModelProperty(value = "事件描述")
    @Size(max = 1000, message = "事件描述长度不能超过1000")
    private String description;

}
