package com.jettech.jettong.base.dto.msg;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 消息通知方案信息分页实体类
 * <AUTHOR>
 * @version 1.0
 * @description 消息通知方案信息分页实体类
 * @projectName jettong
 * @package com.jettech.jettong.base.dto.msg
 * @className MsgScheme
 * @date 2023-05-31
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "MsgSchemePageQuery", description = "消息通知方案信息")
public class MsgSchemePageQuery implements Serializable
{

    private static final long serialVersionUID = 1L;

    /**
     * 消息通知方案类型枚举，支持平台级，项目级，流水线、代码库
     */
    @ApiModelProperty(value = "消息通知方案类型枚举，支持平台级，项目级，流水线、代码库")
    private String type;
    /**
     * 方案名称
     */
    @ApiModelProperty(value = "方案名称")
    private String name;
    /**
     * 方案描述
     */
    @ApiModelProperty(value = "方案描述")
    private String description;
    /**
     * 启用状态
     */
    @ApiModelProperty(value = "启用状态")
    private Boolean state;
    /**
     * 是否内置方案
     */
    @ApiModelProperty(value = "是否内置方案")
    private Boolean readonly;
    /**
     * 消息通知引擎类型枚举
     */
    @ApiModelProperty(value = "消息通知引擎类型枚举")
    private String msgEngineType;
    /**
     * 外键，消息通知事件id
     */
    @ApiModelProperty(value = "外键，消息通知事件id")
    private Long eventId;
    /**
     * 外键，消息通知模板id
     */
    @ApiModelProperty(value = "外键，消息通知模板id")
    private Long templateId;


    /**
     * 外键，消息接收人规则id
     */
    @ApiModelProperty(value = "外键，消息通知模板id")
    private Long receiveId;
    /**
     * 是否定时通知
     */
    @ApiModelProperty(value = "是否定时通知")
    private Boolean scheduled;
    /**
     * 定时通知cron表达式
     */
    @ApiModelProperty(value = "定时通知cron表达式")
    private String cron;

}
