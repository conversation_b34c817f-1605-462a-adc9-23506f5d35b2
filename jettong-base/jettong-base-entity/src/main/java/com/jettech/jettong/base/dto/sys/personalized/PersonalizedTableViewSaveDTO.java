package com.jettech.jettong.base.dto.sys.personalized;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * 用户自定义列表过滤器信息新增实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 用户自定义列表过滤器信息新增实体类
 * @projectName jettong
 * @package com.jettech.jettong.base.dto.sys.personalized
 * @className PersonalizedTableViewSaveDTO
 * @date 2021-11-20
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "PersonalizedTableViewSaveDTO", description = "用户自定义列表过滤器信息")
public class PersonalizedTableViewSaveDTO implements Serializable
{

    private static final long serialVersionUID = 1L;

    /**
     * 过滤器名称
     */
    @ApiModelProperty(value = "视图名称")
    @NotEmpty(message = "请填写视图名称")
    @Size(max = 100, message = "过滤器名称长度不能超过100")
    private String name;

    /**
     * 列表唯一code
     */
    @ApiModelProperty(value = "列表唯一code")
    @NotEmpty(message = "请填写列表唯一code")
    @Size(max = 100, message = "列表唯一code长度不能超过100")
    private String tableCode;

    /**
     * 是否公共视图
     */
    @ApiModelProperty(value = "是否公共视图")
    private Boolean isPublic;

    /**
     * 是否保护视图
     */
    @ApiModelProperty(value = "是否保护视图")
    private Boolean isProtect;

    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    private Integer sort;

    /**
     * 视图查询条件，展示字段等信息
     */
    @ApiModelProperty(value = "视图查询条件，展示字段等信息")
    private String search;

    /**
     * 是否默认视图
     */
    @ApiModelProperty(value = "是否默认视图")
    @NotNull(message = "请填写是否默认视图")
    private Boolean isDefault;

}
