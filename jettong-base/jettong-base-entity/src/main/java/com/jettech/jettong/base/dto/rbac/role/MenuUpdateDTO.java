package com.jettech.jettong.base.dto.rbac.role;

import com.jettech.basic.base.entity.SuperEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * 菜单修改对象
 *
 * <AUTHOR>
 * @version 1.0
 * @description 菜单修改对象
 * @projectName jettong
 * @package com.jettech.jettong.base.dto.rbac.role
 * @className MenuSaveDTO
 * @date 2021/9/15 9:43
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "MenuUpdateDTO", description = "菜单")
public class MenuUpdateDTO implements Serializable
{

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @NotNull(message = "id不能为空", groups = SuperEntity.Update.class)
    private Long id;

    @ApiModelProperty(value = "名称")
    @NotEmpty(message = "请填写名称")
    @Size(max = 20, message = "名称长度不能超过20")
    protected String name;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    @Size(max = 200, message = "描述长度不能超过200")
    private String description;
    @ApiModelProperty(value = "所属平台")
    protected String platform;
    @ApiModelProperty(value = "上级资源")
    protected Long parentId;

    /**
     * 路径
     */
    @ApiModelProperty(value = "地址栏地址")
    @NotEmpty(message = "请填写地址栏地址")
    @Size(max = 1000, message = "地址栏地址长度不能超过1000")
    private String path;

    /**
     * 菜单图标
     */
    @ApiModelProperty(value = "图标")
    @Size(max = 255, message = "图标长度不能超过255")
    private String icon;

    /**
     * 组件
     */
    @ApiModelProperty(value = "组件")
    @Size(max = 200, message = "组件长度不能超过200")
    private String component;

    /**
     * 排序
     */
    @ApiModelProperty(value = "排序号")
    protected Integer sort;

    /**
     * 通用菜单
     * True表示无需分配所有人就可以访问的
     */
    @ApiModelProperty(value = "通用菜单")
    private Boolean isGeneral;

    /**
     * 是否隐藏
     */
    @ApiModelProperty(value = "是否隐藏")
    private Boolean isHide;

    /**
     * 是否显示子级
     */
    @ApiModelProperty(value = "是否显示子级")
    private Boolean showChildren;

    /**
     * 是否为按钮
     */
    @ApiModelProperty(value = "是否为按钮")
    private Boolean isButton;

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    private Boolean state;

    /**
     * 内置
     */
    @ApiModelProperty(value = "内置")
    private Boolean readonly;

    /**
     * 是否外部菜单
     */
    @ApiModelProperty(value = "是否外部菜单")
    private Boolean isExternal;

    /**
     * 外部菜单url
     */
    @ApiModelProperty(value = "外部菜单url")
    @Size(max = 2048, message = "外部菜单url不能超过2048")
    private String externalUrl;
}
