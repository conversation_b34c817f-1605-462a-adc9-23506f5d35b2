package com.jettech.jettong.base.dto.rbac.tenant;

import com.jettech.jettong.base.enumeration.rbac.TenantStatus;
import com.jettech.jettong.base.enumeration.rbac.TenantType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 租户分页查询对象
 *
 * <AUTHOR>
 * @version 1.0
 * @description 租户分页查询对象
 * @projectName jettong
 * @package com.jettech.jettong.base.dto.rbac.tenant
 * @className TenantPageQuery
 * @date 2021/9/15 9:43
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "TenantPageQuery", description = "租户")
public class TenantPageQuery implements Serializable
{

    private static final long serialVersionUID = 1L;

    /**
     * 租户编码
     */
    @ApiModelProperty(value = "租户编码")
    private String code;

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    private String name;

    /**
     * 类型
     * #{CREATE:创建;REGISTER:注册}
     */
    @ApiModelProperty(value = "类型")
    private TenantType type;

    /**
     * 状态
     * #{NORMAL:正常;WAIT_INIT:待初始化;FORBIDDEN:禁用;WAITING:待审核;REFUSE:拒绝;DELETE:已删除}
     */
    @ApiModelProperty(value = "状态")
    private TenantStatus status;

    /**
     * 责任人
     */
    @ApiModelProperty(value = "责任人")
    private String duty;


}
