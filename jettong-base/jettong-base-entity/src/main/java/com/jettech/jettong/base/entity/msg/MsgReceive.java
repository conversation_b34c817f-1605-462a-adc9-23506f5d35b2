package com.jettech.jettong.base.entity.msg;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jettech.basic.annotation.echo.Echo;
import com.jettech.basic.base.entity.Entity;
import com.jettech.basic.model.EchoVO;
import com.jettech.jettong.base.entity.rbac.user.User;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.baomidou.mybatisplus.annotation.SqlCondition.LIKE;
import static com.jettech.jettong.common.constant.BaseEchoConstants.USER_ID_CLASS;


/**
 * 消息通知接收人信息实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 消息通知接收人信息实体类
 * @projectName jettong
 * @package com.jettech.jettong.base.entity.msg
 * @className MsgReceive
 * @date 2023-05-31
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("sys_msg_receive")
@ApiModel(value = "MsgReceive", description = "消息通知接收人信息")
@AllArgsConstructor
public class MsgReceive extends Entity<Long> implements EchoVO {

    private static final long serialVersionUID = 1L;
    @TableField(exist = false)
    private Map<String, Object> echoMap = new HashMap<>();
    /**
     * 接收人名称
     */
    @ApiModelProperty(value = "接收人名称")
    @NotEmpty(message = "请填写接收人名称")
    @Size(max = 100, message = "接收人名称长度不能超过100")
    @TableField(value = "`name`", condition = LIKE)
    private String name;

    /**
     * 接收人描述
     */
    @ApiModelProperty(value = "接收人描述")
    @Size(max = 500, message = "接收人描述长度不能超过500")
    @TableField(value = "`description`", condition = LIKE)
    private String description;

    /**
     * 是否内置
     */
    @ApiModelProperty(value = "是否内置")
    @NotNull(message = "请填写是否内置")
    @TableField(value = "`readonly`")
    private Boolean readonly;

    @ApiModelProperty("创建人ID")
    @TableField(value = "created_by", fill = FieldFill.INSERT)
    @Echo(api = USER_ID_CLASS, beanClass = User.class)
    protected Long createdBy;

    @ApiModelProperty("最后修改人ID")
    @TableField(value = "updated_by", fill = FieldFill.INSERT_UPDATE)
    @Echo(api = USER_ID_CLASS, beanClass = User.class)
    protected Long updatedBy;

    /**
     * 规则列表
     */
    @ApiModelProperty(value = "规则列表")
    @TableField(exist = false)
    private List<MsgReceiveRule> ruleList;


    @Builder
    public MsgReceive(Long id, LocalDateTime createTime, Long createdBy, LocalDateTime updateTime, Long updatedBy,
            String name, String description, Boolean readonly) {
        this.id = id;
        this.createTime = createTime;
        this.createdBy = createdBy;
        this.updateTime = updateTime;
        this.updatedBy = updatedBy;
        this.name = name;
        this.description = description;
        this.readonly = readonly;
    }

}
