package com.jettech.jettong.base.dto.rbac.tenant;

import com.jettech.basic.base.entity.SuperEntity;
import com.jettech.jettong.base.enumeration.rbac.TenantStatus;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 租户修改对象
 *
 * <AUTHOR>
 * @version 1.0
 * @description 租户修改对象
 * @projectName jettong
 * @package com.jettech.jettong.base.dto.rbac.tenant
 * @className TenantUpdateDTO
 * @date 2021/9/15 9:43
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "TenantUpdateDTO", description = "企业")
public class TenantUpdateDTO implements Serializable
{

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @NotNull(message = "id不能为空", groups = SuperEntity.Update.class)
    private Long id;

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    @Size(max = 200, message = "名称长度不能超过200")
    private String name;

    /**
     * 状态
     * #{NORMAL:正常;WAIT_INIT:待初始化;FORBIDDEN:禁用;WAITING:待审核;REFUSE:拒绝;DELETE:已删除}
     */
    @ApiModelProperty(value = "状态")
    private TenantStatus status;

    /**
     * 内置
     */
    @ApiModelProperty(value = "内置")
    private Boolean readonly;

    /**
     * 责任人
     */
    @ApiModelProperty(value = "责任人")
    @Size(max = 50, message = "责任人长度不能超过50")
    private String duty;

    /**
     * 有效期
     * 为空表示永久
     */
    @ApiModelProperty(value = "有效期")
    private LocalDateTime expirationTime;

    /**
     * 企业简介
     */
    @ApiModelProperty(value = "描述")
    @Size(max = 500, message = "描述长度不能超过500")
    private String description;

}
