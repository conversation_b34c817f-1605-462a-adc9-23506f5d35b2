package com.jettech.jettong.base.dto.rbac.role;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 角色权限保存对象
 *
 * <AUTHOR>
 * @version 1.0
 * @description 角色权限保存对象
 * @projectName jettong
 * @package com.jettech.jettong.base.dto.rbac.role
 * @className RoleAuthoritySaveDTO
 * @date 2021/9/15 9:43
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "RoleAuthoritySaveDTO", description = "角色的资源")
public class RoleAuthoritySaveDTO implements Serializable
{

    private static final long serialVersionUID = 1L;

    /**
     * 菜单ID
     * #sys_menu
     */
    @ApiModelProperty(value = "菜单ID")
    private List<Long> menuIdList;

    /**
     * 功能id
     * #sys_function
     */
    @ApiModelProperty(value = "功能ID")
    private List<Long> functionIdList;
    /**
     * 角色id
     * #sys_role
     */
    @ApiModelProperty(value = "角色id")
    @NotNull(message = "角色id不能为空")
    private Long roleId;

}
