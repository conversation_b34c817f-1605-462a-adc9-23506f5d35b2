package com.jettech.jettong.base.dto.rbac.user;

import com.jettech.jettong.base.entity.rbac.user.User;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * 用户角色对象
 *
 * <AUTHOR>
 * @version 1.0
 * @description 用户角色对象
 * @projectName jettong
 * @package com.jettech.jettong.base.dto.rbac.user
 * @className UserRoleDTO
 * @date 2021/9/15 9:43
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "UserRoleDTO", description = "用户角色DTO")
public class UserRoleDTO implements Serializable
{
    @ApiModelProperty(value = "用户id")
    private List<Long> idList;

    @ApiModelProperty(value = "用户信息")
    private List<User> userList;

}
