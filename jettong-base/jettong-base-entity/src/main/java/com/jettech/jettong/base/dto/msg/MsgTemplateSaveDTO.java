package com.jettech.jettong.base.dto.msg;

import com.jettech.jettong.base.entity.msg.MsgEvent;
import com.jettech.jettong.common.event.enumeration.MsgEngineType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * 消息通知模板信息新增实体类
 * <AUTHOR>
 * @version 1.0
 * @description 消息通知模板信息新增实体类
 * @projectName jettong
 * @package com.jettech.jettong.base.base.dto.msg
 * @className MsgTemplateSaveDTO
 * @date 2023-05-24
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "MsgTemplateSaveDTO", description = "消息通知模板信息")
public class MsgTemplateSaveDTO implements Serializable
{

    private static final long serialVersionUID = 1L;

    /**
     * 模板名称
     */
    @ApiModelProperty(value = "模板名称")
    @NotEmpty(message = "请填写模板名称")
    @Size(max = 200, message = "模板名称长度不能超过200")
    private String name;
    /**
     * 模板描述
     */
    @ApiModelProperty(value = "模板描述")
    @Size(max = 1000, message = "模板描述长度不能超过1000")
    private String description;
    /**
     * 是否内置
     */
    @ApiModelProperty(value = "是否内置")
    @NotNull(message = "请填写是否内置")
    private Boolean readonly;
    /**
     * 模板标题
     */
    @ApiModelProperty(value = "模板标题")
    @NotEmpty(message = "请填写模板标题")
    @Size(max = 1000, message = "模板标题长度不能超过1000")
    private String title;
    /**
     * 模板内容
     */
    @ApiModelProperty(value = "模板内容")
    @NotEmpty(message = "请填写模板内容")
    @Size(max = 65535, message = "模板内容长度不能超过65,535")
    private String body;

    /**
     * 关联引擎类型
     */
    @ApiModelProperty(value = "关联引擎类型")
    private List<MsgEngineType> typeList;

    /**
     * 关联事件
     */
    @ApiModelProperty(value = "关联事件")
    private List<MsgEvent> eventList;

}
