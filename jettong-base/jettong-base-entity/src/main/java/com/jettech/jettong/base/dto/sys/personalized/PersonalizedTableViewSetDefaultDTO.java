package com.jettech.jettong.base.dto.sys.personalized;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 用户自定义列表过滤器信息设置默认过滤器实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 用户自定义列表过滤器信息设置默认过滤器实体类
 * @projectName jettong
 * @package com.jettech.jettong.base.dto.sys.personalized
 * @className PersonalizedTableViewUpdateDTO
 * @date 2021-11-20
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "PersonalizedTableViewSetDefaultDTO", description = "用户自定义列表视图设置默认视图信息")
public class PersonalizedTableViewSetDefaultDTO implements Serializable
{

    private static final long serialVersionUID = 1L;

    /**
     * 视图id
     */
    @ApiModelProperty(value = "列表视图id")
    private Long id;

    /**
     * 过滤器名称
     */
    @ApiModelProperty(value = "视图名称")
    @NotEmpty(message = "请填写视图名称")
    private String name;

    /**
     * 列表唯一code
     */
    @ApiModelProperty(value = "列表唯一code")
    @NotEmpty(message = "请填写列表唯一code")
    private String tableCode;

    /**
     * 是否内置视图
     */
    @ApiModelProperty(value = "是否内置视图")
    @NotNull(message = "请填写是否内置视图")
    private Boolean isBuiltIn;

    /**
     * 查询条件信息
     */
    @ApiModelProperty(value = "查询条件信息")
    private String search;

    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    private Integer sort;

}
