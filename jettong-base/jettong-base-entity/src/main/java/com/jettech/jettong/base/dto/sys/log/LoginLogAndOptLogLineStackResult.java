package com.jettech.jettong.base.dto.sys.log;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 登录日志及操作日志折线图返回对象
 *
 * <AUTHOR>
 * @version 1.0
 * @description 登录日志及操作日志折线图返回对象
 * @projectName FAW
 * @package com.jettech.jettong.base.dto.sys.log
 * @className LoginLogAndOptLogLineStackResult
 * @date 2023/5/15 15:51
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "LoginLogAndOptLogLineStackResult", description = "登录日志及操作日志折线图返回对象")
public class LoginLogAndOptLogLineStackResult implements Serializable
{

    private static final long serialVersionUID = 1L;

    /**
     * 日期
     */
    @ApiModelProperty(value = "日期集合")
    private String[] date;

    /**
     * 登录次数 UV
     */
    @ApiModelProperty(value = "访客数 UV")
    private Integer[] loginCount;

    /**
     * 访问次数
     */
    @ApiModelProperty(value = "访问次数")
    private Integer[] optCount;

    @Builder
    public LoginLogAndOptLogLineStackResult(String[] date, Integer[] loginCount, Integer[] optCount)
    {
        this.date = date;
        this.loginCount = loginCount;
        this.optCount = optCount;
    }
}
