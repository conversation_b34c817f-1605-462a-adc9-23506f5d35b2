package com.jettech.jettong.base.dto.sys.personalized;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 筛选器信息分页实体类
 * <AUTHOR>
 * @version 1.0
 * @description 筛选器信息分页实体类
 * @projectName jettong
 * @package com.jettech.jettong.base.base.dto.sys
 * @className TableView
 * @date 2023-03-23
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "TableViewPageQuery", description = "筛选器信息")
public class TableViewPageQuery implements Serializable
{

    private static final long serialVersionUID = 1L;

    /**
     * 筛选器表
     */
    @ApiModelProperty(value = "筛选器表")
    private String tableType;

    /**
     * 筛选器类型
     */
    @ApiModelProperty(value = "筛选器类型")
    private String type;

    /**
     * 筛选器范围，0-共用，1-项目下全部成员可用，2-私有
     */
    @ApiModelProperty(value = "筛选器范围，0-共用，1-项目下全部成员可用，2-私有")
    private Integer scope;
    /**
     * 所属项目ID
     */
    @ApiModelProperty(value = "所属项目ID")
    private Long ownerProject;
    /**
     * 所属用户ID
     */
    @ApiModelProperty(value = "所属用户ID")
    private Long ownerUser;
    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    private String name;
    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    private String description;
    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    private Integer sort;
    /**
     * 搜索条件
     */
    @ApiModelProperty(value = "搜索条件")
    private String search;

}
