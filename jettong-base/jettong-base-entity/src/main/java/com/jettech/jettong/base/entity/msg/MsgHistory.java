package com.jettech.jettong.base.entity.msg;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jettech.basic.annotation.echo.Echo;
import com.jettech.basic.base.entity.SuperEntity;
import com.jettech.basic.model.EchoVO;
import com.jettech.jettong.base.entity.rbac.user.User;
import com.jettech.jettong.common.event.enumeration.MsgEngineType;
import com.jettech.jettong.common.event.enumeration.MsgEventType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.baomidou.mybatisplus.annotation.SqlCondition.LIKE;
import static com.jettech.jettong.common.constant.BaseEchoConstants.USER_ID_CLASS;


/**
 * 消息通知发送历史信息实体类
 * <AUTHOR>
 * @version 1.0
 * @description 消息通知发送历史信息实体类
 * @projectName jettong
 * @package com.jettech.jettong.base.entity.msg
 * @className MsgHistory
 * @date 2023-06-07
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("sys_msg_history")
@ApiModel(value = "MsgHistory", description = "消息通知发送历史信息")
@AllArgsConstructor
public class MsgHistory extends SuperEntity<Long> implements EchoVO
{

    private static final long serialVersionUID = 1L;
    @TableField(exist = false)
    private Map<String, Object> echoMap = new HashMap<>();
    /**
     * 引擎类型枚举
     */
    @ApiModelProperty(value = "引擎类型枚举")
    @NotEmpty(message = "请填写引擎类型枚举")
    @Size(max = 50, message = "引擎类型枚举长度不能超过50")
    @TableField(value = "`msg_engine_type`", condition = LIKE)
    private MsgEngineType msgEngineType;

    /**
     * 事件类型枚举
     */
    @ApiModelProperty(value = "事件类型枚举")
    @NotEmpty(message = "请填写事件类型枚举")
    @Size(max = 50, message = "事件类型枚举长度不能超过50")
    @TableField(value = "`msg_event_type`", condition = LIKE)
    private MsgEventType msgEventType;

    /**
     * 消息标题
     */
    @ApiModelProperty(value = "消息标题")
    @Size(max = 4000, message = "消息标题长度不能超过4000")
    @TableField(value = "`title`", condition = LIKE)
    private String title;

    /**
     * 消息内容
     */
    @ApiModelProperty(value = "消息内容")
    @TableField(value = "`body`", condition = LIKE)
    private String body;

    @ApiModelProperty(value = "发送结果，为空时表示发送成功，否则为失败原因")
    @TableField(value = "`result`")
    private String result;

    @ApiModelProperty("创建人ID")
    @TableField(value = "created_by", fill = FieldFill.INSERT)
    @Echo(api = USER_ID_CLASS, beanClass = User.class)
    protected Long createdBy;

    /**
     * 消息接收人id
     */
    @ApiModelProperty(value = "消息接收人id")
    @TableField(exist = false)
    private List<Long> receiverIds;

    /**
     * 是否已读
     */
    @ApiModelProperty(value = "是否已读")
    @TableField(exist = false)
    private Boolean isRead;

    @Builder
    public MsgHistory(Long id, Long createdBy, LocalDateTime createTime, List<Long> receiverIds, Boolean isRead,
            MsgEngineType msgEngineType, MsgEventType msgEventType, String title, String body, String result)
    {
        this.id = id;
        this.createdBy = createdBy;
        this.createTime = createTime;
        this.msgEngineType = msgEngineType;
        this.msgEventType = msgEventType;
        this.title = title;
        this.body = body;
        this.result = result;
        this.receiverIds = receiverIds;
        this.isRead = isRead;
    }

}
