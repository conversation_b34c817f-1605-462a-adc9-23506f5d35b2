package com.jettech.jettong.base.controller.rbac.tenant;

import com.jettech.basic.annotation.log.SysLog;
import com.jettech.basic.annotation.security.PreAuth;
import com.jettech.basic.base.R;
import com.jettech.basic.base.controller.SuperSimpleController;
import com.jettech.basic.database.mybatis.conditions.Wraps;
import com.jettech.basic.log.entity.OptLogTypeEnum;
import com.jettech.basic.utils.BeanPlusUtil;
import com.jettech.jettong.base.dto.rbac.tenant.TenantPlatformConfigSaveDTO;
import com.jettech.jettong.base.dto.rbac.tenant.TenantPlatformConfigUpdateDTO;
import com.jettech.jettong.base.entity.rbac.tenant.TenantPlatformConfig;
import com.jettech.jettong.base.service.rbac.tenant.TenantPlatformConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * 平台配置信息（如：logo，名称，版权信息等）控制器
 *
 * <AUTHOR>
 * @version 1.0
 * @description 平台配置信息（如：logo，名称，版权信息等）控制器
 * @projectName jettong
 * @package com.jettech.jettong.base.controller
 * @className TenantPlatformConfigController
 * @date 2021-11-20
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/tenantPlatformConfig")
@Api(value = "TenantPlatformConfig", tags = "租户平台配置信息（如：logo，名称，版权信息等）")
@PreAuth(replace = "base:platformConfig:")
public class TenantPlatformConfigController extends
        SuperSimpleController<TenantPlatformConfigService, TenantPlatformConfig>
{

    @ApiOperation(value = "批量新增", notes = "批量新增")
    @PostMapping("/batch")
    @SysLog(value = "批量新增", optType = OptLogTypeEnum.ADD)
    @PreAuth("hasAnyPermission('{}add')")
    public R<List<TenantPlatformConfig>> saveBatch(@Validated @RequestBody List<TenantPlatformConfigSaveDTO> models)
    {
        List<TenantPlatformConfig> tenantPlatformConfigs = BeanPlusUtil.toBeanList(models, TenantPlatformConfig.class);
        baseService.saveBatch(tenantPlatformConfigs);
        return success(tenantPlatformConfigs);
    }

    @ApiOperation(value = "批量修改", notes = "批量修改")
    @PutMapping("/batch")
    @SysLog(value = "批量修改", optType = OptLogTypeEnum.EDIT)
    @PreAuth("hasAnyPermission('{}edit')")
    public R<List<TenantPlatformConfig>> updateBatch(@Validated @RequestBody List<TenantPlatformConfigUpdateDTO> models)
    {
        List<TenantPlatformConfig> tenantPlatformConfigs = BeanPlusUtil.toBeanList(models, TenantPlatformConfig.class);
        baseService.updateBatch(tenantPlatformConfigs);
        return success(tenantPlatformConfigs);
    }

    @ApiOperation(value = "删除", notes = "删除")
    @DeleteMapping
    @SysLog(value = "删除", optType = OptLogTypeEnum.DELETE)
    @PreAuth("hasAnyPermission('{}delete')")
    public R<Boolean> deleteByIds(@RequestBody List<Long> ids)
    {
        baseService.removeByIds(ids);
        return success();
    }

    @ApiOperation(value = "根据主键ID查询", notes = "根据主键ID查询")
    @GetMapping("/findById/{id}")
    @SysLog(value = "根据主键ID查询")
    @PreAuth("hasAnyPermission('{}view')")
    public R<TenantPlatformConfig> getById(@PathVariable Long id)
    {
        return success(baseService.getById(id));
    }

    @ApiOperation(value = "根据Code查询", notes = "根据Code查询")
    @GetMapping("/findByCode/{code}")
    @SysLog(value = "根据Code查询")
    @PreAuth("hasAnyPermission('{}view')")
    public R<TenantPlatformConfig> getByCode(@PathVariable String code)
    {
        return success(
                baseService.getOne(Wraps.<TenantPlatformConfig>lbQ().eq(TenantPlatformConfig::getCode, code), false));
    }

    @ApiOperation(value = "查询所有配置", notes = "查询所有配置")
    @GetMapping
    @SysLog(value = "查询所有配置")
    @PreAuth("hasAnyPermission('{}view')")
    public R<List<TenantPlatformConfig>> getAll()
    {
        return success(baseService.list());
    }

    @ApiOperation(value = "新增租户平台配置信息", notes = "新增租户平台配置信息")
    @PostMapping("/save")
    @SysLog(value = "新增租户平台配置信息", optType = OptLogTypeEnum.ADD)
    @PreAuth("hasAnyPermission('{}add')")
    public R<TenantPlatformConfig> save(@Validated @RequestBody TenantPlatformConfigUpdateDTO models)
    {
        TenantPlatformConfig tenantPlatformConfigs = BeanPlusUtil.toBean(models, TenantPlatformConfig.class);

        List<TenantPlatformConfig> list = baseService.list(Wraps.<TenantPlatformConfig>lbQ()
                .eq(TenantPlatformConfig::getCode, tenantPlatformConfigs.getCode()));
        if(list.size() == 1){
            tenantPlatformConfigs.setId(list.get(0).getId());
            baseService.updateAllById(tenantPlatformConfigs);
        }
        return success(tenantPlatformConfigs);
    }

}
