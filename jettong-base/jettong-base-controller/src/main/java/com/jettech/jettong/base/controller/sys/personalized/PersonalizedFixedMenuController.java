package com.jettech.jettong.base.controller.sys.personalized;

import com.jettech.basic.annotation.log.SysLog;
import com.jettech.basic.base.R;
import com.jettech.basic.base.controller.SuperSimpleController;
import com.jettech.basic.database.mybatis.conditions.Wraps;
import com.jettech.basic.database.mybatis.conditions.query.LbqWrapper;
import com.jettech.basic.log.entity.OptLogTypeEnum;
import com.jettech.basic.utils.BeanPlusUtil;
import com.jettech.jettong.base.entity.sys.personalized.PersonalizedFixedMenu;
import com.jettech.jettong.base.service.sys.personalized.PersonalizedFixedMenuService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 用户自定义固定菜单信息控制器
 *
 * <AUTHOR>
 * @version 1.0
 * @description 用户自定义固定菜单信息控制器
 * @projectName jettong
 * @package com.jettech.jettong.base.controller
 * @className PersonalizedFixedMenuController
 * @date 2021-11-29
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/base/personalizedFixedMenu")
@Api(value = "PersonalizedFixedMenu", tags = "用户自定义固定菜单信息")
public class PersonalizedFixedMenuController
        extends SuperSimpleController<PersonalizedFixedMenuService, PersonalizedFixedMenu>
{

    @ApiOperation(value = "批量修改", notes = "批量修改")
    @PutMapping
    @SysLog(value = "批量修改", optType = OptLogTypeEnum.EDIT)
    public R<Boolean> handlerUpdate(@RequestBody @Validated List<PersonalizedFixedMenu> models)
    {
        if (models.isEmpty())
        {
            return success();
        }
        if (null == getUserId())
        {
            return validFail("未获取到当前登录用户信息");
        }
        List<PersonalizedFixedMenu> personalizedFixedMenus =
                BeanPlusUtil.toBeanList(models, PersonalizedFixedMenu.class);
        personalizedFixedMenus.forEach(item -> item.setId(null));
        baseService.batchUpdate(personalizedFixedMenus, getUserId());
        return success();
    }

    @ApiOperation(value = "查询当前登录用户自定义固定列", notes = "查询当前登录用户自定义固定列")
    @GetMapping("/find")
    @SysLog(value = "查询当前登录用户自定义固定列")
    public R<List<PersonalizedFixedMenu>> find()
    {
        if (null == getUserId())
        {
            return validFail("未获取到当前登录用户信息");
        }
        LbqWrapper<PersonalizedFixedMenu> lbqWrapper = Wraps.<PersonalizedFixedMenu>lbQ()
                .eq(PersonalizedFixedMenu::getUserId, getUserId())
                .orderByAsc(PersonalizedFixedMenu::getSort);

        return success(baseService.list(lbqWrapper));
    }
}
