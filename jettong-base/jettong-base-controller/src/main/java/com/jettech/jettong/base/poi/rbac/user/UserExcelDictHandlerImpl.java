package com.jettech.jettong.base.poi.rbac.user;

import cn.afterturn.easypoi.handler.inter.IExcelDictHandler;
import com.jettech.basic.database.mybatis.conditions.Wraps;
import com.jettech.jettong.base.entity.rbac.org.Org;
import com.jettech.jettong.base.entity.rbac.role.Role;
import com.jettech.jettong.base.service.rbac.org.OrgService;
import com.jettech.jettong.base.service.rbac.role.RoleService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 用户导出字典处理器
 *
 * <AUTHOR>
 * @version 1.0
 * @description 用户导出字典处理器
 * @projectName jettong
 * @package com.jettech.jettong.base.poi.rbac.user
 * @className UserExcelDictHandlerImpl
 * @date 2021/9/15 9:43
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Component
@RequiredArgsConstructor
public class UserExcelDictHandlerImpl implements IExcelDictHandler
{
    public static final String ORG = "org";
    public static final String ROLE = "role";
    private final OrgService orgService;
    private final RoleService roleService;

    /**
     * 平台所有机构id和名称map
     */
    private static final ThreadLocal<Map<String, String>> ORG_ID_NAME = new ThreadLocal<>();

    /**
     * 平台所有机构名称和id map
     */
    private static final ThreadLocal<Map<String, String>> ORG_NAME_ID = new ThreadLocal<>();

    /**
     * 平台所有角色id和名称map
     */
    private static final ThreadLocal<Map<String, String>> ROLE_ID_NAME = new ThreadLocal<>();
    /**
     * 平台所有角色名称和id map
     */
    private static final ThreadLocal<Map<String, String>> ROLE_NAME_ID = new ThreadLocal<>();

    @Override
    public String toName(String dict, Object obj, String name, Object value)
    {
        if (value == null)
        {
            return null;
        }
        if (ORG.equals(dict))
        {
            Map<String, String> orgMap = ORG_ID_NAME.get();
            if (orgMap == null)
            {
                orgMap = getOrgIdName();
                ORG_ID_NAME.set(orgMap);
            }
            return orgMap.getOrDefault(String.valueOf(value), value.toString());
        }
        if (ROLE.equals(dict))
        {
            Map<String, String> roleMap = ROLE_ID_NAME.get();
            if (roleMap == null)
            {
                roleMap = getRoleIdName();
                ROLE_ID_NAME.set(roleMap);
            }
            String val = String.valueOf(value);
            String[] ids = val.split(";");
            String[] names = new String[ids.length];
            for (int i = 0; i < ids.length; i++)
            {
                names[i] = roleMap.getOrDefault(val, ids[i]);
            }
            return String.join(";", names);
        }
        return String.valueOf(value);
    }

    @Override
    public String toValue(String dict, Object obj, String name, Object value)
    {
        if (value == null)
        {
            return null;
        }

        if (ORG.equals(dict))
        {
            Map<String, String> orgMap = ORG_NAME_ID.get();
            if (orgMap == null)
            {
                orgMap = getOrgNameId();
                ORG_NAME_ID.set(orgMap);
            }
            return orgMap.getOrDefault(String.valueOf(value), "");
        }
        if (ROLE.equals(dict))
        {
            Map<String, String> roleMap = ROLE_NAME_ID.get();
            if (roleMap == null)
            {
                roleMap = getRoleNameId();
                ROLE_NAME_ID.set(roleMap);
            }
            String val = String.valueOf(value);
            String[] names = val.split(";");
            String[] ids = new String[names.length];
            for (int i = 0; i < names.length; i++)
            {
                ids[i] = roleMap.getOrDefault(names[i], "");
            }
            if (ids.length == 0)
            {
                return "";
            }

            return String.join(";", ids);
        }

        return value.toString();
    }

    /**
     * 获取机构id->name map
     *
     * @return Map<String, String> 机构id->name map
     * <AUTHOR>
     * @date 2021/12/10 11:42
     * @update zxy 2021/12/10 11:42
     * @since 1.0
     */
    private Map<String, String> getOrgIdName()
    {
        List<Org> orgs = orgService.list();
        return orgs.stream().collect(Collectors.toMap(item -> String.valueOf(item.getId()), Org::getName));
    }

    /**
     * 获取机构name->id map
     *
     * @return Map<String, String> 机构name->id map
     * <AUTHOR>
     * @date 2021/12/10 11:42
     * @update zxy 2021/12/10 11:42
     * @since 1.0
     */
    private Map<String, String> getOrgNameId()
    {
        List<Org> orgs = orgService.list(Wraps.<Org>lbQ().select(Org::getId, Org::getName).ne(Org::getState, 3));
        return orgs.stream().collect(Collectors.toMap(Org::getName, item -> String.valueOf(item.getId()), (entity1, entity2) -> entity1));
    }


    /**
     * 获取角色id->name map
     *
     * @return Map<String, String> 角色id->name map
     * <AUTHOR>
     * @date 2021/12/10 11:42
     * @update zxy 2021/12/10 11:42
     * @since 1.0
     */
    private Map<String, String> getRoleIdName()
    {
        List<Role> roles = roleService.list();
        return roles.stream().collect(Collectors.toMap(item -> String.valueOf(item.getId()), Role::getName));
    }

    /**
     * 获取角色name->id map
     *
     * @return Map<String, String> 角色name->id map
     * <AUTHOR>
     * @date 2021/12/10 11:42
     * @update zxy 2021/12/10 11:42
     * @since 1.0
     */
    private Map<String, String> getRoleNameId()
    {
        List<Role> roles = roleService.list();
        return roles.stream().collect(Collectors.toMap(Role::getName, item -> String.valueOf(item.getId())));
    }

    /**
     * 清理ThreadLocal
     *
     * <AUTHOR>
     * @date 2021/12/10 11:50
     * @update zxy 2021/12/10 11:50
     * @since 1.0
     */
    public void removeThreadLocal()
    {
        ORG_ID_NAME.remove();
        ORG_NAME_ID.remove();

        ROLE_ID_NAME.remove();
        ROLE_NAME_ID.remove();
    }
}
