package com.jettech.jettong.base.controller.file;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jettech.basic.annotation.log.SysLog;
import com.jettech.basic.annotation.security.PreAuth;
import com.jettech.basic.base.R;
import com.jettech.basic.base.controller.QueryController;
import com.jettech.basic.base.controller.SuperSimpleController;
import com.jettech.basic.base.request.PageParams;
import com.jettech.basic.database.mybatis.conditions.Wraps;
import com.jettech.basic.database.mybatis.conditions.query.LbqWrapper;
import com.jettech.basic.echo.core.EchoService;
import com.jettech.basic.exception.BizException;
import com.jettech.basic.log.entity.OptLogTypeEnum;
import com.jettech.basic.utils.ArgumentAssert;
import com.jettech.jettong.base.entity.file.File;
import com.jettech.jettong.base.entity.sys.personalized.PersonalizedTableView;
import com.jettech.jettong.base.service.file.FileService;
import com.jettech.jettong.base.service.sys.personalized.PersonalizedTableViewService;
import com.jettech.jettong.base.vo.file.param.FileParamVO;
import com.jettech.jettong.base.vo.file.param.FileUploadVO;
import com.jettech.jettong.base.vo.file.result.FileResultVO;
import com.jettech.jettong.base.vo.file.result.OnlyOfficeResultVO;
import com.jettech.jettong.common.util.converter.SizeConverterUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.PrintWriter;
import java.net.URL;
import java.util.ArrayList;
import java.util.List;
import java.util.Scanner;

import static com.jettech.basic.exception.code.ExceptionCode.BASE_VALID_PARAM;
import static com.jettech.jettong.common.constant.SwaggerConstants.*;

/**
 * 文件上传控制器
 *
 * <AUTHOR>
 * @version 1.0
 * @description 文件上传控制器
 * @projectName jettong
 * @package com.jettech.jettong.base.controller.file
 * @className FileController
 * @date 2021/9/15 9:43
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/base/file")
@Api(value = "FileFileController", tags = "文件管理")
@RequiredArgsConstructor
public class FileController extends SuperSimpleController<FileService, File>
        implements QueryController<File, Long, FileParamVO>
{

    /**
     * 是否开启office在线预览/编辑，默认不开启
     */
    @Value("${jettong.file.office.edit:false}")
    private Boolean onlyofficeEdit;

    /**
     * OnlyOffice访问地址，默认为null
     */
    @Value("${jettong.file.office.onlyoffice.url:null}")
    private String onlyofficeUrl;

    /**
     * 文件上传大小限制
     */
    @Value("${spring.servlet.multipart.max-file-size:5MB}")
    private String fileMaxSize;

    private final EchoService echoService;
    private final PersonalizedTableViewService tableViewService;

    /**
     * 上传文件
     */
    @ApiOperation(value = "附件上传", notes = "附件上传")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "file", value = "附件", dataType = DATA_TYPE_MULTIPART_FILE, allowMultiple = true,
                    required = true),
    })
    @PostMapping(value = "/anyone/upload")
    @SysLog(value = "上传附件", optType = OptLogTypeEnum.UPLOAD)
    public R<FileResultVO> upload(@RequestParam(value = "file") MultipartFile file,
            @Validated FileUploadVO attachmentVO)
    {
        // 忽略路径字段,只处理文件类型
        if (file.isEmpty())
        {
            return R.validFail(BASE_VALID_PARAM.build("请上传有效文件"));
        }

        // 判断文件大小，超过限制不支持上传
        if (SizeConverterUtil.convertToBytes(fileMaxSize) < file.getSize())
        {
            return validFail("文件超过" + fileMaxSize + ", 不支持上传");
        }

        return R.success(baseService.upload(file, attachmentVO));
    }

    @ApiOperation(value = "删除")
    @DeleteMapping
    @SysLog(value = "'删除:' + #ids", optType = OptLogTypeEnum.DELETE)
    @PreAuth("hasAnyPermission('{}delete')")
    public R<Boolean> handlerDelete(@RequestBody List<Long> ids)
    {
        return success(baseService.removeFileByIds(ids));
    }

    @ApiOperation(value = "根据文件id打包下载", notes = "根据附件id下载多个打包的附件")
    @PostMapping(value = "/download", produces = "application/octet-stream")
    @SysLog(value = "下载附件", optType = OptLogTypeEnum.DOWNLOAD)
    public void download(@RequestBody List<Long> ids, HttpServletRequest request, HttpServletResponse response)
            throws Exception
    {
        ArgumentAssert.notEmpty(ids, "请选择至少一个附件");
        baseService.download(request, response, ids);
    }

    @ApiOperation(value = "根据文件id获取文件流，用于下载文件或在线预览文件", notes = "根据文件id获取文件流，用于下载文件或在线预览文件")
    @GetMapping(value = "/noToken/download/{id}", produces = "application/octet-stream")
    @SysLog(value = "下载或预览附件", optType = OptLogTypeEnum.DOWNLOAD)
    public void download(@PathVariable Long id, HttpServletRequest request, HttpServletResponse response)
            throws Exception
    {
        List<Long> ids = new ArrayList<>();
        ids.add(id);
        ArgumentAssert.notEmpty(ids, "请选择至少一个附件");
        baseService.download(request, response, ids);
    }

    @ApiOperation(value = "获取OnlyOffice配置", notes = "获取OnlyOffice配置")
    @GetMapping(value = "/onlyoffice/config")
    @SysLog(value = "获取OnlyOffice配置", optType = OptLogTypeEnum.DOWNLOAD)
    public R<OnlyOfficeResultVO> getOnlyOffice()
    {
        return success(OnlyOfficeResultVO.builder().edit(onlyofficeEdit).url(onlyofficeUrl)
                .build());
    }

    @PostMapping("/noToken/save/{id}/{sessionUserId}")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "文件id", dataType = DATA_TYPE_LONG, paramType = PARAM_TYPE_PATH),
            @ApiImplicitParam(name = "sessionUserId", value = "当前登录用户id", dataType = DATA_TYPE_LONG,
                    paramType = PARAM_TYPE_PATH)
    })
    @ApiOperation(value = "office文件在线编辑后保存接口", notes = "office文件在线编辑后保存接口")
    @SysLog(value = "文件保存", optType = OptLogTypeEnum.EDIT)
    public void updateFile(@PathVariable("id") Long id, @PathVariable("sessionUserId") Long sessionUserId,
            HttpServletRequest request, HttpServletResponse response)
    {
        PrintWriter writer = null;
        String body = "";
        try
        {
            writer = response.getWriter();
            Scanner scanner = new Scanner(request.getInputStream());
            scanner.useDelimiter("\\A");
            body = scanner.hasNext() ? scanner.next() : "";
            scanner.close();
        }
        catch (Exception ex)
        {
            log.error(ex.getMessage(), ex);
            if (writer != null)
            {
                throw new BizException("get request.getInputStream error:" + ex.getMessage());
            }
            return;
        }

        if (body.isEmpty())
        {
            throw new BizException("ONLYOFFICE回调保存请求体为空");
        }

        JSONObject jsonObj = JSONObject.parseObject(body);
        int status = (Integer) jsonObj.get("status");
        int saved = 0;
        //MustSave, Corrupted
        if (status == 2 || status == 3 || status == 6)
        {
            String downloadUri = (String) jsonObj.get("url");
            try
            {
                String filePath = "tempfiles/onlyoffice/savedownload/";
                java.io.File file = new java.io.File(filePath);
                FileUtils.copyURLToFile(new URL(downloadUri), file);
                baseService.updateFile(id, sessionUserId, file);
            }
            catch (Exception ex)
            {
                saved = 1;
                log.error(ex.getMessage(), ex);
            }
        }
        writer.write("{\"error\":" + saved + "}");
    }

    @Override
    public IPage<File> query(PageParams<FileParamVO> params)
    {
        IPage<File> page = params.buildPage(File.class);
        FileParamVO paramsModel = params.getModel();

        LbqWrapper<File> wrapper = Wraps.<File>lbQ()
                .eq(File::getBizType, paramsModel.getBizType())
                .eq(File::getBizId, paramsModel.getBizId())
                .eq(File::getFileType, paramsModel.getFileType())
                .eq(File::getStorageType, paramsModel.getStorageType())
                .eq(File::getBucket, paramsModel.getBucket())
                .like(File::getOriginalFileName, paramsModel.getOriginalFileName())
                .like(File::getContentType, paramsModel.getContentType())
                .like(File::getPath, paramsModel.getPath())
                .like(File::getUniqueFileName, paramsModel.getUniqueFileName())
                .like(File::getFileMd5, paramsModel.getFileMd5())
                .like(File::getSuffix, paramsModel.getSuffix());

        baseService.page(page, wrapper);

        // 手动注入
        echoService.action(page);

        // 保存最后一次查询
        PersonalizedTableView lastedView = PersonalizedTableView.lastView(getUserId(), "fileManageTable", params);
        tableViewService.saveLastUseFilter(lastedView);

        return page;
    }

}
