package com.jettech.jettong.base.poi.rbac.user;

import cn.afterturn.easypoi.excel.entity.result.ExcelVerifyHandlerResult;
import cn.afterturn.easypoi.handler.inter.IExcelVerifyHandler;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import com.jettech.basic.cache.repository.CacheOps;
import com.jettech.basic.database.mybatis.conditions.Wraps;
import com.jettech.jettong.base.entity.rbac.user.User;
import com.jettech.jettong.base.service.rbac.user.UserService;
import com.jettech.jettong.base.vo.rbac.user.UserExcelVO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 用户导入验证器，该方法中不要使用大批量的查询数据库操作
 *
 * <AUTHOR>
 * @version 1.0
 * @description 用户导入验证器
 * @projectName jettong
 * @package com.jettech.jettong.base.poi.rbac.user
 * @className UserExcelVerifyHandlerImpl
 * @date 2021/9/15 9:43
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Component
@RequiredArgsConstructor
public class UserExcelVerifyHandlerImpl implements IExcelVerifyHandler<UserExcelVO>
{

    private final UserService userService;
    private final CacheOps cacheOps;

    /**
     * 用户姓名正则
     */
    private final String USER_NAME_REGEX = "[a-zA-Z0-9_\\u4e00-\\u9fa5]{1,50}";

    /**
     * 用户账号正则
     */
    private final String USER_ACCOUNT_REGEX = "[A-Za-z0-9_\\.\\@]{1,50}";

    /**
     * 用户手机号正则
     */
    private final String USER_MOBILE_REGEX = "0?(13|14|16|15|17|18|19)[0-9]{9}";

    /**
     * 邮箱正则
     */
    private final String USER_EMAIL_REGEX = "\\w[-\\w.+]*@([A-Za-z0-9][-A-Za-z0-9]+\\.)+[A-Za-z]{2,14}";

    /**
     * 平台所有用户Account信息
     */
    private static final ThreadLocal<List<String>> ACCOUNT_LIST = new ThreadLocal<>();

    /**
     * 平台所有用户手机号
     */
    private static final ThreadLocal<List<String>> MOBILE_LIST = new ThreadLocal<>();

    @Override
    public ExcelVerifyHandlerResult verifyHandler(UserExcelVO obj)
    {
        StringBuilder builder = new StringBuilder();
        boolean bool = true;

        // 校验姓名
        if (StrUtil.isEmpty(obj.getName()))
        {
            builder.append("姓名不能为空");
            bool = false;
        }
        else if (!ReUtil.isMatch(USER_NAME_REGEX, obj.getName()))
        {
            builder.append("姓名只能由1到50位中文、字母、数字或下划线组成");
            bool = false;
        }

        // 校验登录账号
        if (StrUtil.isEmpty(obj.getAccount()))
        {
            if (builder.length() != 0)
            {
                builder.append("\n");
            }
            builder.append("登录账号不能为空");
            bool = false;
        }
        else if (!ReUtil.isMatch(USER_ACCOUNT_REGEX, obj.getAccount()))
        {

            if (builder.length() != 0)
            {
                builder.append("\n");
            }
            builder.append("登录账号只能由不超过50位的字母、数字、下划线、.或@组成");
            bool = false;
        }
        else
        {
            List<String> accounts = ACCOUNT_LIST.get();
            if (accounts == null)
            {
                accounts = getAccount();
                ACCOUNT_LIST.set(accounts);
            }
            if (accounts.contains(obj.getAccount()))
            {
                if (builder.length() != 0)
                {
                    builder.append("\n");
                }
                builder.append("登录账号已存在");
                bool = false;
            }
        }

        // 校验手机号
        if (StrUtil.isEmpty(obj.getMobile()))
        {
            if (builder.length() != 0)
            {
                builder.append("\n");
            }
            builder.append("手机号不能为空");
            bool = false;
        }
        else
        {
            if (!ReUtil.isMatch(USER_MOBILE_REGEX, obj.getMobile()))
            {
                if (builder.length() != 0)
                {
                    builder.append("\n");
                }
                builder.append("手机号不合法");
                bool = false;
            }
            List<String> mobiles = MOBILE_LIST.get();
            if (mobiles == null)
            {
                mobiles = getMobile();
                MOBILE_LIST.set(mobiles);
            }

            if (mobiles.contains(obj.getMobile()))
            {
                if (builder.length() != 0)
                {
                    builder.append("\n");
                }
                builder.append("手机号已存在");
                bool = false;
            }
        }

        // 校验邮箱
        if (StrUtil.isEmpty(obj.getEmail()))
        {
            if (builder.length() != 0)
            {
                builder.append("\n");
            }
            builder.append("邮箱不能为空");
            bool = false;
        }
        else
        {
            if (!ReUtil.isMatch(USER_EMAIL_REGEX, obj.getEmail()))
            {
                if (builder.length() != 0)
                {
                    builder.append("\n");
                }
                builder.append("邮箱不合法");
                bool = false;
            }
        }

        // 校验状态
        if (obj.getState() == null)
        {
            if (builder.length() != 0)
            {
                builder.append("\n");
            }
            builder.append("用户状态不能为空");
            bool = false;
        }

        // 校验机构
        if (StrUtil.isEmpty(obj.getOrgId()))
        {
            if (builder.length() != 0)
            {
                builder.append("\n");
            }
            builder.append("所属机构不能为空");
            bool = false;
        }

        if (bool)
        {
            ACCOUNT_LIST.get().add(obj.getAccount());
            MOBILE_LIST.get().add(obj.getMobile());
        }
        return new ExcelVerifyHandlerResult(bool, builder.toString());
    }

    /**
     * 获取所有用户账号
     *
     * @return List<String> 用户账号
     * <AUTHOR>
     * @date 2021/12/10 11:42
     * @update zxy 2021/12/10 11:42
     * @since 1.0
     */
    private List<String> getAccount()
    {
        return userService.listObjs(Wraps.<User>lbQ().select(User::getAccount), Convert::toStr);
    }

    /**
     * 获取所有用户手机号
     *
     * @return List<String> 用户手机号
     * <AUTHOR>
     * @date 2021/12/10 11:42
     * @update zxy 2021/12/10 11:42
     * @since 1.0
     */
    private List<String> getMobile()
    {
        return userService.listObjs(Wraps.<User>lbQ().select(User::getMobile), Convert::toStr);
    }

    /**
     * 清理ThreadLocal
     *
     * <AUTHOR>
     * @date 2021/12/10 11:50
     * @update zxy 2021/12/10 11:50
     * @since 1.0
     */
    public void removeThreadLocal()
    {
        ACCOUNT_LIST.remove();
        MOBILE_LIST.remove();
    }
}
