package com.jettech.jettong.base.controller.rbac.user;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jettech.basic.annotation.security.PreAuth;
import com.jettech.basic.base.R;
import com.jettech.basic.base.request.PageParams;
import com.jettech.basic.context.ContextUtil;
import com.jettech.jettong.base.dto.rbac.user.Online;
import com.jettech.jettong.base.entity.sys.personalized.PersonalizedTableView;
import com.jettech.jettong.base.service.rbac.user.OnlineService;
import com.jettech.jettong.base.service.sys.personalized.PersonalizedTableViewService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 在线用户处理控制器
 *
 * <AUTHOR>
 * @version 1.0
 * @description 在线用户处理控制器
 * @projectName jettong
 * @package com.jettech.jettong.base.controller.rbac.user
 * @className OnlineController
 * @date 2021/9/15 9:43
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/base/onlineUser")
@Api(value = "OnlineController", tags = "在线用户管理")
@PreAuth(replace = "base:onlineUser:")
@RequiredArgsConstructor
public class BaseOnlineController
{
    private final OnlineService onlineService;
    private final PersonalizedTableViewService tableViewService;

    @PostMapping(value = "/list")
    @PreAuth("hasAnyPermission('{}view')")
    public R<List<Online>> list(@RequestParam(required = false) String name)
    {
        return R.success(onlineService.list(name));
    }

    @PostMapping(value = "/page")
    @PreAuth("hasAnyPermission('{}view')")
    public R<IPage<Online>> page(@RequestBody @Validated PageParams<Online> params)
    {
        List<Online> list = onlineService.list(params.getModel().getName());
        IPage<Online> page = new Page<>(1, list.size(), list.size());
        page.setRecords(list);

        Long userId = ContextUtil.getUserId();

        // 保存最后一次使用筛选记录，并作为默认视图
        tableViewService.saveLastUseFilter(PersonalizedTableView.lastView(userId, "onlineTable", params));

        return R.success(page);
    }

    @ApiOperation(value = "T人", notes = "T人")
    @PostMapping(value = "/t")
    @PreAuth("hasAnyPermission('{}delete')")
    public R<Boolean> logout(String userToken, Long userId, String clientId)
    {
        return R.success(onlineService.clear(userToken, userId, clientId));
    }

}
