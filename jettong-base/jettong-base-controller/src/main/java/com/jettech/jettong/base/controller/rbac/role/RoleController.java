package com.jettech.jettong.base.controller.rbac.role;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jettech.basic.annotation.log.SysLog;
import com.jettech.basic.annotation.security.PreAuth;
import com.jettech.basic.base.R;
import com.jettech.basic.base.controller.SuperCacheController;
import com.jettech.basic.base.request.PageParams;
import com.jettech.basic.database.mybatis.conditions.Wraps;
import com.jettech.basic.database.mybatis.conditions.query.LbqWrapper;
import com.jettech.basic.database.mybatis.conditions.query.QueryWrap;
import com.jettech.basic.echo.core.EchoService;
import com.jettech.basic.utils.BeanPlusUtil;
import com.jettech.jettong.base.dto.rbac.role.*;
import com.jettech.jettong.base.dto.rbac.user.UserRoleSaveDTO;
import com.jettech.jettong.base.entity.rbac.role.Role;
import com.jettech.jettong.base.entity.rbac.role.RoleAuthority;
import com.jettech.jettong.base.entity.sys.personalized.PersonalizedTableView;
import com.jettech.jettong.base.enumeration.rbac.AuthorizeType;
import com.jettech.jettong.base.service.rbac.role.RoleAuthorityService;
import com.jettech.jettong.base.service.rbac.role.RoleService;
import com.jettech.jettong.base.service.sys.personalized.PersonalizedTableViewService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 角色处理控制器
 *
 * <AUTHOR>
 * @version 1.0
 * @description 角色处理控制器
 * @projectName jettong
 * @package com.jettech.jettong.base.controller.rbac.role
 * @className RoleAuthorityController
 * @date 2021/9/15 9:43
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/base/role")
@Api(value = "Role", tags = "角色管理")
@PreAuth(replace = "base:role:")
@RequiredArgsConstructor
public class RoleController
        extends SuperCacheController<RoleService, Long, Role, RolePageQuery, RoleSaveDTO, RoleUpdateDTO>
{

    private final RoleAuthorityService roleAuthorityService;
    private final EchoService echoService;
    private final PersonalizedTableViewService tableViewService;

    @Override
    public R<List<Role>> query(Role data) {
        Long orgId;
        if (null == data.getOrgId())
        {
            orgId = getOrgId();
        }
        else
        {
            orgId = data.getOrgId();
        }
        // 在机构ID单独处理
        data.setOrgId(null);
        QueryWrap<Role> wrapper = Wraps.q(data);

        LbqWrapper<Role> lambda = wrapper.lambda();

        if (orgId == -1)
        {
            // 查询 公共角色
            lambda.isNull(Role::getOrgId);
        }
        else
        {
            lambda.and(w -> w.isNull(Role::getOrgId).or().eq(Role::getOrgId, orgId));
        }

        return success(getBaseService().list(wrapper));
    }

    @Override
    public IPage<Role> query(PageParams<RolePageQuery> params)
    {
        IPage<Role> page = params.buildPage(Role.class);
        RolePageQuery roleQuery = params.getModel();

        QueryWrap<Role> wrap = handlerWrapper(null, params);

        LbqWrapper<Role> wrapper = wrap.lambda();

        wrapper.like(Role::getName, roleQuery.getName())
                .like(Role::getCode, roleQuery.getCode())
                .eq(Role::getState, roleQuery.getState())
                .eq(Role::getReadonly, roleQuery.getReadonly())
                .eq(Role::getDsType, roleQuery.getDsType());
        Long orgId;
        if (null == roleQuery.getOrgId())
        {
            orgId = getOrgId();
        }
        else
        {
            orgId = getOrgId();
        }

        if (orgId == -1)
        {
            wrapper.isNull(Role::getOrgId);
        }
        else
        {
            wrapper.eq(Role::getOrgId, roleQuery.getOrgId());
        }

        baseService.page(page, wrapper);
        echoService.action(page);

        // 保存最后一次使用筛选记录，并作为默认视图
        PersonalizedTableView lastView = PersonalizedTableView.lastView(getUserId(), "baseRoleTable", params);
        tableViewService.saveLastUseFilter(lastView);
        return page;
    }

    @ApiOperation(value = "查询角色", notes = "查询角色")
    @GetMapping("/details")
    @SysLog("查询角色")
    public R<RoleQueryDTO> getDetails(@RequestParam Long id)
    {
        Role role = baseService.getByIdCache(id);
        RoleQueryDTO query = BeanPlusUtil.toBean(role, RoleQueryDTO.class);
        return success(query);
    }

    @ApiOperation(value = "检测角色编码", notes = "检测角色编码")
    @GetMapping("/check")
    @SysLog("检测角色编码")
    public R<Boolean> check(@RequestParam String code)
    {
        return success(baseService.check(code));
    }


    @Override
    public R<Role> handlerSave(RoleSaveDTO data)
    {
        baseService.saveRole(data, getUserId());
        return success(BeanPlusUtil.toBean(data, Role.class));
    }

    @Override
    public R<Role> handlerUpdate(RoleUpdateDTO data)
    {
        baseService.updateRole(data, getUserId());
        return success(BeanPlusUtil.toBean(data, Role.class));
    }

    @Override
    public R<Boolean> handlerDelete(List<Long> ids)
    {
        return success(baseService.removeByIdWithCache(ids));
    }

    @ApiOperation(value = "给用户或团队分配角色", notes = "给用户或团队分配角色")
    @PostMapping("/saveUserRole")
    @SysLog("给角色分配用户")
    @PreAuth("hasAnyPermission('{}config')")
    public R<Boolean> saveUserRole(@RequestBody UserRoleSaveDTO userRole)
    {
        return success(roleAuthorityService.saveUserRole(userRole));
    }

    @ApiOperation(value = "查询角色拥有的资源id集合", notes = "查询角色拥有的资源id集合")
    @GetMapping("/roleAuthorityList")
    @SysLog("查询角色拥有的功能")
    @PreAuth("hasAnyPermission('{}view')")
    public R<RoleAuthoritySaveDTO> findRoleAuthorityListByRoleId(@RequestParam Long roleId,
            @RequestParam(required = false,value = "0") String platform)
    {
        List<RoleAuthority> list =
                roleAuthorityService.list(Wraps.<RoleAuthority>lbQ().eq(RoleAuthority::getRoleId, roleId));
        List<Long> menuIdList = list.stream().filter(item -> AuthorizeType.MENU.eq(item.getAuthorityType()))
                .map(RoleAuthority::getAuthorityId).collect(Collectors.toList());
        List<Long> resourceIdList = list.stream().filter(item -> AuthorizeType.FUNCTION.eq(item.getAuthorityType()))
                .map(RoleAuthority::getAuthorityId).collect(Collectors.toList());
        RoleAuthoritySaveDTO roleAuthority = RoleAuthoritySaveDTO.builder()
                .menuIdList(menuIdList).functionIdList(resourceIdList)
                .build();
        return success(roleAuthority);
    }

    @ApiOperation(value = "给角色配置权限", notes = "给角色配置权限")
    @PostMapping("/saveRoleAuthority")
    @SysLog("给角色配置权限")
    @PreAuth("hasAnyPermission('{}auth')")
    public R<Boolean> saveRoleAuthority(@RequestBody RoleAuthoritySaveDTO roleAuthoritySaveDTO)
    {
        return success(roleAuthorityService.saveRoleAuthority(roleAuthoritySaveDTO));
    }

    @ApiOperation(value = "根据角色编码查询用户ID", notes = "根据角色编码查询用户ID")
    @GetMapping("/codes")
    @SysLog("根据角色编码查询用户ID")
    public R<List<Long>> findUserIdByCode(@RequestParam(value = "codes") String[] codes)
    {
        return success(baseService.findUserIdByCode(codes));
    }

}
