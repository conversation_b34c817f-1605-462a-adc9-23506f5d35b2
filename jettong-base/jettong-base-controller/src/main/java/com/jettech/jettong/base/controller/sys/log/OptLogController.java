package com.jettech.jettong.base.controller.sys.log;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jettech.basic.annotation.log.SysLog;
import com.jettech.basic.annotation.security.PreAuth;
import com.jettech.basic.base.R;
import com.jettech.basic.base.controller.DeleteController;
import com.jettech.basic.base.controller.SuperSimpleController;
import com.jettech.basic.base.request.PageParams;
import com.jettech.basic.database.mybatis.conditions.Wraps;
import com.jettech.basic.database.mybatis.conditions.query.LbqWrapper;
import com.jettech.basic.log.entity.OptLogTypeEnum;
import com.jettech.jettong.base.dto.sys.log.OptLogPageQuery;
import com.jettech.jettong.base.dto.sys.log.OptLogResult;
import com.jettech.jettong.base.entity.sys.log.LoginLog;
import com.jettech.jettong.base.entity.sys.log.OptLog;
import com.jettech.jettong.base.entity.sys.personalized.PersonalizedTableView;
import com.jettech.jettong.base.service.sys.log.OptLogService;
import com.jettech.jettong.base.service.sys.personalized.PersonalizedTableViewService;
import com.jettech.jettong.common.dto.PeriodQuery;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;

import static com.jettech.jettong.common.constant.SwaggerConstants.*;


/**
 * 系统日志控制器
 *
 * <AUTHOR>
 * @version 1.0
 * @description 系统日志控制器
 * @projectName jettong
 * @package com.jettech.jettong.base.controller.sys.log
 * @className OptLogController
 * @date 2021-10-15
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/base/optLog")
@Api(value = "OptLog", tags = "系统日志")
@PreAuth(replace = "authority:optLog:")
@RequiredArgsConstructor
public class OptLogController extends SuperSimpleController<OptLogService, OptLog>
        implements DeleteController<OptLog, Long>
{

    private final PersonalizedTableViewService tableViewService;

    @ApiOperation(value = "分页列表查询")
    @PostMapping("/page")
    @SysLog(value = "'分页列表查询:第' + #params?.current + '页, 显示' + #params?.size + '行'", response = false)
    @PreAuth("hasAnyPermission('{}view')")
    public R<IPage<OptLog>> page(@RequestBody @Validated PageParams<OptLogPageQuery> params)
    {
        IPage<OptLog> page = params.buildPage(LoginLog.class);
        OptLogPageQuery optLogPageQuery = params.getModel();
        PeriodQuery<LocalDateTime> date = optLogPageQuery.getDate();

        LbqWrapper<OptLog> lbqWrapper = Wraps.<OptLog>lbQ().eq(OptLog::getCreatedBy, optLogPageQuery.getUserId())
                .like(OptLog::getRequestIp, optLogPageQuery.getRequestIp())
                .like(OptLog::getRequestUri, optLogPageQuery.getRequestUri())
                .ge(OptLog::getConsumingTime, optLogPageQuery.getConsumingTime())
                .eq(OptLog::getDescription, optLogPageQuery.getDescription());
        if (null != optLogPageQuery.getHttpMethods() && !optLogPageQuery.getHttpMethods().isEmpty())
        {
            lbqWrapper.in(OptLog::getHttpMethod, optLogPageQuery.getHttpMethods());
        }
        if (null != optLogPageQuery.getTypes() && !optLogPageQuery.getTypes().isEmpty())
        {
            lbqWrapper.in(OptLog::getType, optLogPageQuery.getTypes());
        }
        if(null != date){
            if (null != date.getStart() && null != date.getEnd())
            {
                lbqWrapper.between(OptLog::getCreateTime, date.getStart(),
                        date.getEnd());
            }
        }

        baseService.page(page, lbqWrapper);

        // 保存最后一次使用筛选记录，并作为默认视图
        tableViewService.saveLastUseFilter(
                PersonalizedTableView.lastView(getUserId(), "log-table", params));

        return success(page);
    }

    /**
     * 查询
     *
     * @param id 主键id
     * @return 查询结果
     */
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "主键", dataType = DATA_TYPE_LONG, paramType = PARAM_TYPE_QUERY),
    })
    @ApiOperation(value = "单体查询", notes = "单体查询")
    @GetMapping("/get")
    @PreAuth("hasAnyPermission('{}view')")
    public R<OptLogResult> get(@RequestParam Long id)
    {
        return success(baseService.getOptLogResultById(id));
    }

    @ApiOperation("清理日志")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "type",
                    value = "清理类型,1-一个月前，2-一个季度前，3-半年前，4-1年前，5-保留最新1千条，6-保留最新1万条，7-保留最新3万条，8-保留最新10万条",
                    dataType = DATA_TYPE_INT,
                    paramType = PARAM_TYPE_QUERY)
    })
    @DeleteMapping("/clear")
    @SysLog(value = "清理日志", optType = OptLogTypeEnum.DELETE)
    @PreAuth("hasAnyPermission('{}delete')")
    public R<Boolean> clear(@RequestParam(required = false, defaultValue = "1") Integer type)
    {
        LocalDateTime clearBeforeTime = null;
        Integer clearBeforeNum = null;
        switch (type)
        {
            // 清理一个月以前的日志数据
            case 1:
                clearBeforeTime = LocalDateTime.now().plusMonths(-1);
                break;
            // 清理三个月以前的日志数据
            case 2:
                clearBeforeTime = LocalDateTime.now().plusMonths(-3);
                break;
            // 清理六个月以前的日志数据
            case 3:
                clearBeforeTime = LocalDateTime.now().plusMonths(-6);
                break;
            // 清理一年以前的日志数据
            case 4:
                clearBeforeTime = LocalDateTime.now().plusMonths(-12);
                break;
            // 清理一千条以前日志数据
            case 5:
                clearBeforeNum = 1000;
                break;
            // 清理一万条以前日志数据
            case 6:
                clearBeforeNum = 10000;
                break;
            // 清理三万条以前日志数据
            case 7:
                clearBeforeNum = 30000;
                break;
            // 清理十万条以前日志数据
            case 8:
                clearBeforeNum = 100000;
                break;
            default:
                return fail("非法参数");
        }

        baseService.clearLog(clearBeforeTime, clearBeforeNum);
        return success();
    }

}
