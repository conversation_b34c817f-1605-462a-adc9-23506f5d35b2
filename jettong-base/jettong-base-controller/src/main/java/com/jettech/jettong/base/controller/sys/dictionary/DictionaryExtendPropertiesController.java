package com.jettech.jettong.base.controller.sys.dictionary;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jettech.basic.base.R;
import com.jettech.basic.database.mybatis.conditions.Wraps;
import com.jettech.basic.utils.BeanPlusUtil;
import com.jettech.basic.utils.YmalFc;
import com.jettech.jettong.base.entity.sys.dictionary.Dictionary;
import com.jettech.jettong.base.entity.sys.dictionary.DictionaryType;
import com.jettech.jettong.base.service.sys.dictionary.DictionaryService;
import com.jettech.jettong.base.vo.sys.dictionary.DictionaryExtendAttributesVO;
import com.fasterxml.jackson.databind.JsonNode;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;

import static com.jettech.jettong.common.constant.SwaggerConstants.DATA_TYPE_STRING;
import static com.jettech.jettong.common.constant.SwaggerConstants.PARAM_TYPE_PATH;

/**
 * 字典扩展信息配置文件控制器
 *
 * <AUTHOR>
 * @version 1.0
 * @description 字典扩展信息配置文件控制器
 * @projectName jettong-enterprises
 * @package com.jettech.jettong.base.controller.sys.dictionary
 * @className DictionaryExtendPropertiesController
 * @date 2021/10/27 16:07
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/cmdb/dictionaryExtendProperties")
@Api(value = "DictionaryExtendPropertiesController", tags = "字典扩展信息配置")
@RequiredArgsConstructor
public class DictionaryExtendPropertiesController
{

    private final DictionaryService dictionaryService;

    @ApiOperation(value = "根据字典类型获取字典扩展属性")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dictionaryType", value = "字典类型", required = true, dataType = DATA_TYPE_STRING,
                    paramType = PARAM_TYPE_PATH)})
    @GetMapping("/{dictionaryType}")
    public R<DictionaryExtendAttributesVO> findByDictionaryType(@PathVariable String dictionaryType)
    {
        YmalFc ymalFc = new YmalFc();
        try
        {
            JsonNode nodes = ymalFc.build("/dictionary_extend.yaml");
            JSONArray array = JSONArray.parseArray(nodes.toString());
            for (int i = 0; i < array.size(); i++)
            {
                JSONObject obj = array.getJSONObject(i);
                String dictType = obj.getString("dictionaryType");
                if (dictionaryType.equals(dictType))
                {
                    DictionaryExtendAttributesVO dictionaryExtendAttributesVO =
                            BeanPlusUtil.toBeanIgnoreCase(obj, DictionaryExtendAttributesVO.class, true);
                    if (obj.containsKey("parentKey"))
                    {
                        String parentKey = obj.getString("parentKey");

                        Dictionary parentDictionary = dictionaryService.getOne(
                                Wraps.<Dictionary>lbQ().eq(Dictionary::getType, parentKey).last(" limit 1"),
                                false);
                        if (null != parentDictionary)
                        {
                            DictionaryType parentDictionaryType =
                                    DictionaryType.builder().type(parentKey).label(parentDictionary.getLabel()).build();
                            dictionaryExtendAttributesVO.setParentKey(parentDictionaryType);
                        }
                    }
                    return R.success(dictionaryExtendAttributesVO);
                }
            }
        }
        catch (IOException e)
        {
            log.error("获取字典扩展属性失败，原因:{}", e.getMessage(), e);
            return R.validFail("获取字典扩展属性失败，原因:%s", e.getMessage());
        }
        return R.success(null);
    }
}
