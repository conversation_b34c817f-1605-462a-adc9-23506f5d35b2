package com.jettech.jettong.base.controller.sys.personalized;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jettech.basic.base.R;
import com.jettech.basic.base.controller.SuperController;
import com.jettech.basic.base.entity.SuperEntity;
import com.jettech.basic.base.request.PageParams;
import com.jettech.basic.context.ContextUtil;
import com.jettech.basic.database.mybatis.conditions.Wraps;
import com.jettech.basic.database.mybatis.conditions.query.LbqWrapper;
import com.jettech.basic.database.mybatis.conditions.query.QueryWrap;
import com.jettech.basic.echo.core.EchoService;
import com.jettech.basic.utils.BeanPlusUtil;
import com.jettech.jettong.base.dto.sys.personalized.TableViewPageQuery;
import com.jettech.jettong.base.dto.sys.personalized.TableViewSaveDTO;
import com.jettech.jettong.base.dto.sys.personalized.TableViewShareDTO;
import com.jettech.jettong.base.dto.sys.personalized.TableViewUpdateDTO;
import com.jettech.jettong.base.entity.rbac.user.User;
import com.jettech.jettong.base.entity.sys.personalized.TableView;
import com.jettech.jettong.base.entity.sys.personalized.TableViewCollection;
import com.jettech.jettong.base.entity.sys.personalized.TableViewShare;
import com.jettech.jettong.base.service.sys.personalized.TableViewCollectionService;
import com.jettech.jettong.base.service.sys.personalized.TableViewService;
import com.jettech.jettong.base.service.sys.personalized.TableViewShareService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;

import static java.util.stream.Collectors.groupingBy;
import static java.util.stream.Collectors.mapping;
import static java.util.stream.Collectors.toMap;
import static java.util.stream.Collectors.toSet;

/**
 * 筛选器控制类
 *
 * <AUTHOR>
 * @version 1.0
 * @projectname jettong-base-cloud
 * 2023/3/27 15:51
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/base/tableView")
@RequiredArgsConstructor
@Api(value = "TableViewController", tags = "筛选器管理")
public class TableViewController extends SuperController<TableViewService, Long, TableView, TableViewPageQuery,
        TableViewSaveDTO, TableViewUpdateDTO> {

    private final TableViewCollectionService collectionService;
    private final TableViewShareService shareService;
    private final EchoService echoService;

    @Override
    public R<TableView> get(Long id) {

        TableView tableView = baseService.getById(id);
        // 添加已分享给哪些用户
        List<User> userList = baseService.shareUsers(id);
        echoService.action(tableView);
        tableView.getEchoMap().put("sharedUsers", userList);
        return success(tableView);
    }

    @ApiOperation("分页查询收藏的筛选器列表")
    @PostMapping("/collect")
    public R<IPage<TableView>> collect(@RequestBody PageParams<TableViewPageQuery> query) {
        Long userId = ContextUtil.getUserId();
        List<Long> viewIds = collectionService.getCollectViewIds(userId);

        IPage<TableView> page = query.buildPage();
        if (CollUtil.isEmpty(viewIds)) {
            return success(page);
        }

        TableView tableView = BeanPlusUtil.toBean(query.getModel(), TableView.class);
        LbqWrapper<TableView> wrapper = Wraps.lbQ(tableView);
        wrapper.in(SuperEntity::getId, viewIds);
        wrapper.orderByAsc(TableView::getSort);
        baseService.page(page, wrapper);
        page.getRecords().forEach(view -> view.setCollected(true));
        echoService.action(page);
        echoCollection(page);
        return success(page);
    }

    @ApiOperation(value = "分页查询分享到的筛选器", notes = "分页查询分享到的筛选器")
    @PostMapping("/shared")
    public R<IPage<TableView>> sharedView(@RequestBody PageParams<TableViewPageQuery> query) {
        Long userId = ContextUtil.getUserId();
        IPage<TableView> page = query.buildPage(TableView.class);

        LbqWrapper<TableViewShare> shareLbqWrapper = Wraps.lbQ();
        shareLbqWrapper.eq(TableViewShare::getUserId, userId);
        List<TableViewShare> shareList = shareService.list(shareLbqWrapper);

        if (shareList.isEmpty()) {
            return success(page);
        }

        Set<Long> viewIds = shareList.stream().map(TableViewShare::getViewId).collect(toSet());

        TableView model = BeanPlusUtil.toBean(query.getModel(), TableView.class);
        QueryWrap<TableView> wrap = Wraps.q(model);
        LbqWrapper<TableView> lbqWrapper = wrap.lambda()
                .in(SuperEntity::getId, viewIds);
        baseService.page(page, lbqWrapper);
        echoCollection(page);

        echoService.action(page);
        page.getRecords().forEach(item -> item.setShared(true));

        return success(page);
    }

    @ApiOperation(value = "分页查询我的筛选器", notes = "分页查询我的筛选器, 我创建的筛选器")
    @PostMapping("/my")
    public R<IPage<TableView>> myTableView(@RequestBody PageParams<TableViewPageQuery> query) {
        Long userId = ContextUtil.getUserId();
        IPage<TableView> page = query.buildPage(TableView.class);
        TableView model = BeanPlusUtil.toBean(query.getModel(), TableView.class);
        QueryWrap<TableView> wrap = Wraps.q(model);
        LbqWrapper<TableView> lbqWrapper = wrap.lambda()
                .eq(SuperEntity::getCreatedBy, userId);
        baseService.page(page, lbqWrapper);
        echoCollection(page);
        echoService.action(page);

        return success(page);
    }

    @Override
    public void handlerResult(IPage<TableView> page) {
        super.handlerResult(page);

        echoCollection(page);
        echoService.action(page);

    }

    private void echoCollection(IPage<TableView> page) {
        List<TableView> records = page.getRecords();

        if (records.isEmpty()) {
            return;
        }

        Long userId = ContextUtil.getUserId();

        Map<Long, TableView> viewMap = records.stream().collect(toMap(SuperEntity::getId, Function.identity()));

        // 查询相关的收藏信息
        LbqWrapper<TableViewCollection> wrapper = Wraps.lbQ();
        wrapper.in(TableViewCollection::getViewId, viewMap.keySet());
        List<TableViewCollection> collectionList = collectionService.list(wrapper);

        // 根据筛选器ID对收藏的人分组
        Map<Long, Set<Long>> viewUserMap = collectionList.stream().collect(groupingBy(
                TableViewCollection::getViewId,
                mapping(TableViewCollection::getUserId, toSet())
        ));

        // 将收藏信息放入数据中
        viewMap.forEach((viewId, view) -> {
            Set<Long> userIds = viewUserMap.get(viewId);

            if (userIds == null) {
                return;
            }
            view.setCollectedCount(userIds.size());
            if (userIds.contains(userId)) {
                view.setCollected(true);
            }
        });
    }

    @ApiOperation(value = "查询适用表可用的筛选器", notes = "查询全部可用的筛选器, 包含我创建的、被分享的、公开的筛选器")
    @GetMapping("/table")
    public R<List<TableView>> enableTableView(@RequestParam String table,
            @RequestParam(required = false) Long projectId) {
        Long userId = ContextUtil.getUserId();
        List<TableView> enableTableView = baseService.enableTableView(table, userId, projectId);
        return success(enableTableView);
    }

    @ApiOperation(value = "收藏筛选器")
    @PutMapping("/{id}/collect")
    public R<Boolean> collect(@PathVariable Long id) {

        collectionService.collect(id);
        return success();
    }

    @ApiOperation(value = "取消收藏筛选器")
    @PutMapping("/{id}/uncollect")
    public R<Boolean> uncollect(@PathVariable Long id) {

        collectionService.uncollect(id);
        return success();
    }

    @ApiOperation(value = "分享筛选器")
    @PostMapping("/share")
    public R<Boolean> share(@Valid @RequestBody TableViewShareDTO shareDTO) {

        shareService.share(shareDTO.getViewIds(), shareDTO.getUserIds());
        return success();
    }

    @ApiOperation(value = "删除分享的筛选器")
    @PostMapping("/unshare")
    public R<Boolean> unshare(@Valid @RequestBody TableViewShareDTO shareDTO) {

        shareService.unshare(shareDTO.getViewIds(), shareDTO.getUserIds());
        return success();
    }

    @ApiOperation(value = "复制筛选器")
    @PostMapping("/{viewId}/copy")
    public R<TableView> copy(@PathVariable String viewId,
            @RequestParam(required = false) String name) {

        TableView tableView = baseService.copy(viewId, name);
        return success(tableView);
    }


}
