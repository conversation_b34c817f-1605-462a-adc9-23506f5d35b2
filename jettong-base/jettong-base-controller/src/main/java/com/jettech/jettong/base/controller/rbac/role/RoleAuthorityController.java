package com.jettech.jettong.base.controller.rbac.role;


import com.jettech.basic.annotation.log.SysLog;
import com.jettech.basic.base.R;
import com.jettech.basic.database.mybatis.conditions.Wraps;
import com.jettech.jettong.base.entity.rbac.role.RoleAuthority;
import com.jettech.jettong.base.service.rbac.role.RoleAuthorityService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 角色权限处理控制器
 *
 * <AUTHOR>
 * @version 1.0
 * @description 角色权限处理控制器
 * @projectName jettong
 * @package com.jettech.jettong.base.controller.rbac.role
 * @className RoleAuthorityController
 * @date 2021/9/15 9:43
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/base/roleAuthority")
@Api(value = "RoleAuthority", tags = "角色权限管理")
@RequiredArgsConstructor
public class RoleAuthorityController
{

    private final RoleAuthorityService roleAuthorityService;

    @ApiOperation(value = "查询指定角色关联的菜单和资源", notes = "查询指定角色关联的菜单和资源")
    @GetMapping("/{roleId}")
    @SysLog(value = "'查询指定角色关联的菜单和资源", response = false)
    public R<List<RoleAuthority>> queryByRoleId(@PathVariable Long roleId)
    {
        return R.success(roleAuthorityService.list(Wraps.<RoleAuthority>lbQ().eq(RoleAuthority::getRoleId, roleId)));
    }

}
