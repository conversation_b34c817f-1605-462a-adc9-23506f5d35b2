package com.jettech.jettong.base.controller.rbac.team;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jettech.basic.annotation.log.SysLog;
import com.jettech.basic.annotation.security.PreAuth;
import com.jettech.basic.base.R;
import com.jettech.basic.base.controller.SuperSimpleController;
import com.jettech.basic.base.request.PageParams;
import com.jettech.basic.database.mybatis.conditions.Wraps;
import com.jettech.basic.database.mybatis.conditions.query.LbqWrapper;
import com.jettech.basic.echo.core.EchoService;
import com.jettech.basic.log.entity.OptLogTypeEnum;
import com.jettech.basic.utils.ArgumentAssert;
import com.jettech.basic.utils.BeanPlusUtil;
import com.jettech.basic.utils.TreeUtil;
import com.jettech.jettong.base.dto.rbac.team.SysTeamQuery;
import com.jettech.jettong.base.dto.rbac.team.SysTeamSaveDTO;
import com.jettech.jettong.base.dto.rbac.team.SysTeamUpdateDTO;
import com.jettech.jettong.base.dto.rbac.user.UserPageQuery;
import com.jettech.jettong.base.entity.rbac.team.SysTeam;
import com.jettech.jettong.base.entity.rbac.team.SysTeamUser;
import com.jettech.jettong.base.entity.rbac.user.User;
import com.jettech.jettong.base.entity.rbac.user.UserRole;
import com.jettech.jettong.base.entity.sys.personalized.PersonalizedTableView;
import com.jettech.jettong.base.service.rbac.org.OrgService;
import com.jettech.jettong.base.service.rbac.team.SysTeamService;
import com.jettech.jettong.base.service.rbac.team.SysTeamUserService;
import com.jettech.jettong.base.service.rbac.user.UserRoleService;
import com.jettech.jettong.base.service.rbac.user.UserService;
import com.jettech.jettong.base.service.sys.personalized.PersonalizedTableViewService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;

import static com.jettech.jettong.common.constant.SwaggerConstants.*;
import static com.jettech.jettong.common.constant.SwaggerConstants.PARAM_TYPE_QUERY;

/**
 * 团队信息表控制器
 *
 * <AUTHOR>
 * @version 1.0
 * @description 团队信息表控制器
 * @projectName jettong
 * @package com.jettech.jettong.base.base.controller
 * @className SysTeamController
 * @date 2023-03-13
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Slf4j
@Validated
@RestController
@RequiredArgsConstructor
@RequestMapping("/sysTeam")
@Api(value = "SysTeam", tags = "团队信息表")
@PreAuth(replace = "base:sysTeam:")
public class SysTeamController extends SuperSimpleController<SysTeamService, SysTeam>
{

    private final SysTeamUserService teamUserService;
    private final UserService userService;
    private final UserRoleService userRoleService;
    private final OrgService orgService;
    private final EchoService echoService;
    private final PersonalizedTableViewService tableViewService;

    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", dataType = DATA_TYPE_LONG, paramType = PARAM_TYPE_QUERY),
            @ApiImplicitParam(name = "name", value = "名称", dataType = DATA_TYPE_STRING, paramType = PARAM_TYPE_QUERY),
    })
    @ApiOperation(value = "检测名称是否可用", notes = "检测名称是否可用")
    @GetMapping("/checkNameUnique")
    public R<Boolean> checkNameUnique(@RequestParam(required = false) Long id, @RequestParam String name)
    {
        return success(baseService.checkNameUnique(id, name));
    }
    @ApiOperation(value = "新增团队")
    @PostMapping
    @SysLog(value = "新增团队", request = false, optType = OptLogTypeEnum.ADD)
    @PreAuth("hasAnyPermission('{}add')")
    public R<SysTeam> handlerSave(@RequestBody @Validated SysTeamSaveDTO model)
    {
        SysTeam sysTeam = BeanPlusUtil.toBean(model, SysTeam.class);
        String name = sysTeam.getName();
        ArgumentAssert.isFalse(baseService.checkNameUnique(null,name), "团队名称{}已经存在",name);
        baseService.save(sysTeam);
        return success(sysTeam);
    }

    @ApiOperation(value = "删除团队")
    @DeleteMapping
    @SysLog(value = "'删除团队:' + #ids", optType = OptLogTypeEnum.DELETE)
    @PreAuth("hasAnyPermission('{}delete')")
    public R<Boolean> handlerDelete(@RequestBody List<Long> ids)
    {
        baseService.removeByIds(ids);
        return R.success();
    }

    @ApiOperation(value = "修改团队", notes = "修改UpdateDTO中不为空的字段")
    @PutMapping
    @SysLog(value = "'修改团队:' + #updateDTO?.id", request = false, optType = OptLogTypeEnum.EDIT)
    @PreAuth("hasAnyPermission('{}edit')")
    public R<SysTeam> handlerUpdate(@RequestBody @Validated SysTeamUpdateDTO model)
    {
        SysTeam sysTeam = BeanPlusUtil.toBean(model, SysTeam.class);
        String name = sysTeam.getName();
        ArgumentAssert.isFalse(baseService.checkNameUnique(sysTeam.getId(),name), "团队名称{}已经存在",name);
        baseService.updateById(sysTeam);
        return R.success(sysTeam);
    }

    @ApiOperation(value = "查询团队树形结构", notes = "查询团队树形结构")
    @PostMapping("/tree")
    @SysLog("查询团队树形结构")
    @PreAuth("hasAnyPermission('{}view')")
    public R<List<SysTeam>> tree(@RequestBody SysTeamQuery model)
    {
        List<SysTeam> list = baseService.list(Wraps.<SysTeam>lbQ().like(SysTeam::getName, model.getName())
                .eq(SysTeam::getParentId, model.getParentId()).eq(SysTeam::getOrgId, model.getOrgId())
                .eq(SysTeam::getLeaderBy, model.getLeaderBy()).eq(SysTeam::getType, model.getType()));

        echoService.action(list);
        return success(TreeUtil.buildTree(list));
    }

    @ApiOperation(value = "根据id查询团队（组织级）信息")
    @GetMapping(value = "/{id}")
    @SysLog(value = "根据id查询团队（组织级）信息")
    @PreAuth("hasAnyPermission('{}view')")
    public R<SysTeam> selectOrgById(@RequestParam("id") Long id)
    {
        SysTeam sysTeam = baseService.getById(id);
        echoService.action(sysTeam);
        return R.success(sysTeam);
    }

    @ApiOperation(value = "添加团队成员", notes = "添加团队成员")
    @PostMapping("/{teamId}/user")
    @PreAuth("hasAnyPermission('{}add')")
    @SysLog("添加团队成员")
    public R<Boolean> handlerSaveTeamUser(@PathVariable Long teamId, @RequestBody List<Long> userIds)
    {
        teamUserService.addTeamUser(teamId, userIds);
        return success();
    }

    @ApiOperation(value = "删除团队成员", notes = "删除团队成员")
    @DeleteMapping("/{teamId}/user")
    @PreAuth("hasAnyPermission('{}delete')")
    @SysLog("删除团队成员")
    public R<Boolean> removeTeamUser(@PathVariable Long teamId,
            @RequestBody List<Long> userIds)
    {
        teamUserService.removeTeamUser(teamId, userIds);
        return success();
    }

    @ApiOperation(value = "分页查询团队下的人员", notes = "分页查询团队下的人员")
    @PostMapping("/{teamId}/user/page")
    @PreAuth("hasAnyPermission('{}view')")
    @SysLog("分页查询团队下的人员")
    public R<IPage<User>> pageTeamUser(@PathVariable Long teamId, @RequestBody PageParams<UserPageQuery> pageParams)
    {
        IPage<User> page = pageParams.buildPage();
        UserPageQuery model = pageParams.getModel();

        userService.page(page, Wraps.<User>lbQ().like(User::getName, model.getName())
                .like(User::getAccount, model.getAccount())
                .eq(User::getReadonly, false)
                .like(User::getIdCard, model.getIdCard())
                .like(User::getEmail, model.getEmail())
                .like(User::getMobile, model.getMobile())
                .like(User::getDescription, model.getDescription())
                .eq(User::getType, model.getType())
                .eq(User::getState, model.getState())
                .exists("select 1 from `sys_team_user` stu where stu.`team_id` = {0} and stu.`user_id` = `sys_user`" +
                                ".`id`",
                        teamId));
        echoService.action(page);

        page.getRecords().forEach(item ->
        {
            item.setPassword(null);
            item.setSalt(null);
            List<UserRole> userRoles =
                    userRoleService.list(Wraps.<UserRole>lbQ().eq(UserRole::getUserId, item.getId()));
            echoService.action(userRoles);

            item.setUserRoles(userRoles);

            List<SysTeamUser> list = teamUserService.list(Wraps.<SysTeamUser>lbQ().eq(SysTeamUser::getTeamId, teamId)
                    .eq(SysTeamUser::getUserId, item.getId()));
            item.getEchoMap().put("teamUser", list.get(0));

            // 添加组织机构
            Optional.of(item)
                    .map(User::getOrgId)
                    .map(orgService::findAllParent)
                    .ifPresent(item::setOrgs);
        });

        // 保存最后一次查询条件
        PersonalizedTableView lasted = PersonalizedTableView.lastView(getUserId(), "teamUserTable", pageParams);
        tableViewService.saveLastUseFilter(lasted);

        return success(page);
    }

    @ApiOperation(value = "查询团队下的全部人员(只返回用户基本信息)", notes = "查询团队下的全部人员(只返回用户基本信息)")
    @GetMapping("/{teamId}/user/query")
    @PreAuth("hasAnyPermission('{}view')")
    @SysLog("查询团队下的全部人员")
    public R<List<User>> teamUser(@PathVariable Long teamId, @RequestParam(required = false) String keyword)
    {
        LbqWrapper<User> wrapper = Wraps.<User>lbQ()
                .eq(User::getState, true)
                .exists("select 1 from `sys_team_user` stu where stu.`team_id` = {0} and stu.`user_id` = `sys_user`" +
                                ".`id`",
                        teamId);
        if (StrUtil.isNotEmpty(keyword))
        {
            wrapper.and(wrap -> wrap.like(User::getName, keyword).or().like(User::getAccount, keyword).or()
                    .like(User::getEmail, keyword)
                    .or().like(User::getMobile, keyword)
                    .or().like(User::getIdCard, keyword));

        }

        List<User> users = userService.list(wrapper);
        echoService.action(users);
        return success(users);
    }


}
