package com.jettech.jettong.base.controller.rbac.role;

import cn.hutool.core.util.StrUtil;
import com.jettech.basic.annotation.log.SysLog;
import com.jettech.basic.annotation.security.PreAuth;
import com.jettech.basic.base.R;
import com.jettech.basic.base.controller.SuperCacheController;
import com.jettech.basic.database.mybatis.conditions.Wraps;
import com.jettech.basic.utils.BeanPlusUtil;
import com.jettech.basic.utils.TreeUtil;
import com.jettech.jettong.base.dto.rbac.role.MenuFunctionTreeVO;
import com.jettech.jettong.base.dto.rbac.role.MenuSaveDTO;
import com.jettech.jettong.base.dto.rbac.role.MenuUpdateDTO;
import com.jettech.jettong.base.entity.rbac.role.Menu;
import com.jettech.jettong.base.service.rbac.role.MenuService;
import com.jettech.jettong.base.vo.rbac.role.OtherMenusVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.jettech.basic.utils.StrPool.DEF_COMPONENT;
import static com.jettech.basic.utils.StrPool.DEF_PARENT_ID;
import static com.jettech.jettong.common.constant.SwaggerConstants.*;

/**
 * 菜单处理控制器
 *
 * <AUTHOR>
 * @version 1.0
 * @description 菜单处理控制器
 * @projectName jettong
 * @package com.jettech.jettong.base.controller.rbac.role
 * @className MenuController
 * @date 2021/9/15 9:43
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/base/menu")
@Api(value = "Menu", tags = "菜单管理")
@PreAuth(replace = "base:menu:")
public class MenuController extends SuperCacheController<MenuService, Long, Menu, Menu, MenuSaveDTO, MenuUpdateDTO>
{

    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", dataType = DATA_TYPE_LONG, paramType = PARAM_TYPE_QUERY),
            @ApiImplicitParam(name = "code", value = "编码", dataType = DATA_TYPE_STRING, paramType = PARAM_TYPE_QUERY),
    })
    @ApiOperation(value = "检测菜单编码是否可用", notes = "检测菜单编码是否可用")
    @GetMapping("/check")
    @SysLog(value = "检测菜单编码是否可用", request = false)
    public R<Boolean> check(@RequestParam(required = false) Long id, @RequestParam String code)
    {
        return success(baseService.check(id, code));
    }

    @Override
    public R<Menu> handlerSave(MenuSaveDTO menuSaveDTO)
    {
        Menu menu = BeanPlusUtil.toBean(menuSaveDTO, Menu.class);
        fillMenu(menu);
        baseService.saveWithCache(menu);
        return success(menu);
    }

    /**
     * 设置菜单默认值
     *
     * @param menu 菜单信息
     * <AUTHOR>
     * @date 2021/10/18 15:53
     * @update zxy 2021/10/18 15:53
     * @since 1.0
     */
    private void fillMenu(Menu menu)
    {
        if (menu.getReadonly() == null)
        {
            menu.setReadonly(false);
        }
        if (menu.getSort() == null)
        {
            menu.setSort(0);
        }
        if (StrUtil.isEmpty(menu.getComponent()))
        {
            menu.setComponent(DEF_COMPONENT);
        }
        if (null == menu.getIsHide())
        {
            menu.setIsHide(false);
        }
        if (null == menu.getParentId())
        {
            menu.setParentId(DEF_PARENT_ID);
        }
        if (null == menu.getShowChildren())
        {
            menu.setShowChildren(false);
        }
    }

    @Override
    public R<Menu> handlerUpdate(MenuUpdateDTO model)
    {
        Menu menu = BeanPlusUtil.toBean(model, Menu.class);
        baseService.updateWithCache(menu);
        return success(menu);
    }

    @Override
    public R<Boolean> handlerDelete(List<Long> ids)
    {
        baseService.removeByIdWithCache(ids);
        return success();
    }

    @ApiOperation(value = "查询系统所有的菜单", notes = "查询系统所有的菜单")
    @PostMapping("/tree")
    @SysLog("查询系统所有的菜单")
    public R<List<Menu>> allTree(Menu menu)
    {
        List<Menu> list = baseService.list(Wraps.<Menu>lbQ().eq(Menu::getPlatform,menu.getPlatform()).orderByAsc(Menu::getSort));
        return success(TreeUtil.buildTree(list));
    }

    @ApiOperation(value = "查询系统所有的菜单和功能树", notes = "查询系统所有的菜单和功能树")
    @PostMapping("/menuFunctionTree")
    @SysLog("查询系统所有的菜单和功能树")
    public R<List<MenuFunctionTreeVO>> menuFunctionTree(@RequestBody Menu menu)
    {
        return success(baseService.findMenuFunctionTree(menu));
    }

    @ApiOperation(value = "查询当前登录用户的菜单资源树", notes = "查询当前登录用户的菜单资源树")
    @PostMapping("/currentMenuFunctionTree")
    @SysLog("查询当前登录用户的菜单资源树")
    public R<List<MenuFunctionTreeVO>> currentMenuFunctionTree(@RequestBody Menu menu)
    {
        return success(baseService.currentMenuFunctionTree(menu));
    }


    @ApiOperation(value = "外部应用新增或修改菜单及功能权限", notes = "外部应用新增或修改菜单及功能权限")
    @PostMapping("/noToken/addOrUpdateMenuAndFunction")
    @SysLog("外部应用新增或修改菜单及功能权限")
    @PreAuth("base:menu:external:save")
    public R<List<Menu>> addOrUpdateMenuAndFunction(@RequestBody @Validated List<OtherMenusVo> menus,@RequestParam String platform) {
        try {
            if(StringUtils.isEmpty(platform)){
                return fail("平台代码不能为空");
            }
            List<Menu> result = baseService.addOrUpdateMenuAndFunction(menus ,platform);
            return success(result);
        } catch (Exception e) {
            log.error("外部应用新增或修改菜单及功能权限失败", e);
            return fail("操作失败：" + e.getMessage());
        }
    }
}
