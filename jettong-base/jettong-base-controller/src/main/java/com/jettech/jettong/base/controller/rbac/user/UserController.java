package com.jettech.jettong.base.controller.rbac.user;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.TemplateExportParams;
import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jettech.basic.annotation.log.SysLog;
import com.jettech.basic.annotation.security.PreAuth;
import com.jettech.basic.base.R;
import com.jettech.basic.base.controller.SuperCacheController;
import com.jettech.basic.base.entity.SuperEntity;
import com.jettech.basic.base.request.PageParams;
import com.jettech.basic.cache.repository.CacheOps;
import com.jettech.basic.database.mybatis.conditions.Wraps;
import com.jettech.basic.database.mybatis.conditions.query.LbqWrapper;
import com.jettech.basic.database.mybatis.conditions.query.QueryWrap;
import com.jettech.basic.echo.core.EchoService;
import com.jettech.basic.log.entity.OptLogTypeEnum;
import com.jettech.basic.security.config.EncryptionConfig;
import com.jettech.basic.utils.ArgumentAssert;
import com.jettech.basic.utils.StringUtil;
import com.jettech.jettong.base.dto.rbac.user.*;
import com.jettech.jettong.base.dto.rbac.user.*;
import com.jettech.jettong.base.entity.rbac.org.Org;
import com.jettech.jettong.base.entity.rbac.user.User;
import com.jettech.jettong.base.entity.rbac.user.UserRole;
import com.jettech.jettong.base.entity.sys.dictionary.Dictionary;
import com.jettech.jettong.base.entity.sys.personalized.PersonalizedTableView;
import com.jettech.jettong.base.poi.rbac.user.UserExcelDictHandlerImpl;
import com.jettech.jettong.base.poi.rbac.user.UserExcelVerifyHandlerImpl;
import com.jettech.jettong.base.service.rbac.org.OrgService;
import com.jettech.jettong.base.service.rbac.user.UserRoleService;
import com.jettech.jettong.base.service.rbac.user.UserService;
import com.jettech.jettong.base.service.rbac.user.impl.UserServiceImpl;
import com.jettech.jettong.base.service.sys.dictionary.DictionaryService;
import com.jettech.jettong.base.service.sys.personalized.PersonalizedTableViewService;
import com.jettech.jettong.base.vo.rbac.user.UserExcelVO;
import com.jettech.jettong.common.constant.BizConstant;
import com.jettech.jettong.common.util.poi.ExcelDownLoadUtil;
import com.jettech.jettong.common.util.poi.ExcelExportPlusUtil;
import com.google.common.collect.Maps;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.springframework.util.Base64Utils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.groups.Default;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

import static com.jettech.jettong.common.constant.SwaggerConstants.*;
import static org.apache.poi.ss.usermodel.Font.COLOR_RED;

/**
 * 用户处理控制器
 *
 * <AUTHOR>
 * @version 1.0
 * @description 用户处理控制器
 * @projectName jettong
 * @package com.jettech.jettong.base.controller.rbac.user
 * @className UserController
 * @date 2021/9/15 9:43
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/base/user")
@Api(value = "User", tags = "用户管理")
@PreAuth(replace = "base:user:")
@RequiredArgsConstructor
public class UserController
        extends SuperCacheController<UserService, Long, User, UserPageQuery, UserSaveDTO, UserUpdateDTO>
{
    private final OrgService orgService;
    private final EchoService echoService;
    private final UserRoleService userRoleService;
    private final UserExcelVerifyHandlerImpl userExcelVerifyHandler;
    private final UserExcelDictHandlerImpl userExcelDictHandler;
    private final DictionaryService dictionaryService;
    private final CacheOps cacheOps;
    private final PersonalizedTableViewService tableViewService;
    private final EncryptionConfig encryptionConfig;

    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", dataType = DATA_TYPE_LONG, paramType = PARAM_TYPE_QUERY),
            @ApiImplicitParam(name = "name", value = "名称", dataType = DATA_TYPE_STRING, paramType = PARAM_TYPE_QUERY),
    })
    @ApiOperation(value = "检测名称是否可用", notes = "检测名称是否可用")
    @GetMapping("/check")
    public R<Boolean> check(@RequestParam(required = false) Long id, @RequestParam String name)
    {
        return success(baseService.check(id, name));
    }

    @Override
    public R<User> handlerSave(UserSaveDTO data)
    {
        User user = BeanUtil.toBean(data, User.class);
        user.setReadonly(false);
        baseService.saveUser(user);
        echoParentOrgList(user);
        return success(user);
    }

    @Override
    public R<Boolean> handlerDelete(List<Long> ids)
    {
        baseService.remove(ids);
        return success(true);
    }

    @Override
    public R<User> handlerUpdate(UserUpdateDTO data)
    {
        ArgumentAssert.isFalse(baseService.check(data.getId(),data.getAccount()), "账号{}已经存在", data.getAccount());
        User user = BeanUtil.toBean(data, User.class);
        baseService.updateUser(user);
        return success(user);
    }

    @Override
    public R<List<User>> query(User data)
    {
        QueryWrap<User> wrapper = Wraps.q(data);

        if (null != data.getUserRoles() && !data.getUserRoles().isEmpty())
        {
            // 获取用户角色
            Set<Long> roleIds = data.getUserRoles().stream().map(UserRole::getRoleId).collect(Collectors.toSet());

            // 拼接用户角色为sql字符串
            String roleIdsStr = StrUtil.format("({})", CollUtil.join(roleIds, StrUtil.COMMA));
            wrapper.exists(StrUtil.format(
                    "select 1 from `sys_user_role` sur where sur.`role_id` in {} and sur.`user_id` = `id`",
                    roleIdsStr));
        }
        return success(baseService.list(wrapper));
    }

    @ApiOperation(value = "修改基础信息")
    @PutMapping("/base")
    @SysLog(value = "'修改基础信息:' + #data?.id", request = false, optType = OptLogTypeEnum.EDIT)
    @PreAuth("hasAnyPermission('{}edit')")
    public R<User> updateBase(@RequestBody @Validated({SuperEntity.Update.class}) UserUpdateBaseInfoDTO data)
    {
        User user = BeanUtil.toBean(data, User.class);
        baseService.updateById(user);
        return success(user);
    }

    @ApiOperation(value = "修改密码", notes = "修改密码")
    @PutMapping("/password")
    @SysLog(value = "'修改密码:' + #data.id", optType = OptLogTypeEnum.EDIT)
    public R<Boolean> updatePassword(@RequestBody @Validated(SuperEntity.Update.class) UserUpdatePasswordDTO data)
    {
        return success(baseService.updatePassword(data));
    }

    @ApiOperation(value = "重置密码", notes = "重置密码")
    @PostMapping("/reset")
    @SysLog(value = "'重置密码:' + #data.id", optType = OptLogTypeEnum.EDIT)
    public R<Boolean> reset(@RequestBody @Validated(SuperEntity.Update.class) UserResetPasswordDTO data)
    {
        baseService.reset(data);
        return success();
    }

    @ApiImplicitParams({
            @ApiImplicitParam(name = "userId", value = "用户id", dataType = DATA_TYPE_LONG,
                    paramType = PARAM_TYPE_PATH),
            @ApiImplicitParam(name = "state", value = "是否正常", dataType = DATA_TYPE_BOOLEAN,
                    paramType = PARAM_TYPE_PATH)
    })
    @ApiOperation(value = "修改用户状态", notes = "修改用户状态")
    @PutMapping("/updateStateById/{userId}/{state}")
    @SysLog(value = "修改用户状态", optType = OptLogTypeEnum.EDIT)
    public R<User> updateStateById(@PathVariable("userId") Long userId, @PathVariable("state") Boolean state)
    {
        return success(baseService.updateStateById(userId, state));
    }

    @ApiImplicitParams({
            @ApiImplicitParam(name = "roleId", value = "角色id", dataType = DATA_TYPE_LONG,
                    paramType = PARAM_TYPE_PATH),
            @ApiImplicitParam(name = "keyword", value = "关键字,登录账号或用户姓名", dataType = DATA_TYPE_LONG,
                    paramType = PARAM_TYPE_QUERY)
    })
    @ApiOperation(value = "查询角色的已关联用户", notes = "查询角色的已关联用户")
    @GetMapping(value = "/role/{roleId}")
    @SysLog("查询角色的已关联用户")
    public R<UserRoleDTO> findUserByRoleId(@PathVariable("roleId") Long roleId,
            @RequestParam(value = "keyword", required = false) String keyword)
    {
        List<User> list = baseService.findUserByRoleId(roleId, keyword);
        List<Long> idList = list.stream().mapToLong(User::getId).boxed().collect(Collectors.toList());
        return success(UserRoleDTO.builder().idList(idList).userList(list).build());
    }

    @ApiOperation(value = "查询所有用户", notes = "查询所有用户")
    @GetMapping("/find")
    @SysLog("查询所有用户")
    public R<List<Long>> findAllUserId()
    {
        return success(baseService.findAllUserId());
    }

    @ApiOperation(value = "查询所有用户实体", notes = "查询所有用户实体")
    @GetMapping("/findAll")
    @SysLog("查询所有用户")
    public R<List<User>> findAll(@RequestParam(required = false) Long orgId)
    {
        LbqWrapper<User> wrapper = Wraps.lbQ();

        wrapper.eq(User::getOrgId, orgId);

        List<User> res = baseService.list(wrapper);
        res.forEach(obj -> {
            obj.setPassword(null);
            obj.setSalt(null);
        });
        return success(res);
    }

    @ApiOperation(value = "根据id集合查询用户信息", notes = "根据id集合查询用户信息")
    @GetMapping("/findUserById")
    @SysLog("根据id集合查询用户信息")
    public R<List<User>> findUserById(@RequestParam(value = "ids") List<Long> ids)
    {
        return success(baseService.findUserById(ids));
    }

    @Override
    public R<User> get(Long id)
    {
        User user = baseService.getByIdCache(id);
        if (null != user)
        {
            user.setPassword(null);
            if (null != user.getUserRoles() && !user.getUserRoles().isEmpty())
            {
                echoService.action(user.getUserRoles());
            }
            echoService.action(user);
            echoParentOrgList(user);
        }
        return success(user);
    }
    @ApiOperation(value = "根据用户名或账号模糊查询用户信息", notes = "根据用户名或账号模糊查询用户信息")
    @GetMapping("/findUserByToken")
    public R<User> findUserByToken()
    {
        User user = baseService.getByIdCache(getUserId());
        if (null != user)
        {
            user.setPassword(null);
            if (null != user.getUserRoles() && !user.getUserRoles().isEmpty())
            {
                echoService.action(user.getUserRoles());
            }
            echoService.action(user);
            echoParentOrgList(user);
        }
        return success(user);
    }
    @ApiOperation(value = "根据用户名或账号模糊查询用户信息", notes = "根据用户名或账号模糊查询用户信息")
    @GetMapping("/findByAccountOrName")
    @SysLog("根据用户名或账号模糊查询用户信息")
    public R<List<User>> findByAccountOrName(@RequestParam(required = false) String accountOrName)
    {
        List<User> users = baseService.list(
                Wraps.<User>lbQ().like(User::getAccount, accountOrName).or().like(User::getName, accountOrName));
        users.forEach(obj -> obj.setPassword(null));
        users.forEach(this::echoParentOrgList);

        return success(users);
    }

    @ApiOperation(value = "分页查询用户，用于机构页面", notes = "分页查询用户，用于机构页面")
    @PostMapping("/pageForOrg")
    public IPage<User> queryForOrg(@RequestBody PageParams<UserPageQuery> params) {
        IPage<User> userIPage = queryPage(params);

        PersonalizedTableView tableView = PersonalizedTableView.lastView(getUserId(), "orgUserTable", params);
        tableViewService.saveLastUseFilter(tableView);
        return userIPage;
    }

    @Override
    public IPage<User> query(PageParams<UserPageQuery> params) {
        IPage<User> userIPage = queryPage(params);

        PersonalizedTableView tableView = PersonalizedTableView.lastView(getUserId(), "baseUserTable", params);
        tableViewService.saveLastUseFilter(tableView);
        return userIPage;
    }

    public IPage<User> queryPage(PageParams<UserPageQuery> params)
    {
        IPage<User> page = params.buildPage(User.class);
        UserPageQuery userPage = params.getModel();

        QueryWrap<User> wrap = handlerWrapper(null, params);

        LbqWrapper<User> wrapper = wrap.lambda();
        if (userPage.getOrgId() != null)
        {
            List<Org> children = orgService.findChildren(userPage.getOrgId());
            wrapper.in(User::getOrgId, children.stream().map(Org::getId).collect(Collectors.toList()));
        }
        wrapper.like(User::getName, userPage.getName())
                .like(User::getAccount, userPage.getAccount())
                .eq(User::getReadonly, false)
                .like(User::getIdCard, userPage.getIdCard())
                .like(User::getEmail, userPage.getEmail())
                .like(User::getMobile, userPage.getMobile())
                .like(User::getDescription, userPage.getDescription())
                .eq(User::getType, userPage.getType())
                .eq(User::getState, userPage.getState())
                .eq(userPage.getReportWork()!=null,User::getReportWork,userPage.getReportWork());
        if (null != userPage.getRoleIds() && !userPage.getRoleIds().isEmpty())
        {
            // 拼接用户角色为sql字符串
            String roleIdsStr = StrUtil.format("({})", CollUtil.join(userPage.getRoleIds(), StrUtil.COMMA));
            wrapper.exists(StrUtil.format(
                    "select 1 from `sys_user_role` sur where sur.`role_id` in {} and sur.`user_id` = s.`id`", roleIdsStr));
        }

        List<Long> teamIds = userPage.getTeamIds();
        if (ObjectUtil.isNotEmpty(teamIds)) {
            wrapper.exists("select 1 from sys_team_user stu where stu.user_id = s.`id` and stu.team_id in "
                    + CollUtil.join(teamIds, StrUtil.COMMA, "(", ")"));
        }

        baseService.findPage(page, wrapper);
        // 手动注入
        echoService.action(page);

        page.getRecords().forEach(item ->
        {
            item.setPassword(null);
            item.setSalt(null);
            List<UserRole> userRoles =
                    userRoleService.list(Wraps.<UserRole>lbQ().eq(UserRole::getUserId, item.getId()));
            echoService.action(userRoles);

            item.setUserRoles(userRoles);
        });

        page.getRecords().forEach(this::echoParentOrgList);


        return page;
    }

    @ApiOperation(value = "导出Excel")
    @PostMapping(value = "/export", produces = "application/octet-stream")
    @SysLog(value = "导出Excel", optType = OptLogTypeEnum.EXPORT)
    @PreAuth("hasAnyPermission('{}export')")
    public void exportExcel(@RequestBody @Validated UserExportQuery model, HttpServletResponse response)
    {
        //1.获取用户的excel模板
        TemplateExportParams exportParams = new TemplateExportParams("poi/template/user.xls");

        model.setReadonly(false);

        // 查询导出数据
        List<Map<String, Object>> list = baseService.findUserByUserExportQuery(model);

        Map<String, Object> map = Maps.newHashMapWithExpectedSize(7);
        map.put("list", list);

        //3.执行excel导出
        Workbook workbook = ExcelExportUtil.exportExcel(exportParams, map);

        try
        {
            ExcelDownLoadUtil.export(response, workbook, "用户信息.xls");
        }
        catch (IOException e)
        {
            log.error("导出文件失败，原因:{}", e.getMessage(), e);
        }

    }

    @ApiOperation(value = "下载用户导入模板")
    @GetMapping(value = "/downloadImportTemplate", produces = "application/octet-stream")
    @SysLog(value = "下载用户导入模板", optType = OptLogTypeEnum.OTHER)
    @PreAuth("hasAnyPermission('{}import')")
    public void downloadImportTemplate(HttpServletResponse response)
    {
        Workbook workbook = getImportWorkBook(Collections.emptyList());

        // 下载导入模板
        try
        {
            ExcelDownLoadUtil.export(response, workbook, "用户导入模板.xls");
        }
        catch (IOException e)
        {
            log.error("下载用户导入模板失败，原因:{}", e.getMessage(), e);
        }
    }

    /**
     * 获取导入模板的workbook
     *
     * @return Workbook workbook
     * <AUTHOR>
     * @date 2021/12/13 14:01
     * @update zxy 2021/12/13 14:01
     * @since 1.0
     */
    private Workbook getImportWorkBook(List<?> userExcels)
    {
        // 查询所有机构名称
        List<String> orgNames = orgService.listObjs(Wraps.<Org>lbQ().select(Org::getName).ne(Org::getState, 3), Convert::toStr);
        long dataUserNum = baseService.count();

        Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams(
                        "1、带*的列必须填写\n2、登录账号不能重复\n3、用户有多个角色时请用;隔开，角色不合法将不会导入角色\n4、导入成功的数据，默认密码为123456,请登录平台自行修改\n",
                        "用户信息"),
                UserExcelVO.class, userExcels);

        Row row = workbook.getSheetAt(0).getRow(0);
        // 设置title单元格行高
        row.setHeightInPoints(70);
        CellStyle cellStyle = row.getCell(0).getCellStyle();
        // 设置title单元格\n强制换行
        cellStyle.setWrapText(true);
        Font font = workbook.createFont();
        font.setBold(true);
        font.setColor(COLOR_RED);
        cellStyle.setFont(font);
        // 设置title单元格居左
        cellStyle.setAlignment(HorizontalAlignment.LEFT);

        //查询所有用户类型
        List<String> userType = dictionaryService
                .listObjs(Wraps.<Dictionary>lbQ().eq(Dictionary::getType, "USER_TYPE").ne(Dictionary::getCode, "LOCAL_TYPE").select(Dictionary::getName),
                        Convert::toStr);
        // 设置下拉选项
        ExcelExportPlusUtil.addDropDownList(workbook, workbook.getSheetAt(0), new String[]{"正常", "冻结"}, 2, 65535, 5);
        ExcelExportPlusUtil.addDropDownList(workbook, workbook.getSheetAt(0), orgNames.toArray(new String[0]), 2,
                65535, 6);
        ExcelExportPlusUtil.addDropDownList(workbook, workbook.getSheetAt(0), userType.toArray(new String[0]), 2,
                65535, 7);
        return workbook;
    }

    @ApiOperation(value = "导入Excel, 错误excel在extra中，fileName为文件名，fileStream为错误excel的base64编码字符串")
    @PostMapping(value = "/import")
    @SysLog(value = "'导入Excel:' + #simpleFile?.originalFilename", request = false, optType = OptLogTypeEnum.IMPORT)
    @PreAuth("hasAnyPermission('{}import')")
    public R<Boolean> importExcel(@RequestParam(value = "file") MultipartFile simpleFile) throws Exception
    {
        ImportParams params = new ImportParams();
        params.setTitleRows(1);
        params.setHeadRows(1);
        params.setNeedVerify(true);
        params.setVerifyGroup(new Class[]{Default.class});
        params.setVerifyHandler(userExcelVerifyHandler);
        params.setDictHandler(userExcelDictHandler);

        ExcelImportResult<UserExcelVO> result =
                ExcelImportUtil.importExcelMore(simpleFile.getInputStream(), UserExcelVO.class, params);

        // 清理ThreadLocal
        userExcelDictHandler.removeThreadLocal();
        userExcelVerifyHandler.removeThreadLocal();

        List<UserExcelVO> list = result.getList();
        if (!list.isEmpty())
        {
            List<User> userList = list.stream().map(item ->
            {
                User user = new User();
                BeanUtil.copyProperties(item, user);
                user.setSalt(RandomUtil.randomString(20));
//                String password = SecureUtil.sha256(BizConstant.DEF_PASSWORD + user.getSalt());
                String password = null;
                try {
                    password = encryptionConfig.encryptionService().encrypt(BizConstant.DEF_PASSWORD, user.getSalt());
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
                user.setPassword(password);
                List<UserRole> userRoles = new ArrayList<>();
                String roleId = item.getRoleId();
                if (StringUtil.isNotEmpty(roleId))
                {
                    String[] roleIds = roleId.split(";");
                    for (String str : roleIds)
                    {
                        userRoles.add(UserRole.builder().roleId(Long.parseLong(str)).build());
                    }
                }
                user.setUserRoles(userRoles);
                user.setReadonly(false);
                user.setAvatarType(true);
                user.setAvatarPath("avatar1");
                String type = user.getType();
                Dictionary userType =
                        dictionaryService.getOne(Wraps.<Dictionary>lbQ().eq(Dictionary::getType, "USER_TYPE")
                                .eq(Dictionary::getName, type), false);
                user.setType(userType.getCode());
                return user;
            }).collect(Collectors.toList());

            baseService.saveBatchUser(userList);
        }
        R<Boolean> r = success();
        r.setMsg("用户导入完成，共导入成功" + list.size() + "条，失败" + result.getFailList().size() + "条");
        if (result.isVerifyFail())
        {
            ByteArrayOutputStream bos = null;
            try
            {
                bos = new ByteArrayOutputStream();
                result.getFailWorkbook().write(bos);

                Map<Object, Object> extra = Maps.newHashMapWithExpectedSize(7);
                extra.put("fileName", "用户导入错误信息.xls");
                byte[] bytes = bos.toByteArray();
                extra.put("fileStream", Base64Utils.encodeToString(bytes));
                r.setExtra(extra);

                bos.flush();
            }
            catch (Exception e)
            {
                log.error("获取导入用户错误文件流失败,原因:{}", e.getMessage(), e);
            }
            finally
            {
                IoUtil.close(bos);
            }
        }
        return r;
    }

    /**
     * 对用户添加 上级组织机构信息
     *
     * @param user  用户实体
     */
    private void echoParentOrgList(User user) {
        Optional.ofNullable(user)
                .map(User::getOrgId)
                .map(orgService::findAllParent)
                .ifPresent(user::setOrgs);
    }

    @ApiOperation(value = "同步OA系统用户信息")
    @GetMapping(value = "/sync")
    @SysLog(value = "同步OA系统用户信息")
    public R<Boolean> handlerSync()
    {
        if (UserServiceImpl.syncOAUserState)
        {
            return fail("正在同步中，请稍后再试");
        }
        boolean isSync = baseService.syncOaUser();

        if (isSync)
        {
            return R.success(true, "正在同步，请稍后查看");
        }
        return fail("同步失败，请重试");
    }
}
