package com.jettech.jettong.base.controller.rbac.org;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.lang.tree.TreeNode;
import cn.hutool.core.util.StrUtil;
import com.jettech.basic.annotation.log.SysLog;
import com.jettech.basic.annotation.security.PreAuth;
import com.jettech.basic.base.R;
import com.jettech.basic.base.controller.SuperCacheController;
import com.jettech.basic.base.entity.SuperEntity;
import com.jettech.basic.base.entity.TreeEntity;
import com.jettech.basic.context.ContextUtil;
import com.jettech.basic.database.mybatis.conditions.Wraps;
import com.jettech.basic.database.mybatis.conditions.query.LbqWrapper;
import com.jettech.basic.database.mybatis.conditions.query.QueryWrap;
import com.jettech.basic.echo.core.EchoService;
import com.jettech.basic.exception.BizException;
import com.jettech.basic.utils.BeanPlusUtil;
import com.jettech.basic.utils.TreeUtil;
import com.jettech.jettong.base.dto.rbac.org.OrgPageQuery;
import com.jettech.jettong.base.dto.rbac.org.OrgSaveDTO;
import com.jettech.jettong.base.dto.rbac.org.OrgUpdateDTO;
import com.jettech.jettong.base.dto.rbac.org.OrgUpdateSortDTO;
import com.jettech.jettong.base.entity.rbac.org.Org;
import com.jettech.jettong.base.entity.rbac.team.SysTeam;
import com.jettech.jettong.base.service.rbac.org.OrgService;
import com.jettech.jettong.base.service.rbac.org.impl.OrgServiceImpl;
import com.jettech.jettong.base.vo.rbac.org.OrgUserVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.jettech.jettong.common.constant.SwaggerConstants.*;

/**
 * 组织机构处理控制器
 *
 * <AUTHOR>
 * @version 1.0
 * @description 组织机构处理控制器
 * @projectName jettong
 * @package com.jettech.jettong.base.controller.rbac.role
 * @className OrgController
 * @date 2021/9/15 9:43
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Slf4j
@RestController
@RequestMapping("/base/org")
@Api(value = "Org", tags = "组织机构管理")
@PreAuth(replace = "base:org:")
@RequiredArgsConstructor
public class OrgController extends SuperCacheController<OrgService, Long, Org, OrgPageQuery, OrgSaveDTO, OrgUpdateDTO>
{

    private final EchoService echoService;

    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", dataType = DATA_TYPE_LONG, paramType = PARAM_TYPE_QUERY),
            @ApiImplicitParam(name = "name", value = "名称", dataType = DATA_TYPE_STRING, paramType = PARAM_TYPE_QUERY),
    })
    @ApiOperation(value = "检测名称是否可用", notes = "检测名称是否可用")
    @GetMapping("/check")
    @SysLog(value = "检测名称是否可用", request = false)
    public R<Boolean> check(@RequestParam(required = false) Long id, @RequestParam String name)
    {
        return success(baseService.check(id, name));
    }

    @ApiOperation(value = "获取所有组织机构和用户", notes = "获取所有组织机构和用户")
    @GetMapping("/getOrgUser")
    public R<List<OrgUserVO>> getOrgUserVO()
    {
        return success(baseService.getOrgUserVO());
    }

    @Override
    public R<List<Org>> query(Org data)
    {
        QueryWrap<Org> wrapper = Wraps.q(data);
        wrapper.select(Org.class, i -> !"logo".equals(i.getProperty()));
        return success(getBaseService().list(wrapper));
    }

    @Override
    public R<Org> handlerSave(@Validated OrgSaveDTO model)
    {
        Org org = BeanPlusUtil.toBean(model, Org.class);
        baseService.save(org);
        return success(org);
    }

    @Override
    public R<Org> handlerUpdate(@Validated OrgUpdateDTO model)
    {
        Org org = BeanPlusUtil.toBean(model, Org.class);
        baseService.updateOrg(org);
        return success(org);
    }

    @ApiOperation(value = "修改机构排序和父机构", notes = "修改机构排序和父机构")
    @PutMapping("/updateSort")
    @PreAuth("hasAnyPermission('{}edit')")
    @SysLog(value = "修改机构排序和父机构", request = false)
    public R<Boolean> updateSort(@Validated @RequestBody OrgUpdateSortDTO orgUpdateSortDTO)
    {
        baseService.updateSort(orgUpdateSortDTO.getParentId(), orgUpdateSortDTO.getParentCode(),
                orgUpdateSortDTO.getOrgId(),
                orgUpdateSortDTO.getPreOrgId());
        return success();
    }

    @Override
    public R<Boolean> handlerDelete(List<Long> ids)
    {
        return this.success(baseService.remove(ids));
    }

    @ApiOperation(value = "查询系统所有的组织树", notes = "查询系统所有的组织树")
    @GetMapping("/tree")
    @PreAuth("hasAnyPermission('{}view')")
    @SysLog(value = "查询系统所有的组织树", request = false)
    public R<List<Org>> tree(@RequestParam(value = "name", required = false) String name,
            @RequestParam(value = "state", required = false) Integer state,
            @RequestParam(value = "team", required = false) Boolean team,
            @RequestParam(value = "leadingBy", required = false) String leadingBy)
    {
        List<Org> tree = baseService.tree(name, state, leadingBy);
        echoService.action(tree);
        if (team != null && team)
        {
            List<SysTeam> list = baseService.getTeamByTreeId(tree);
            List<Org> orgs = BeanUtil.copyToList(list, Org.class);
            tree.addAll(orgs);
        }
        return this.success(TreeUtil.buildTree(tree));
    }

    @ApiOperation(value = "查询当前登录用户所在机构的组织树", notes = "查询当前登录用户所在机构的组织树")
    @GetMapping("/sessionUser/tree")
    @PreAuth("hasAnyPermission('{}view')")
    @SysLog(value = "查询当前登录用户所在机构的组织树", request = false)
    public R<List<Org>> tree(@RequestParam(value = "name", required = false) String name,
            @RequestParam(value = "state", required = false) Integer state)
    {
        List<Org> tree = baseService.sessionUserTree(getOrgId(), name, state);
        echoService.action(tree);
        return this.success(TreeUtil.buildTree(tree));
    }

    @ApiOperation(value = "查询自己所在组织架构的logo", notes = "查询自己所在组织架构的logo")
    @GetMapping("/logo")
    @SysLog(value = "查询自己所在组织架构的logo", request = false)
    public R<String> logo()
    {
        Long orgId = ContextUtil.getOrgId();
        if (orgId == null)
        {
            throw BizException.wrap("当前用户未登录");
        }
        String logo = null;
        Long nowId = orgId;

        // 防止死循环，最大10次。循环向父级组织查询logo
        for (int i = 0; i < 10 && StrUtil.isBlank(logo); i++)
        {
            if (nowId == null || nowId == 0)
            {
                return R.success(null);
            }
            LbqWrapper<Org> wrapper = Wraps.lbQ();
            wrapper.select(Org::getId, Org::getParentId, Org::getLogo);
            wrapper.eq(SuperEntity::getId, nowId);
            Org org = baseService.getOne(wrapper);
            if (org == null)
            {
                throw BizException.wrap("获取logo失败");
            }
            logo = org.getLogo();
            nowId = org.getParentId();
        }

        return R.success(logo);
    }

    @ApiOperation(value = "查询机构信息")
    @GetMapping(value = "/selectOrgById")
    @SysLog(value = "查询机构信息")
    @PreAuth("hasAnyPermission('{}view')")
    public R selectOrgById(@RequestParam("orgId") Long orgId)
    {
        Org org = baseService.getById(orgId);
        boolean isFor = true;
        String name = org.getName();
        while (isFor)
        {
            if (org.getParentId() == null || org.getParentId() == 0)
            {
                isFor = false;
            }
            else
            {
                org = baseService.getById(org.getParentId());
                name = org.getName() + "/" + name;
            }
        }

        return R.success(name);
    }

    @ApiOperation(value = "根据机构ID查询下级机构信息,顶级机构parentId为0", notes = "根据机构ID查询下级机构信息，顶级机构parentId为0")
    @GetMapping("/child/{parentId}")
    @PreAuth("hasAnyPermission('{}view')")
    @SysLog(value = "查询当前登录用户所在机构的组织树", request = false)
    public R<List<Org>> selectChildOrgByParentId(@PathVariable("parentId") Long parentId,
            @RequestParam(required = false) String name)
    {
        List<Org> orgs = baseService.list(
                Wraps.<Org>lbQ().eq(TreeEntity::getParentId, parentId).like(TreeEntity::getName, name)
                        .ne(Org::getState, 3));
        echoService.action(orgs);
        return this.success(orgs);
    }

    @ApiOperation(value = "同步OA系统机构信息")
    @GetMapping(value = "/sync")
    @SysLog(value = "同步OA系统机构信息")
    public R<Boolean> handlerSync()
    {
        if (OrgServiceImpl.syncOAOrgState)
        {
            return fail("正在同步中，请稍后再试");
        }
        boolean isSync = baseService.syncOaOrg();

        if (isSync)
        {
            return R.success(true, "正在同步，请稍后查看");
        }
        return fail("同步失败，请重试");
    }

}
