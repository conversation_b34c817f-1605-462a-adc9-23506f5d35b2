package com.jettech.jettong.base.controller.sys.personalized;

import cn.hutool.core.util.StrUtil;
import com.jettech.basic.annotation.log.SysLog;
import com.jettech.basic.base.R;
import com.jettech.basic.base.controller.SuperSimpleController;
import com.jettech.basic.log.entity.OptLogTypeEnum;
import com.jettech.basic.utils.BeanPlusUtil;
import com.jettech.jettong.base.dto.sys.personalized.PersonalizedTableViewQuery;
import com.jettech.jettong.base.dto.sys.personalized.PersonalizedTableViewSaveDTO;
import com.jettech.jettong.base.dto.sys.personalized.PersonalizedTableViewSetDefaultDTO;
import com.jettech.jettong.base.dto.sys.personalized.PersonalizedTableViewUpdateDTO;
import com.jettech.jettong.base.entity.sys.personalized.PersonalizedTableView;
import com.jettech.jettong.base.service.sys.personalized.PersonalizedTableViewService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 用户自定义列表过滤器信息控制器
 *
 * <AUTHOR>
 * @version 1.0
 * @description 用户自定义列表过滤器信息控制器
 * @projectName jettong
 * @package com.jettech.jettong.base.controller.sys.personalized
 * @className PersonalizedTableViewController
 * @date 2021-11-20
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/base/personalizedTableSearchFilter")
@Api(value = "PersonalizedTableSearchFilter", tags = "用户自定义列表视图信息")
public class PersonalizedTableViewController
        extends SuperSimpleController<PersonalizedTableViewService, PersonalizedTableView>
{

    @ApiOperation(value = "新增")
    @PostMapping
    @SysLog(value = "新增", request = false, optType = OptLogTypeEnum.ADD)
    public R<PersonalizedTableView> handlerSave(@RequestBody @Validated PersonalizedTableViewSaveDTO model)
    {
        if (null == getUserId())
        {
            return validFail("未获取到当前登录用户信息");
        }
        PersonalizedTableView personalizedTableView =
                BeanPlusUtil.toBean(model, PersonalizedTableView.class);

        personalizedTableView.setUserId(getUserId());

        baseService.savePersonalizedTableView(personalizedTableView);

        return success(personalizedTableView);

    }

    @ApiOperation(value = "删除", notes = "删除")
    @DeleteMapping
    @SysLog(value = "'删除:' + #ids", optType = OptLogTypeEnum.DELETE)
    public R<Boolean> handlerDelete(@RequestBody List<Long> ids)
    {
        baseService.deletePersonalizedTableView(ids);
        return success();
    }

    @ApiOperation(value = "单个修改", notes = "单个修改")
    @PutMapping
    @SysLog(value = "单个修改", optType = OptLogTypeEnum.EDIT)
    public R<Boolean> handlerUpdate(@RequestBody @Validated PersonalizedTableViewUpdateDTO model)
    {
        if (null == getUserId())
        {
            return validFail("未获取到当前登录用户信息");
        }

        PersonalizedTableView personalizedTableView =
                BeanPlusUtil.toBean(model, PersonalizedTableView.class);
        baseService.updatePersonalizedTableView(personalizedTableView);
        return success();
    }

    @ApiOperation(value = "查询当前登录用户列表视图", notes = "查询当前登录用户列表视图")
    @PostMapping("/findByQuery")
    @SysLog(value = "查询当前登录用户列表视图")
    public R<List<PersonalizedTableView>> findByQuery(
            @RequestBody @Validated PersonalizedTableViewQuery query)
    {
        if (null == getUserId())
        {
            return validFail("未获取到当前登录用户信息");
        }

        List<PersonalizedTableView> viewList = baseService.findByUserIdAndTableCode(getUserId(), query.getTableCode());

        viewList.forEach(view -> {
            // 替换 用户ID
            String search = view.getSearch();
            if (StrUtil.isEmpty(search)) {
                return;
            }
            String replaced = search.replaceAll("\\{\\{userId}}", String.valueOf(getUserId()));
            view.setSearch(replaced);
        });

        return success(viewList);
    }

    @ApiOperation(value = "设置为默认视图", notes = "设置为默认视图")
    @PutMapping("/setDefault")
    @SysLog(value = "设置为默认视图", optType = OptLogTypeEnum.EDIT)
    public R<Boolean> setDefault(@RequestBody @Validated
            PersonalizedTableViewSetDefaultDTO model)
    {
        baseService.setDefault(model);
        return R.success();
    }

}
