package com.jettech.jettong.base.controller.echo;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import com.jettech.basic.annotation.base.IgnoreResponseBodyAdvice;
import com.jettech.basic.base.R;
import com.jettech.basic.database.mybatis.conditions.Wraps;
import com.jettech.basic.database.mybatis.conditions.query.LbqWrapper;
import com.jettech.basic.log.entity.OptLogDTO;
import com.jettech.basic.utils.ArgumentAssert;
import com.jettech.basic.utils.BeanPlusUtil;
import com.jettech.jettong.base.dto.sys.personalized.TableViewSaveDTO;
import com.jettech.jettong.base.entity.file.File;
import com.jettech.jettong.base.entity.rbac.org.Org;
import com.jettech.jettong.base.entity.rbac.team.SysTeam;
import com.jettech.jettong.base.entity.rbac.team.SysTeamUser;
import com.jettech.jettong.base.entity.rbac.user.User;
import com.jettech.jettong.base.entity.sys.dictionary.Dictionary;
import com.jettech.jettong.base.entity.sys.dictionary.DictionaryExtended;
import com.jettech.jettong.base.entity.sys.log.OptLog;
import com.jettech.jettong.base.entity.sys.personalized.PersonalizedTableView;
import com.jettech.jettong.base.entity.sys.personalized.TableView;
import com.jettech.jettong.base.enumeration.file.FileBizType;
import com.jettech.jettong.base.service.file.FileService;
import com.jettech.jettong.base.service.rbac.org.OrgService;
import com.jettech.jettong.base.service.rbac.team.SysTeamService;
import com.jettech.jettong.base.service.rbac.team.SysTeamUserService;
import com.jettech.jettong.base.service.rbac.user.UserService;
import com.jettech.jettong.base.service.sys.dictionary.DictionaryExtendedService;
import com.jettech.jettong.base.service.sys.dictionary.DictionaryService;
import com.jettech.jettong.base.service.sys.log.OptLogService;
import com.jettech.jettong.base.service.sys.personalized.PersonalizedTableViewService;
import com.jettech.jettong.base.service.sys.personalized.TableViewService;
import com.google.common.collect.Maps;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.io.Serializable;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 数据注入查询接口
 *
 * <AUTHOR>
 * @version 1.0
 * @description 数据注入查询接口
 * @projectName jettong-enterprises
 * @package com.jettech.jettong.base.controller.echo
 * @className EchoController
 * @date 2021/10/27 19:30
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/base")
@IgnoreResponseBodyAdvice
@Api(value = "数据注入查询接口", tags = "数据注入查询接口， 不建议前端调用")
@ApiIgnore
public class EchoController
{
    private final DictionaryService dictionaryService;
    private final DictionaryExtendedService dictionaryExtendedService;
    private final OrgService orgService;
    private final UserService userService;
    private final OptLogService optLogService;
    private final FileService fileService;
    private final SysTeamService sysTeamService;
    private final SysTeamUserService sysTeamUserService;
    private final TableViewService tableViewService;
    private final PersonalizedTableViewService personalizedTableViewService;

    @PostMapping("/echo/personalizedTableView/saveLast")
    public Boolean saveLastSearch(@RequestBody PersonalizedTableView lastView) {
        personalizedTableViewService.saveLastUseFilter(lastView);
        return true;
    }

    @GetMapping("/echo/tableView/findByIds")
    Map<Serializable, Object> findByIds(@RequestParam(value = "ids") Set<Serializable> ids) {
        if (CollUtil.isEmpty(ids)) {
            return new HashMap<>();
        }
        List<TableView> tableViews = tableViewService.listByIds(ids);
        return tableViews.stream().collect(Collectors.toMap(TableView::getId, Function.identity()));

    }


    @PostMapping("/echo/tableView/saveLast")
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveLastSearch(@RequestBody TableViewSaveDTO saveDTO) {
        Integer scope = saveDTO.getScope();
        ArgumentAssert.equals(scope, 3, "最后一次使用筛选记录的范围必须为3");
        Long ownerUser = saveDTO.getOwnerUser();
        ArgumentAssert.notNull(ownerUser, "最后一次使用筛选记录的所属用户不能为空");
        String tableType = saveDTO.getTableType();
        ArgumentAssert.notNull(tableType, "最后一次使用筛选记录的所属所在表不能为空");

        // 删除原来记录
        LbqWrapper<TableView> wrapper = Wraps.lbQ();
        wrapper.eq(TableView::getScope, scope)
                .eq(TableView::getOwnerUser, ownerUser)
                .eq(TableView::getTableType, tableType);
        tableViewService.remove(wrapper);

        TableView tableView = BeanPlusUtil.toBean(saveDTO, TableView.class);
        return tableViewService.save(tableView);
    }

    @PostMapping("/optLog/echo")
    @ApiOperation(value = "保存操作日志", notes = "保存保存操作日志不为空的字段")
    public R<OptLog> save(@RequestBody OptLogDTO data)
    {
        optLogService.save(data);
        return R.success(BeanPlusUtil.toBean(data, OptLog.class));
    }

    @ApiOperation(value = "根据id查询用户", notes = "根据id查询用户")
    @GetMapping("/user/findByIds")
    public Map<Serializable, Object> findUserByIds(@RequestParam(value = "ids") Set<Serializable> ids)
    {
        return userService.findByIds(ids);
    }

    @ApiOperation(value = "根据id集合查询用户信息", notes = "根据id集合查询用户信息")
    @GetMapping("/user/findByOrgIds")
    public List<User> findByOrgIds(@RequestParam(value = "orgIds") List<Long> orgIds)
    {
        return userService.findByOrgIds(orgIds);
    }

    @GetMapping("/org/findByIds")
    public Map<Serializable, Object> findOrgByIds(@RequestParam("ids") Set<Serializable> ids)
    {
        return orgService.findByIds(ids);
    }

    @ApiOperation(value = "查询字典项", notes = "根据字典编码查询字典项")
    @GetMapping("/dictionary/findByIds")
    public Map<Serializable, Object> findDictByIds(@RequestParam("ids") Set<Serializable> ids)
    {
        return dictionaryService.findByIds(ids);
    }

    @GetMapping("/dictionary/echo/findByParentKey/{dictionaryType}/{parentKey}")
    public List<Dictionary> findDictionaryByParentKey(@PathVariable("dictionaryType") String dictionaryType,
            @PathVariable("parentKey") String parentKey)
    {
        return dictionaryService.findByParentKey(dictionaryType, parentKey);
    }

    @PostMapping("/dictionary/echo/query")
    public List<Dictionary> query(@RequestBody Dictionary dictionary)
    {
        LbqWrapper<Dictionary> lbqWrapper = Wraps.<Dictionary>lbQ().isNotNull(Dictionary::getCode);
        if (null != dictionary)
        {
            if (StrUtil.isNotEmpty(dictionary.getParentCode()))
            {
                // 查询父级字典
                Dictionary parentDictionary = dictionaryService.getOne(
                        Wraps.<Dictionary>lbQ().eq(Dictionary::getCode, dictionary.getParentCode())
                                .eq(Dictionary::getType, dictionary.getType()), false);
                if (parentDictionary != null)
                {
                    dictionary.setParentId(parentDictionary.getId());
                    dictionary.setType(null);
                }
            }
            if (StrUtil.isNotEmpty(dictionary.getType()))
            {
                lbqWrapper.eq(Dictionary::getType, dictionary.getType());
            }
            if (StrUtil.isNotEmpty(dictionary.getCode()))
            {
                lbqWrapper.like(Dictionary::getCode, dictionary.getCode());
            }
            if (StrUtil.isNotEmpty(dictionary.getName()))
            {
                lbqWrapper.like(Dictionary::getName, dictionary.getName());
            }
            if (null != dictionary.getState())
            {
                lbqWrapper.eq(Dictionary::getState, dictionary.getState());
            }

            if (null != dictionary.getParentId())
            {
                lbqWrapper.eq(Dictionary::getParentId, dictionary.getParentId());
            }
        }
        List<Dictionary> dictionaries = dictionaryService.list(lbqWrapper.orderByAsc(Dictionary::getSort));

        dictionaries.forEach(item ->
        {
            List<DictionaryExtended> dictionaryExtendeds = dictionaryExtendedService.findByDictId(item.getId());

            item.setDictionaryExtendeds(dictionaryExtendeds);

            if (null != item.getParentId())
            {
                Map<String, Object> echoMap = Maps.newHashMapWithExpectedSize(1);

                echoMap.put("parentId", dictionaryService.getByIdCache(item.getParentId()));

                item.setEchoMap(echoMap);
            }

        });
        return dictionaries;
    }

    @GetMapping("/user/echo/{id}")
    public User findUserById(@PathVariable Long id)
    {
        return userService.getByIdCache(id);
    }

    @PostMapping("/user/echo/query")
    public List<User> findUserListByQuery(@RequestBody User query)
    {
        return userService.list(Wraps.q(query));
    }
    @GetMapping("/user/echo/queryLikeName")
    public List<User> findUserListLikeName(@RequestParam("name") String name)
    {
        return userService.list( Wraps.<User>lbQ().like(User::getName, name));
    }

    @PostMapping("/org/echo/query")
    public List<Org> findOrgListByQuery(@RequestBody Org query)
    {
        return orgService.list(Wraps.<Org>q(query).ne("state", 3));
    }

    @PostMapping("/org/echo/query/findOrgByOrgId")
    public Org findOrgByOrgId(@RequestParam(value = "id") Long id)
    {
        return orgService.getById(id);
    }


    @GetMapping("/org/echo/{orgId}/child")
    public List<Org> findChildrenOrgList(@PathVariable("orgId") Long orgId){
        return orgService.findChildren(orgId);
    }

    @GetMapping("/org/echo/{orgId}/parent")
    public List<Org> getParentOrgList(@PathVariable("orgId") Long orgId)
    {
        return orgService.findAllParent(orgId);
    }

    @DeleteMapping("/file/echo/{bizType}")
    public boolean removeByFileBizTypeAndBizIds(@PathVariable("bizType") FileBizType bizType, @RequestBody
            Collection<? extends Serializable> bizIds)
    {
        List<Long> fileIds = fileService.listObjs(
                Wraps.<File>lbQ().select(File::getId).eq(File::getBizType, bizType).in(File::getBizId, bizIds),
                Convert::toLong);
        if (fileIds.isEmpty())
        {
            return true;
        }
        return fileService.removeByIds(fileIds);
    }

    @DeleteMapping("/file/echo")
    public boolean removeByIds(@RequestBody Collection<? extends Serializable> ids)
    {
        if (ids.isEmpty())
        {
            return true;
        }
        return fileService.removeByIds(ids);
    }

    @PutMapping("/file/echo/batch")
    public boolean updateBatchById(@RequestBody List<File> files)
    {
        if (files.isEmpty())
        {
            return true;
        }
        return fileService.updateBatchById(files);
    }

    @PutMapping("/file/echo/saveBatch")
    public boolean saveBatch(@RequestBody List<File> files)
    {
        if (files.isEmpty())
        {
            return true;
        }
        return fileService.saveBatch(files);
    }

    @GetMapping("/file/echo/{bizType}/{bizId}")
    public List<File> findByBizTypeAndBizId(@PathVariable("bizType") FileBizType bizType,
            @PathVariable("bizId") Serializable bizId)
    {
        return fileService.list(Wraps.<File>lbQ().eq(File::getBizType, bizType).eq(File::getBizId, bizId));
    }

    @GetMapping("/file/echo/findFileByIds")
    public List<File> findFileByIds(@RequestParam("ids") List<Long> ids)
    {
        if (CollUtil.isEmpty(ids))
        {
            return Collections.emptyList();
        }
        return fileService.listByIds(ids);
    }

    @GetMapping("/file/echo/findByIds")
    public Map<Serializable, Object> findFileByIds(@RequestParam("ids") Set<Serializable> ids)
    {
        return fileService.findByIds(ids);
    }

    @PostMapping("/sysTeam/echo/listAll")
    public List<SysTeam> listAll()
    {
        return sysTeamService.list();
    }

    @PostMapping("/sysTeam/echo/insertSysTeamUser")
    public void insertSysTeamUser(@RequestBody List<SysTeamUser> teamUserList)
    {
        sysTeamUserService.saveBatchSomeColumn(teamUserList);
    }


    @PostMapping("/sysTeam/echo/listSysTeamUser")
    public List<SysTeamUser> listSysTeamUser(@RequestBody LbqWrapper<SysTeamUser> wrapper)
    {
        return sysTeamUserService.list(wrapper);
    }

    @PostMapping("/sysTeam/echo/saveSysTeam")
    public SysTeam saveSysTeam(@RequestBody SysTeam sysTeam)
    {
        sysTeamService.save(sysTeam);
        return sysTeam;
    }

    /**
     * 根据用户Id获取团队信息
     *
     * @param userId    用户Id
     * @return          团队信息
     */
    @GetMapping("/team/getSysTeamByUserId")
    public List<SysTeam> getSysTeamById(@RequestParam("userId") Long userId)
    {
        return sysTeamUserService.getTeamByUserId(userId);
    }

    /**
     * 根据用户Id获取团队信息
     *
     * @param userIds   用户Id
     * @return        团队信息
     */
    @GetMapping("/team/getSysTeamByUserIds")
    public Map<Long, List<SysTeam>> getSysTeamById(@RequestParam("userIds") List<Long> userIds)
    {
        return sysTeamUserService.getTeamByUserIds(userIds);
    }


    @PostMapping("/team/getTeamUsers")
    public List<Map<Long, List<User>>> getTeamUsers(@RequestBody List<Long> teamIds)
    {
        List<Map<Long, List<User>>> result = new ArrayList<>();

        List<SysTeamUser> sysTeamUsers =
                sysTeamUserService.list(Wraps.<SysTeamUser>lbQ().in(SysTeamUser::getTeamId, teamIds));

        Set<Long> userIds = sysTeamUsers.stream().map(SysTeamUser::getUserId).collect(Collectors.toSet());
        //获取全部人员信息
        List<User> users = userService.listByIds(userIds);
        //依据团队Id进行人员信息分组
        Map<Long, List<SysTeamUser>> sysTeamUserGroupByTeam = sysTeamUsers.stream()
                .collect(Collectors.groupingBy(SysTeamUser::getTeamId));

        sysTeamUserGroupByTeam.forEach((teamId, teamUsers) ->
        {

            Map<Long, List<User>> map = new HashMap<>();
            List<User> resultUsers = new ArrayList<>();

            List<Long> disUserList =
                    teamUsers.stream().map(SysTeamUser::getUserId).distinct().collect(Collectors.toList());

            disUserList.forEach(userId ->
            {

                List<User> collect =
                        users.stream().filter(a -> Objects.equals(a.getId(), userId)).collect(Collectors.toList());

                resultUsers.add(collect.get(0));
            });
            map.put(teamId, resultUsers);
            result.add(map);
        });

        return result;
    }

    @GetMapping("/dictionary/echo/{id}")
    public Dictionary findDictionaryById(@PathVariable Long id)
    {
        return dictionaryService.getByIdCache(id);
    }
}
