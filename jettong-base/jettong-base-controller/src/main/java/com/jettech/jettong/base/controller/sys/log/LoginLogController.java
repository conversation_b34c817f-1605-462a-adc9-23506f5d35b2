package com.jettech.jettong.base.controller.sys.log;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jettech.basic.annotation.log.SysLog;
import com.jettech.basic.annotation.security.PreAuth;
import com.jettech.basic.base.R;
import com.jettech.basic.base.controller.DeleteController;
import com.jettech.basic.base.controller.SuperSimpleController;
import com.jettech.basic.base.request.PageParams;
import com.jettech.basic.database.mybatis.conditions.Wraps;
import com.jettech.basic.database.mybatis.conditions.query.LbqWrapper;
import com.jettech.basic.log.entity.OptLogTypeEnum;
import com.jettech.jettong.base.dto.sys.log.LoginLogAndOptLogLineStackResult;
import com.jettech.jettong.base.dto.sys.log.LoginLogPageQuery;
import com.jettech.jettong.base.entity.sys.log.LoginLog;
import com.jettech.jettong.base.entity.sys.personalized.PersonalizedTableView;
import com.jettech.jettong.base.service.sys.log.LoginLogService;
import com.jettech.jettong.base.service.sys.personalized.PersonalizedTableViewService;
import com.jettech.jettong.common.dto.PeriodQuery;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;

import static com.jettech.jettong.common.constant.SwaggerConstants.DATA_TYPE_INT;
import static com.jettech.jettong.common.constant.SwaggerConstants.PARAM_TYPE_QUERY;

/**
 * 登录日志控制器
 *
 * <AUTHOR>
 * @version 1.0
 * @description 登录日志控制器
 * @projectName jettong
 * @package com.jettech.jettong.base.controller.sys.log
 * @className LoginLogController
 * @date 2021-10-15
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/base/loginLog")
@Api(value = "LoginLog", tags = "登录日志")
@PreAuth(replace = "base:loginLog:")
@RequiredArgsConstructor
public class LoginLogController extends SuperSimpleController<LoginLogService, LoginLog>
        implements DeleteController<LoginLog, Long>
{

    private final PersonalizedTableViewService tableViewService;

    @ApiOperation(value = "分页列表查询")
    @PostMapping("/page")
    @SysLog(value = "'分页列表查询:第' + #params?.current + '页, 显示' + #params?.size + '行'", response = false)
    @PreAuth("hasAnyPermission('{}view')")
    public R<IPage<LoginLog>> page(@RequestBody @Validated PageParams<LoginLogPageQuery> params)
    {
        IPage<LoginLog> page = params.buildPage(LoginLog.class);
        LoginLogPageQuery loginLogQuery = params.getModel();
        PeriodQuery<LocalDateTime> loginDate = loginLogQuery.getLoginDate();
        LbqWrapper<LoginLog> lbqWrapper = Wraps.<LoginLog>lbQ()
                .like(LoginLog::getAccount, loginLogQuery.getAccount())
                .like(LoginLog::getUserName, loginLogQuery.getUserName())
                .like(LoginLog::getRequestIp, loginLogQuery.getRequestIp());
        if(null != loginDate){
            if (null != loginDate.getStart() && null != loginDate.getEnd())
            {
                lbqWrapper.between(LoginLog::getLoginDate, loginDate.getStart(),
                        loginDate.getEnd());
            }
        }
        baseService.page(page, lbqWrapper);

        // 保存最后一次使用筛选记录，并作为默认视图
        tableViewService.saveLastUseFilter(
                PersonalizedTableView.lastView(getUserId(), "login-table", params)
        );

        return success(page);
    }

    @ApiOperation("清理日志")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "type",
                    value = "清理类型,1-一个月前，2-一个季度前，3-半年前，4-1年前，5-保留最新1千条，6-保留最新1万条，7-保留最新3万条，8-保留最新10万条",
                    dataType = DATA_TYPE_INT,
                    paramType = PARAM_TYPE_QUERY)
    })
    @DeleteMapping("/clear")
    @SysLog(value = "清理日志", optType = OptLogTypeEnum.DELETE)
    @PreAuth("hasAnyPermission('{}delete')")
    public R<Boolean> clear(@RequestParam(required = false, defaultValue = "1") Integer type)
    {
        LocalDateTime clearBeforeTime = null;
        Integer clearBeforeNum = null;
        switch (type)
        {
            // 清理一个月以前的日志数据
            case 1:
                clearBeforeTime = LocalDateTime.now().plusMonths(-1);
                break;
            // 清理三个月以前的日志数据
            case 2:
                clearBeforeTime = LocalDateTime.now().plusMonths(-3);
                break;
            // 清理六个月以前的日志数据
            case 3:
                clearBeforeTime = LocalDateTime.now().plusMonths(-6);
                break;
            // 清理一年以前的日志数据
            case 4:
                clearBeforeTime = LocalDateTime.now().plusMonths(-12);
                break;
            // 清理一千条以前日志数据
            case 5:
                clearBeforeNum = 1000;
                break;
            // 清理一万条以前日志数据
            case 6:
                clearBeforeNum = 10000;
                break;
            // 清理三万条以前日志数据
            case 7:
                clearBeforeNum = 30000;
                break;
            // 清理十万条以前日志数据
            case 8:
                clearBeforeNum = 100000;
                break;
            default:
                return fail("非法参数");
        }

        return success(baseService.clearLog(clearBeforeTime, clearBeforeNum));
    }

    @ApiOperation("查询近一个月的登录日志和操作日志折线图数据，用于工作台使用")
    @GetMapping("/getLastMonthLoginLogAndOptLogLineStack")
    @SysLog(value = "查询近一个月的登录日志和操作日志折线图数据")
    public R<LoginLogAndOptLogLineStackResult> getLastMonthLoginLogAndOptLogLineStack()
    {
        return success(baseService.getLastMonthLoginLogAndOptLogLineStack());
    }

}
