package com.jettech.jettong.base.controller.rbac.tenant;

import com.jettech.basic.annotation.log.SysLog;
import com.jettech.basic.base.R;
import com.jettech.basic.database.mybatis.conditions.Wraps;
import com.jettech.jettong.base.entity.rbac.tenant.TenantPlatformConfig;
import com.jettech.jettong.base.service.rbac.tenant.TenantPlatformConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import static com.jettech.basic.base.R.success;

/**
 *
 *
 * <AUTHOR>
 * @version 1.0
 * @description
 * @projectName jettong
 * @package com.jettech.jettong.base.controller.file
 * @className LicenseVerifyController
 * @date 2021/9/15 9:43
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Slf4j
@RestController
@RequestMapping("/tenantPlatformConfig/noToken")
@AllArgsConstructor
@Api(value = "查询企业设置信息", tags = "查询企业设置信息")
public class TenantPlatformConfigLoginController
{
    private final TenantPlatformConfigService tenantPlatformConfigService;

    @ApiOperation(value = "根据Code查询", notes = "根据Code查询")
    @GetMapping("/findByCode/{code}")
    @SysLog(value = "根据Code查询")
    public R<TenantPlatformConfig> getByCode(@PathVariable String code)
    {
        return success(
                tenantPlatformConfigService.getOne(Wraps.<TenantPlatformConfig>lbQ().eq(TenantPlatformConfig::getCode, code), false));
    }
}
