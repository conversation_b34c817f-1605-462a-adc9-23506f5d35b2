package com.jettech.jettong.base.controller.rbac.role;

import com.jettech.basic.annotation.log.SysLog;
import com.jettech.basic.annotation.security.PreAuth;
import com.jettech.basic.base.R;
import com.jettech.basic.base.controller.SuperCacheController;
import com.jettech.basic.utils.BeanPlusUtil;
import com.jettech.jettong.base.dto.rbac.role.FunctionSaveDTO;
import com.jettech.jettong.base.dto.rbac.role.FunctionUpdateDTO;
import com.jettech.jettong.base.entity.rbac.role.Function;
import com.jettech.jettong.base.service.rbac.role.FunctionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import static com.jettech.jettong.common.constant.SwaggerConstants.*;

/**
 * 菜单功能处理控制器
 *
 * <AUTHOR>
 * @version 1.0
 * @description 菜单功能处理控制器
 * @projectName jettong
 * @package com.jettech.jettong.base.controller.rbac.role
 * @className FunctionController
 * @date 2021/9/15 9:43
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/base/function")
@Api(value = "Function", tags = "菜单功能管理")
@PreAuth(replace = "base:function:")
public class FunctionController
        extends SuperCacheController<FunctionService, Long, Function, Function, FunctionSaveDTO, FunctionUpdateDTO>
{

    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", dataType = DATA_TYPE_LONG, paramType = PARAM_TYPE_QUERY),
            @ApiImplicitParam(name = "menuId", value = "菜单id", dataType = DATA_TYPE_STRING,
                    paramType = PARAM_TYPE_QUERY),
            @ApiImplicitParam(name = "code", value = "编码", dataType = DATA_TYPE_STRING, paramType = PARAM_TYPE_QUERY),
    })
    @ApiOperation(value = "检测功能编码是否可用", notes = "检测功能编码是否可用")
    @GetMapping("/check")
    @SysLog(value = "检测功能是否可用", request = false)
    public R<Boolean> check(@RequestParam(required = false) Long id, @RequestParam(required = false) Long menuId,
            @RequestParam String code)
    {
        return success(baseService.check(id, menuId, code));
    }

    @Override
    public R<Function> handlerSave(@Validated FunctionSaveDTO data)
    {
        Function function = BeanPlusUtil.toBean(data, Function.class);
        baseService.saveWithCache(function);
        return success(function);
    }

    @Override
    public R<Boolean> handlerDelete(List<Long> ids)
    {
        return success(baseService.removeByIdWithCache(ids));
    }

    @Override
    public R<Function> handlerUpdate(@Validated FunctionUpdateDTO data)
    {
        Function function = BeanPlusUtil.toBean(data, Function.class);
        baseService.updateById(function);
        return success(function);
    }

}
