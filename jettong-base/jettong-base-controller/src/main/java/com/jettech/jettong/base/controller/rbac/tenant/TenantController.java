package com.jettech.jettong.base.controller.rbac.tenant;


import com.jettech.basic.annotation.log.SysLog;
import com.jettech.basic.annotation.security.PreAuth;
import com.jettech.basic.base.R;
import com.jettech.basic.base.controller.SuperCacheController;
import com.jettech.basic.database.mybatis.conditions.Wraps;
import com.jettech.basic.utils.BeanPlusUtil;
import com.jettech.jettong.base.dto.rbac.tenant.TenantPageQuery;
import com.jettech.jettong.base.dto.rbac.tenant.TenantSaveDTO;
import com.jettech.jettong.base.dto.rbac.tenant.TenantUpdateDTO;
import com.jettech.jettong.base.entity.rbac.tenant.Tenant;
import com.jettech.jettong.base.enumeration.rbac.TenantStatus;
import com.jettech.jettong.base.service.rbac.tenant.TenantService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 租户前端控制器
 *
 * <AUTHOR>
 * @version 1.0
 * @description 租户前端控制器
 * @projectName jettong
 * @package com.jettech.jettong.base.controller.rbac.tenant
 * @className TenantController
 * @date 2021/9/15 9:43
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/tenant")
@Api(value = "Tenant", tags = "租户")
@SysLog(enabled = false)
public class TenantController
        extends SuperCacheController<TenantService, Long, Tenant, TenantPageQuery, TenantSaveDTO, TenantUpdateDTO>
{

    @Override
    public R<Tenant> handlerSave(TenantSaveDTO model)
    {
        Tenant tenant = BeanPlusUtil.toBean(model, Tenant.class);
        baseService.saveTenant(tenant);
        return success(tenant);
    }

    @Override
    @PreAuth("hasAnyRole('PT_ADMIN')")
    public R<Boolean> handlerDelete(List<Long> ids)
    {
        baseService.deleteTenant(ids);
        return success();
    }

    @ApiOperation(value = "删除租户和基础租户数据")
    @DeleteMapping("/deleteAll")
    @PreAuth("hasAnyRole('PT_ADMIN')")
    public R<Boolean> deleteAll(@RequestBody List<Long> ids)
    {
        baseService.deleteAll(ids);
        return success();
    }

    @Override
    public R<Tenant> handlerUpdate(TenantUpdateDTO model)
    {
        Tenant tenant = BeanPlusUtil.toBean(model, Tenant.class);
        baseService.updateTenant(tenant);
        return success(tenant);
    }

    @ApiOperation(value = "查询所有可用租户", notes = "查询所有可用租户")
    @GetMapping("/all")
    public R<List<Tenant>> list()
    {
        return success(baseService.list(Wraps.<Tenant>lbQ().eq(Tenant::getStatus, TenantStatus.NORMAL)));
    }

    @ApiOperation(value = "检测租户是否存在", notes = "检测租户是否存在")
    @GetMapping("/check/{code}")
    public R<Boolean> check(@PathVariable("code") String code)
    {
        return success(baseService.check(code));
    }

    @ApiOperation(value = "修改租户状态", notes = "修改租户状态")
    @PostMapping("/status")
    public R<Boolean> updateStatus(@RequestParam("ids[]") List<Long> ids,
            @RequestParam(defaultValue = "FORBIDDEN") @NotNull(message = "状态不能为空") TenantStatus status)
    {
        baseService.updateStatus(ids, status);
        return success();
    }

}
