package com.jettech.jettong.base.controller.sys.dictionary;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jettech.basic.annotation.log.SysLog;
import com.jettech.basic.annotation.security.PreAuth;
import com.jettech.basic.base.R;
import com.jettech.basic.base.controller.SuperCacheController;
import com.jettech.basic.base.request.PageParams;
import com.jettech.basic.database.mybatis.conditions.Wraps;
import com.jettech.basic.database.mybatis.conditions.query.LbqWrapper;
import com.jettech.basic.log.entity.OptLogTypeEnum;
import com.jettech.basic.utils.ArgumentAssert;
import com.jettech.basic.utils.BeanPlusUtil;
import com.jettech.jettong.base.dto.sys.dictionary.DictionaryPageQuery;
import com.jettech.jettong.base.dto.sys.dictionary.DictionarySaveDTO;
import com.jettech.jettong.base.dto.sys.dictionary.DictionaryUpdateDTO;
import com.jettech.jettong.base.entity.rbac.tenant.TenantPlatformConfig;
import com.jettech.jettong.base.entity.sys.dictionary.Dictionary;
import com.jettech.jettong.base.entity.sys.dictionary.DictionaryExtended;
import com.jettech.jettong.base.entity.sys.dictionary.DictionaryType;
import com.jettech.jettong.base.service.sys.dictionary.DictionaryExtendedService;
import com.jettech.jettong.base.service.sys.dictionary.DictionaryService;
import com.google.common.collect.Maps;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.jettech.jettong.common.constant.SwaggerConstants.*;


/**
 * 数据字典信息表控制器
 *
 * <AUTHOR>
 * @version 1.0
 * @description 数据字典信息表控制器
 * @projectName jettong
 * @package com.jettech.jettong.base.controller
 * @className DictionaryController
 * @date 2021-10-15
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/base/dictionary")
@Api(value = "Dictionary", tags = "字典管理")
@PreAuth(replace = "base:dictionary:")
@RequiredArgsConstructor
public class DictionaryController
        extends
        SuperCacheController<DictionaryService, Long, Dictionary, DictionaryPageQuery, DictionarySaveDTO,
                DictionaryUpdateDTO> {
    private final DictionaryExtendedService dictionaryExtendedService;

    @Override
    public R<Dictionary> handlerSave(DictionarySaveDTO dictionarySaveDTO) {
        Dictionary dictionary = BeanPlusUtil.toBean(dictionarySaveDTO, Dictionary.class);
        fillDictionarySaveDTO(dictionary);
        baseService.saveDictionary(dictionary);
        return success(dictionary);
    }

    private void fillDictionarySaveDTO(Dictionary dictionary) {
        if (null == dictionary.getReadonly()) {
            dictionary.setReadonly(false);
        }
        if (null == dictionary.getState()) {
            dictionary.setState(true);
        }
        if (null == dictionary.getSort()) {
            dictionary.setSort(0);
        }
    }

    @Override
    public R<Boolean> handlerDelete(List<Long> ids) {
        baseService.remove(ids);
        return success();
    }

    @Override
    public R<Dictionary> handlerUpdate(DictionaryUpdateDTO dictionaryUpdateDTO) {
        Dictionary dictionary = BeanPlusUtil.toBean(dictionaryUpdateDTO, Dictionary.class);
        baseService.updateDictionary(dictionary);
        return success(dictionary);
    }

    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "字典id", dataType = DATA_TYPE_LONG,
                    paramType = PARAM_TYPE_PATH),
            @ApiImplicitParam(name = "state", value = "是否正常", dataType = DATA_TYPE_BOOLEAN,
                    paramType = PARAM_TYPE_PATH)
    })
    @ApiOperation(value = "修改字典状态", notes = "修改字典状态")
    @PutMapping("/updateStateById/{id}/{state}")
    @SysLog("修改字典状态")
    public R<Dictionary> updateStateById(@PathVariable("id") Long id, @PathVariable("state") Boolean state) {
        return success(baseService.updateStateById(id, state));
    }

    @Override
    public R<Dictionary> get(Long id) {
        return success(baseService.getByIdCache(id));
    }

    @ApiImplicitParams({
            @ApiImplicitParam(name = "code", value = "编码", required = true, dataType = DATA_TYPE_STRING,
                    paramType = PARAM_TYPE_QUERY),

            @ApiImplicitParam(name = "type", value = "字典类型", required = true, dataType = DATA_TYPE_STRING,
                    paramType = PARAM_TYPE_QUERY),

            @ApiImplicitParam(name = "parentId", value = "父id", dataType = DATA_TYPE_STRING,
                    paramType = PARAM_TYPE_QUERY)
    })
    @ApiOperation(value = "检测编码是否可用", notes = "检测编码是否可用")
    @GetMapping("/check")
    @SysLog(value = "检测编码是否可用", request = false)
    public R<Boolean> check(@RequestParam("code") String code, @RequestParam("type") String type,
                            @RequestParam(value = "parentId", required = false) Long parentId) {
        return success(
                baseService.check(code, type, parentId));
    }

    @Override
    public IPage<Dictionary> query(PageParams<DictionaryPageQuery> params) {
        IPage<Dictionary> page = params.buildPage(Dictionary.class);
        DictionaryPageQuery dictionaryPage = params.getModel();

        LbqWrapper<Dictionary> wrapper = Wraps.lbQ();

        if (StrUtil.isNotEmpty(dictionaryPage.getParentCode())) {
            // 查询父级字典
            Dictionary parentDictionary = baseService.getOne(
                    Wraps.<Dictionary>lbQ().eq(Dictionary::getCode, dictionaryPage.getParentCode())
                            .eq(Dictionary::getType, dictionaryPage.getType()), false);
            if (parentDictionary != null) {
                dictionaryPage.setParentId(parentDictionary.getId());
                dictionaryPage.setType(null);
            }
        }

        wrapper.like(Dictionary::getName, dictionaryPage.getName())
                .like(Dictionary::getCode, dictionaryPage.getCode())
                .isNotNull(Dictionary::getCode)
                .eq(Dictionary::getType, dictionaryPage.getType())
                .eq(Dictionary::getPlatform, dictionaryPage.getPlatform())
                .eq(Dictionary::getState, dictionaryPage.getState())
                .eq(Dictionary::getParentId, dictionaryPage.getParentId());
        baseService.page(page, wrapper);
        // 手动注入
        page.getRecords().forEach(item ->
        {
            List<DictionaryExtended> dictionaryExtendeds = dictionaryExtendedService.findByDictId(item.getId());

            item.setDictionaryExtendeds(dictionaryExtendeds);

            if (null != item.getParentId()) {
                Map<String, Object> echoMap = Maps.newHashMapWithExpectedSize(1);

                echoMap.put("parentId", baseService.getByIdCache(item.getParentId()));

                item.setEchoMap(echoMap);
            }

        });

        return page;
    }

    @Override
    @ApiImplicitParams({
            @ApiImplicitParam(name = "type", value = "字典类型，如果参数中包含parentId和parentCoded时，传父级字典的类型且该字段必填",
                    dataType = DATA_TYPE_STRING,
                    required = true, paramType = PARAM_TYPE_BODY),
            @ApiImplicitParam(name = "code", value = "编码，模糊查询", dataType = DATA_TYPE_STRING,
                    paramType = PARAM_TYPE_BODY),
            @ApiImplicitParam(name = "name", value = "字典名称，模糊查询", dataType = DATA_TYPE_STRING,
                    paramType = PARAM_TYPE_BODY),
            @ApiImplicitParam(name = "state", value = "状态", dataType = DATA_TYPE_BOOLEAN,
                    paramType = PARAM_TYPE_BODY),
            @ApiImplicitParam(name = "parentId", value = "父级字典id", dataType = DATA_TYPE_LONG,
                    paramType = PARAM_TYPE_BODY),
            @ApiImplicitParam(name = "parentCode", value = "父级字典Code", dataType = DATA_TYPE_LONG,
                    paramType = PARAM_TYPE_BODY)
    })
    public R<List<Dictionary>> query(@ApiIgnore @RequestBody Dictionary dictionary) {
        LbqWrapper<Dictionary> lbqWrapper = Wraps.<Dictionary>lbQ()
                .isNotNull(Dictionary::getCode);
        if (null != dictionary) {
            if (StrUtil.isNotEmpty(dictionary.getParentCode())) {
                // 查询父级字典
                Dictionary parentDictionary = baseService.getOne(
                        Wraps.<Dictionary>lbQ().eq(Dictionary::getCode, dictionary.getParentCode())
                                .eq(Dictionary::getType, dictionary.getType()), false);
                if (parentDictionary != null) {
                    dictionary.setParentId(parentDictionary.getId());
                    dictionary.setType(null);
                }
            }
            if (StrUtil.isNotEmpty(dictionary.getType())) {
                lbqWrapper.eq(Dictionary::getType, dictionary.getType());
            }
            if (StrUtil.isNotEmpty(dictionary.getCode())) {
                lbqWrapper.like(Dictionary::getCode, dictionary.getCode());
            }
            if (StrUtil.isNotEmpty(dictionary.getName())) {
                lbqWrapper.like(Dictionary::getName, dictionary.getName());
            }
            if (null != dictionary.getState()) {
                lbqWrapper.eq(Dictionary::getState, dictionary.getState());
            }

            if (null != dictionary.getParentId()) {
                lbqWrapper.eq(Dictionary::getParentId, dictionary.getParentId());
            }
        }
        List<Dictionary> dictionaries = baseService.list(lbqWrapper.orderByAsc(Dictionary::getSort));

        dictionaries.forEach(item ->
        {
            List<DictionaryExtended> dictionaryExtendeds = dictionaryExtendedService.findByDictId(item.getId());

            item.setDictionaryExtendeds(dictionaryExtendeds);

            if (null != item.getParentId()) {
                Map<String, Object> echoMap = Maps.newHashMapWithExpectedSize(1);

                echoMap.put("parentId", baseService.getByIdCache(item.getParentId()));

                item.setEchoMap(echoMap);
            }

        });

        return R.success(dictionaries);
    }

    @ApiOperation(value = "查询所有字典类型", notes = "查询所有字典类型")
    @GetMapping("/findAllDictionaryType")
    @SysLog(value = "查询所有字典类型", request = false)
    public R<List<DictionaryType>> findAllDictionaryType() {
        // 查询所有字典
        List<Dictionary> dictionaries = baseService.list();

        // 按类型分组
        Map<String, List<Dictionary>> dictionaryMap =
                dictionaries.stream().collect(Collectors.groupingBy(Dictionary::getType));

        List<DictionaryType> dictionaryTypes = new ArrayList<>();
        for (Map.Entry<String, List<Dictionary>> entry : dictionaryMap.entrySet()) {
            Integer count = Math.toIntExact(
                    entry.getValue().stream().filter(item -> null != item.getCode()).count());
            dictionaryTypes.add(DictionaryType.builder().type(entry.getKey()).label(entry.getValue().get(0).getLabel())
                    .dictionaryNum(count).build());
        }

        return success(dictionaryTypes);
    }

    @ApiOperation(value = "批量推送字典类型", notes = "无权限校验，自动编码检测")
    @PostMapping("/noToken/addBatchDictionaryType")
    @SysLog(value = "批量推送字典类型接口", optType = OptLogTypeEnum.ADD)
    public R<Boolean> addBatchDictionaryType(@Validated @RequestBody List<DictionarySaveDTO> dictionarySaveDTOs) {
        //判断dictionarys不为空
        ArgumentAssert.notNull(dictionarySaveDTOs, "字典列表不能为空");
        List<Dictionary> dictionarys = BeanPlusUtil.toBeanList(dictionarySaveDTOs, Dictionary.class);
        Boolean data = baseService.addBatchDictionaryType(dictionarys);
        return success(data);
    }

}
