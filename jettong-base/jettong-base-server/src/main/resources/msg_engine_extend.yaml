- engineInstance: FEI_SHU
  attributes:
    - key: url
      name: 服务器地址
      inputType: INPUT
      isCheck: true
      maxlength: 1000
      message: url地址不能为空
      isVersion: true
      isMore: true
      notEmpty: true
    - key: appId
      name: 飞书应用ID
      inputType: INPUT
      isCheck: true
      maxlength: 1000
      message: 飞书应用ID不能为空
      isVersion: true
      isMore: true
      notEmpty: true
    - key: appSecret
      name: 飞书应用Secret
      inputType: INPUT
      isCheck: true
      maxlength: 1000
      message: 飞书应用Secret不能为空
      isVersion: true
      isMore: true
      notEmpty: true
- engineInstance: MAIL
  attributes:
    - key: host
      name: 邮箱服务器地址
      inputType: INPUT
      isCheck: true
      maxlength: 1000
      message: 服务器地址地址不能为空
      isVersion: true
      isMore: true
      notEmpty: true
    - key: port
      name: 邮箱服务器端口
      inputType: INPUT
      regexp: ^([1-9][0-9]+)$
      isCheck: true
      maxlength: 1000
      message: 邮箱服务器端口为数值类型
      isVersion: true
      isMore: true
      notEmpty: true
    - key: username
      name: 邮箱账号
      inputType: INPUT
      isCheck: true
      maxlength: 1000
      message: 邮箱账号不能为空
      isVersion: true
      isMore: true
      notEmpty: true
    - key: password
      name: 邮箱密码
      inputType: INPUT
      isCheck: true
      maxlength: 1000
      message: 邮箱密码不能为空
      isVersion: true
      isMore: true
      notEmpty: true