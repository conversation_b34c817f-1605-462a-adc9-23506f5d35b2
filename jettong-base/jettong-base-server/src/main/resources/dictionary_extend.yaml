- dictionaryType: ENVIRONMENT
  parentKey: ENVIRONMENT_TYPE
  extendList:
    - key: tagStyle
      name: 颜色
      controlType: el-color-picker
      notEmpty: true
- dictionaryType: ENGINE_INSTANCE
  parentKey: ENGINE_CLASSIFY
  extendList:
    - key: OPNE_PROCESS_CODE
      name: 配置流程
      inputType: INPUT
      notEmpty: false
- dictionaryType: BRANCH
  parentKey: BRANCHING_STRATEGY
- dictionaryType: SCRIPT_FUNCTION
  parentKey: SCRIPT_TYPE
- dictionaryType: RELEASE_TYPE
  extendList:
    - key: RELEASE_PLAN
      name: 发布计划审批流程
      inputType: INPUT
      notEmpty: true
    - key: RELEASE_WORKORDES
      name: 发布工单审批流程
      inputType: INPUT
      notEmpty: true
    - key: tagStyle
      name: 颜色
      controlType: el-color-picker
      notEmpty: true
- dictionaryType: PROJECT_TYPE
  extendList:
    - key: PROJECT_START
      name: 立项流程
      inputType: INPUT
      notEmpty: true
    - key: PROJECT_CLOSE
      name: 结项流程
      inputType: INPUT
      notEmpty: true
    - key: tagStyle
      name: 颜色
      controlType: el-color-picker
      notEmpty: true
