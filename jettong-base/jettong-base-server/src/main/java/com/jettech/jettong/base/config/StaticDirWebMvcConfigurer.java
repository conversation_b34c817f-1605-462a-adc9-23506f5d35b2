package com.jettech.jettong.base.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.ScopedProxyMode;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * 静态目录配置
 *
 * <AUTHOR>
 * @version 1.0
 * @description 静态目录配置
 * @projectName jettong-base-cloud
 * @package com.jettech.jettong.base.config
 * @className StaticDirWebMvcConfigurer
 * @date 2021/10/23 14:32
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Configuration
@RefreshScope(proxyMode = ScopedProxyMode.DEFAULT)
public class StaticDirWebMvcConfigurer implements WebMvcConfigurer
{

    @Value("${web.webapps-path}")
    private String webappsPath;

    @Value("${jettong.file.local.storagePath}")
    private String fileLocalStoragePath;

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry)
    {
        registry.addResourceHandler("/webapps/**")
                .addResourceLocations("file:" + webappsPath, "file:" + fileLocalStoragePath);
        registry.addResourceHandler("/static/**")
                .addResourceLocations("classpath:/resources/static/", "classpath:/static/");
    }

}
