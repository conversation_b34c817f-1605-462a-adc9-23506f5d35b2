package com.jettech.jettong.base.config.datasource;


import com.jettech.basic.database.datasource.BaseMybatisConfiguration;
import com.jettech.basic.database.properties.DatabaseProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Configuration;


/**
 * Mybatis 常用重用拦截器
 *
 * <AUTHOR>
 * @version 1.0
 * @description Mybatis 常用重用拦截器
 * @projectName jettong
 * @package com.jettech.jettong.base.config.datasource
 * @className BaseMybatisAutoConfiguration
 * @date 2021/10/20 15:24
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Configuration
@Slf4j
@EnableConfigurationProperties({DatabaseProperties.class})
public class BaseMybatisAutoConfiguration extends BaseMybatisConfiguration
{

    public BaseMybatisAutoConfiguration(DatabaseProperties databaseProperties)
    {
        super(databaseProperties);
    }
}
