package com.jettech.jettong.base.config;

import com.jettech.basic.boot.config.BaseConfig;
import com.jettech.basic.log.event.SysLogListener;
import com.jettech.jettong.base.service.sys.log.OptLogService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.ScopedProxyMode;

/**
 * <AUTHOR>
 * @date 2017-12-15 14:42
 */
@Configuration
@RefreshScope(proxyMode = ScopedProxyMode.DEFAULT)
public class BaseWebConfiguration extends BaseConfig
{

    @Value("${jettong.log.query:true}")
    private Boolean query;

    /**
     * jettong.log.enabled = true 并且 jettong.log.type=DB时实例该类
     */
    @Bean
    @ConditionalOnExpression("${jettong.log.enabled:true} && 'DB'.equals('${jettong.log.type:LOGGER}')")
    public SysLogListener sysLogListener(OptLogService optLogService)
    {
        return new SysLogListener(optLogService::save, query);
    }

}
