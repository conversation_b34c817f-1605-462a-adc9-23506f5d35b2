package com.jettech.jettong.base.api.hystrix;

import com.jettech.jettong.base.api.UserApi;
import com.jettech.jettong.base.entity.rbac.user.User;
import org.springframework.stereotype.Component;

import java.io.Serializable;
import java.util.*;

/**
 * 用户相关API熔断
 *
 * <AUTHOR>
 * @version 1.0
 * @description 用户相关API熔断
 * @projectName jettong-enterprises
 * @package com.jettech.jettong.base.api.hystrix
 * @className UserApiFallback
 * @date 2021/10/22 10:44
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Component
public class UserApiFallback implements UserApi
{
    @Override
    public Map<Serializable, Object> findByIds(Set<Serializable> ids)
    {
        return Collections.emptyMap();
    }

    @Override
    public Map<Long, User> selectByIds(Collection<Long> ids) {
        return Collections.emptyMap();
    }

    @Override
    public User findUserById(Long id)
    {
        return null;
    }

    @Override
    public List<User> findUserListByQuery(User query)
    {
        return Collections.emptyList();
    }

    @Override
    public List<User> findUserListLikeName(String name) {
        return Collections.emptyList();
    }

    @Override
    public List<User> findByOrgIds(List<Long> orgId) {
        return Collections.emptyList();
    }
}
