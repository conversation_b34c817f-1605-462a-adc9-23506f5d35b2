package com.jettech.jettong.base.api;

import com.jettech.basic.model.LoadService;
import com.jettech.jettong.base.api.hystrix.UserApiFallback;
import com.jettech.jettong.base.entity.rbac.user.User;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 用户相关API
 *
 * <AUTHOR>
 * @version 1.0
 * @description 用户相关API
 * @projectName jettong-enterprises
 * @package com.jettech.jettong.base.api
 * @className UserApi
 * @date 2021/10/22 10:44
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@FeignClient(name = "${jettong.feign.base-server:jettong-base-server}", fallback = UserApiFallback.class
        , path = "/base/user")
public interface UserApi extends LoadService
{
    /**
     * 根据id查询待回显参数
     *
     * @param ids 唯一键（可能不是主键ID)
     * @return Map<Serializable, Object> 待回显参数
     * <AUTHOR>
     * @date 2021/9/13 10:11
     * @update zxy 2021/9/13 10:11
     * @since 1.0
     */
    @Override
    @GetMapping("/findByIds")
    Map<Serializable, Object> findByIds(@RequestParam(value = "ids") Set<Serializable> ids);

    /**
     * 根据id查询待回显参数
     *
     * @param ids 唯一键（可能不是主键ID)
     * @return Map<Serializable, Object> 待回显参数
     * <AUTHOR>
     * @date 2021/9/13 10:11
     * @update zxy 2021/9/13 10:11
     * @since 1.0
     */
    @GetMapping("/findByIds")
    Map<Long, User> selectByIds(@RequestParam(value = "ids") Collection<Long> ids);

    /**
     * 根据id查询用户信息
     *
     * @param id 用户id
     * @return User 用户信息
     * <AUTHOR>
     * @date 2022/5/23 17:25
     * @update zxy 2022/5/23 17:25
     * @since 1.0
     */
    @GetMapping("/echo/{id}")
    User findUserById(@PathVariable("id") Long id);

    /**
     * 根据条件查询用户信息
     *
     * @param user 查询条件
     * @return List<User> 用户信息
     * <AUTHOR>
     * @date 2022/5/23 17:26
     * @update zxy 2022/5/23 17:26
     * @since 1.0
     */
    @PostMapping("/echo/query")
    List<User> findUserListByQuery(@RequestBody User user);

    /**
     * 根据用户名称模糊查询用户信息
     *
     * @param name 用户名
     * @return List<User> 用户信息
     * <AUTHOR>
     * @date 2022/5/23 17:26
     * @update zxy 2022/5/23 17:26
     * @since 1.0
     */
    @GetMapping("/echo/queryLikeName")
    List<User> findUserListLikeName(@RequestParam("name") String name);

    /**
     * 根据机构id集合查询用户信息
     *
     * @param orgIds 机构id集合
     * @return List<User> 用户信息
     * <AUTHOR>
     * @date 2022/5/23 17:27
     * @update zxy 2022/5/23 17:27
     * @since 1.0
     */
    @GetMapping("/findByOrgIds")
    List<User> findByOrgIds(@RequestParam("orgIds") List<Long> orgIds);
}
