package com.jettech.jettong.base.api;

import com.jettech.basic.database.mybatis.conditions.query.LbqWrapper;
import com.jettech.basic.model.LoadService;
import com.jettech.jettong.base.api.hystrix.SysTeamApiFallback;
import com.jettech.jettong.base.entity.rbac.team.SysTeam;
import com.jettech.jettong.base.entity.rbac.team.SysTeamUser;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 团队信息相关API
 *
 * <AUTHOR>
 * @version 1.0
 * @description 团队信息相关API
 * @projectName jettong-enterprises
 * @package com.jettech.jettong.base.api
 * @className SysTeamApi
 * @date 2021/10/22 10:42
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@FeignClient(name = "${jettong.feign.base-server:jettong-base-server}", fallback = SysTeamApiFallback.class
        , path = "/base/sysTeam")
public interface SysTeamApi extends LoadService
{
    /**
     * 根据id查询待回显参数
     *
     * @param ids 唯一键（可能不是主键ID)
     * @return Map<Serializable, Object> 待回显参数
     * <AUTHOR>
     * @date 2021/9/13 10:11
     * @update zxy 2021/9/13 10:11
     * @since 1.0
     */
    @Override
    @GetMapping("/findByIds")
    Map<Serializable, Object> findByIds(@RequestParam(value = "ids") Set<Serializable> ids);



    @PostMapping("/echo/insertSysTeamUser")
    void insertSysTeamUser(@RequestBody List<SysTeamUser> teamUserList);

    @PostMapping("/echo/listSysTeamUser")
    List<SysTeamUser> listSysTeamUser(@RequestBody LbqWrapper<SysTeamUser> wrapper);

    @PostMapping("/echo/saveSysTeam")
    SysTeam saveSysTeam(@RequestBody SysTeam sysTeam);


    @PostMapping("/echo/listAll")
    List<SysTeam> listAll();
}
