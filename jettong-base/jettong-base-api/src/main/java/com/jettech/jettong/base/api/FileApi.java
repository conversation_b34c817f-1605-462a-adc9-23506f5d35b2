package com.jettech.jettong.base.api;

import com.jettech.basic.model.LoadService;
import com.jettech.jettong.base.api.hystrix.FileApiFallback;
import com.jettech.jettong.base.entity.file.File;
import com.jettech.jettong.base.enumeration.file.FileBizType;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 附件管理相关API
 *
 * <AUTHOR>
 * @version 1.0
 * @description 附件管理相关API
 * @projectName jettong-enterprises
 * @package com.jettech.jettong.base.api
 * @className FileApi
 * @date 2021/12/7 16:17
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@FeignClient(name = "${jettong.feign.base-server:jettong-base-server}", fallback = FileApiFallback.class
        , path = "/base/file")
public interface FileApi extends LoadService
{

    /**
     * 根据id查询待回显参数
     *
     * @param ids 唯一键（可能不是主键ID)
     * @return Map<Serializable, Object> 待回显参数
     * <AUTHOR>
     * @date 2021/9/13 10:11
     * @update zxy 2021/9/13 10:11
     * @since 1.0
     */
    @Override
    @GetMapping("/echo/findByIds")
    Map<Serializable, Object> findByIds(@RequestParam(value = "ids") Set<Serializable> ids);

    /**
     * 根据ID获取文件详情
     *
     * @param ids 文件ID
     * @return 文件详情列表
     */
    @GetMapping("/echo/findFileByIds")
    List<File> findFileByIds(@RequestParam("ids") List<Long> ids);

    /**
     * 根据业务类型和业务id删除附件
     *
     * @param bizType 业务类型
     * @param bizIds 业务id集合
     * @return boolean 删除结果
     * <AUTHOR>
     * @date 2021/12/7 16:45
     * @update zxy 2021/12/7 16:45
     * @since 1.0
     */
    @DeleteMapping("/echo/{bizType}")
    boolean removeByFileBizTypeAndBizIds(@PathVariable("bizType") FileBizType bizType, @RequestBody
            Collection<? extends Serializable> bizIds);

    /**
     * 根据文件id删除附件
     *
     * @param ids 文件id集合
     * @return boolean 删除结果
     * <AUTHOR>
     * @date 2021/12/7 16:45
     * @update zxy 2021/12/7 16:45
     * @since 1.0
     */
    @DeleteMapping("/echo")
    boolean removeBIds(@RequestBody Collection<? extends Serializable> ids);

    /**
     * 批量修改附件
     *
     * @param files 附件信息集合
     * @return boolean 修改结果
     * <AUTHOR>
     * @date 2021/12/7 16:45
     * @update zxy 2021/12/7 16:45
     * @since 1.0
     */
    @PutMapping("/echo/batch")
    boolean updateBatchById(@RequestBody List<File> files);

    /**
     * 批量保存文件
     *
     * @param files 文件信息
     * @return boolean 保存结果
     * <AUTHOR>
     * @date 2022/7/12 14:09
     * @update 2022/7/12 14:09
     * @since 1.0
     */
    @PutMapping("/echo/saveBatch")
    boolean saveBatch(@RequestBody List<File> files);

    /**
     * 根据业务类型和业务id查询附件
     *
     * @param bizType 业务类型
     * @param bizId 业务id
     * @return boolean 删除结果
     * <AUTHOR>
     * @date 2021/12/7 16:45
     * @update zxy 2021/12/7 16:45
     * @since 1.0
     */
    @GetMapping("/echo/{bizType}/{bizId}")
    List<File> findByBizTypeAndBizId(@PathVariable("bizType") FileBizType bizType,
            @PathVariable("bizId") Serializable bizId);
}
