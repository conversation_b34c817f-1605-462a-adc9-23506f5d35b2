package com.jettech.jettong.base.api;


import com.jettech.basic.model.LoadService;
import com.jettech.jettong.base.api.hystrix.DictionaryApiFallback;
import com.jettech.jettong.base.entity.sys.dictionary.Dictionary;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 数据字典相关API
 *
 * <AUTHOR>
 * @version 1.0
 * @description 数据字典相关API
 * @projectName jettong-enterprises
 * @package com.jettech.jettong.base.api
 * @className DictionaryApi
 * @date 2021/10/22 10:42
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@FeignClient(name = "${jettong.feign.base-server:jettong-base-server}", fallback = DictionaryApiFallback.class
        , path = "/base/dictionary")
public interface DictionaryApi extends LoadService
{
    /**
     * 根据id查询待回显参数
     *
     * @param ids 唯一键（可能不是主键ID)
     * @return Map<Serializable, Object> 待回显参数
     * <AUTHOR>
     * @date 2021/9/13 10:11
     * @update zxy 2021/9/13 10:11
     * @since 1.0
     */
    @Override
    @GetMapping("/findByIds")
    Map<Serializable, Object> findByIds(@RequestParam(value = "ids") Set<Serializable> ids);

    /**
     * 根据父级字典类型和父级字典code查询字典信息
     *
     * @param dictionaryType 父级字典类型key
     * @param parentKey 父级字典key
     * @return List<Dictionary> 字典信息
     * <AUTHOR>
     * @date 2021/11/1 16:23
     * @update zxy 2021/11/1 16:23
     * @since 1.0
     */
    @GetMapping("/echo/findByParentKey/{dictionaryType}/{parentKey}")
    List<Dictionary> findByParentKey(@PathVariable("dictionaryType") String dictionaryType,
            @PathVariable("parentKey") String parentKey);

    /**
     * 根据条件查询字点列表
     *
     * @param dictionary type 字典类型，如果参数中包含parentId和parentCoded时，传父级字典的类型且该字段必填
     * code 编码，模糊查询
     * name 字典名称，模糊查询
     * state 状态
     * parentId 父级字典id
     * parentCode 父级字典Code
     * @return List<Dictionary> 字典信息
     * <AUTHOR>
     * @date 2021/11/30 19:23
     * @update lyy 2021/11/30 19:23
     * @since 1.0
     */
    @PostMapping("/echo/query")
    List<Dictionary> query(@RequestBody Dictionary dictionary);


    /**
     * 根据id查询字典信息
     *
     * @param id 字典信息id
     * @return Dictionary 字典信息
     * <AUTHOR>
     * @date 2025/7/24 21:25
     * @update lxr 2025/7/24 21:25
     * @since 1.0
     */
    @GetMapping("/echo/{id}")
    Dictionary findDictionaryById(@PathVariable("id") Long id);

}
