package com.jettech.jettong.base.api.hystrix;

import com.jettech.basic.base.R;
import com.jettech.basic.log.entity.OptLogDTO;
import com.jettech.jettong.base.api.OptLogApi;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 操作日志API熔断
 *
 * <AUTHOR>
 * @version 1.0
 * @description 操作日志API熔断
 * @projectName jettong
 * @package com.jettech.jettong.base.api.hystrix
 * @className OptLogApiFallback
 * @date 2021/9/15 9:43
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Component
public class OptLogApiFallback implements OptLogApi
{

    @Override
    public R<OptLogDTO> save(@RequestBody OptLogDTO log)
    {
        return R.timeout();
    }

}
