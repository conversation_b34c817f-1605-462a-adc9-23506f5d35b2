package com.jettech.jettong.base.api.hystrix;

import com.jettech.jettong.base.api.DictionaryApi;
import com.jettech.jettong.base.entity.sys.dictionary.Dictionary;
import org.springframework.stereotype.Component;

import java.io.Serializable;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 数据字典相关API熔断
 *
 * <AUTHOR>
 * @version 1.0
 * @description 数据字典相关API熔断
 * @projectName jettong-enterprises
 * @package com.jettech.jettong.base.api.hystrix
 * @className DictionaryApiFallback
 * @date 2021/10/22 10:46
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Component
public class DictionaryApiFallback implements DictionaryApi
{
    @Override
    public Map<Serializable, Object> findByIds(Set<Serializable> ids)
    {
        return Collections.emptyMap();
    }

    @Override
    public List<Dictionary> findByParentKey(String dictionaryType, String parentKey)
    {
        return Collections.emptyList();
    }

    @Override
    public List<Dictionary> query(Dictionary dictionary)
    {
        return Collections.emptyList();
    }

    @Override
    public Dictionary findDictionaryById(Long id)
    {
        return null;
    }
}
