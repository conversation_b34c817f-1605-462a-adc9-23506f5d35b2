package com.jettech.jettong.base.api;

import com.jettech.basic.model.LoadService;
import com.jettech.jettong.base.api.hystrix.TeamApiFallback;
import com.jettech.jettong.base.entity.rbac.team.SysTeam;
import com.jettech.jettong.base.entity.rbac.user.User;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 团队相关API
 *
 * <AUTHOR>
 * @version 1.0
 * @description 团队相关API
 * @projectName jettong-enterprises
 * @package com.jettech.jettong.base.api
 * @className TeamApi
 * @date 2023/03/16 10:43
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@FeignClient(name = "${jettong.feign.base-server:jettong-base-server}", fallback = TeamApiFallback.class
        , path = "/base/team")
public interface TeamApi extends LoadService
{

    /**
     * 根据id查询待回显参数
     *
     * @param ids 唯一键（可能不是主键ID)
     * @return Map<Serializable, Object> 待回显参数
     * <AUTHOR>
     * @date 2021/9/13 10:11
     * @update zxy 2021/9/13 10:11
     * @since 1.0
     */
    @Override
    @GetMapping("/findByIds")
    Map<Serializable, Object> findByIds(@RequestParam(value = "ids") Set<Serializable> ids);

    /**
     * 查询团队下全部成员
     *
     * @param teamIds 团队Id
     * @return Map<Serializable, Object> 待回显参数
     * <AUTHOR>
     * @date 2023/03/17 10:11
     * @update sxs 2023/03/17 10:11
     * @since 1.0
     */
    @PostMapping("/getTeamUsers")
    List<Map<Long, List<User>>> getTeamUsers(@RequestBody List<Long> teamIds);

    /**
     * 根据用户id获取团队
     *
     * @param userIds   用户id
     * @return      团队列表
     */
    @GetMapping("/getSysTeamByUserIds")
    Map<Long, List<SysTeam>> getSysTeamById(@RequestParam("userIds") List<Long> userIds);

    /**
     * 根据用户id获取团队
     *
     * @param userId    用户id
     * @return        团队列表
     */
    @GetMapping("/getSysTeamByUserId")
    List<SysTeam> getSysTeamById(@RequestParam("userId") Long userId);

}
