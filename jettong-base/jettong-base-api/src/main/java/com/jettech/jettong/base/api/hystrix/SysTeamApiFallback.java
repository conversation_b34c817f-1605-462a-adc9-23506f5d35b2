package com.jettech.jettong.base.api.hystrix;

import com.jettech.basic.database.mybatis.conditions.query.LbqWrapper;
import com.jettech.jettong.base.api.SysTeamApi;
import com.jettech.jettong.base.entity.rbac.team.SysTeam;
import com.jettech.jettong.base.entity.rbac.team.SysTeamUser;
import org.springframework.stereotype.Component;

import java.io.Serializable;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 数据字典相关API熔断
 *
 * <AUTHOR>
 * @version 1.0
 * @description 数据字典相关API熔断
 * @projectName jettong-enterprises
 * @package com.jettech.jettong.base.api.hystrix
 * @className DictionaryApiFallback
 * @date 2021/10/22 10:46
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Component
public class SysTeamApiFallback implements SysTeamApi
{
    @Override
    public Map<Serializable, Object> findByIds(Set<Serializable> ids)
    {
        return Collections.emptyMap();
    }


    @Override
    public void insertSysTeamUser(List<SysTeamUser> teamUserList)
    {
        return;
    }

    @Override
    public List<SysTeamUser> listSysTeamUser(LbqWrapper<SysTeamUser> wrapper)
    {
        return Collections.emptyList();
    }

    @Override
    public SysTeam saveSysTeam(SysTeam sysTeam)
    {
        return null;
    }

    @Override
    public List<SysTeam> listAll()
    {
        return  Collections.emptyList();
    }
}
