package com.jettech.jettong.base.api;

import com.jettech.basic.model.LoadService;
import com.jettech.jettong.base.api.hystrix.OrgApiFallback;
import com.jettech.jettong.base.entity.rbac.org.Org;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 组织机构相关API
 *
 * <AUTHOR>
 * @version 1.0
 * @description 组织机构相关API
 * @projectName jettong-enterprises
 * @package com.jettech.jettong.base.api
 * @className OrgApi
 * @date 2021/10/22 10:43
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@FeignClient(name = "${jettong.feign.base-server:jettong-base-server}", fallback = OrgApiFallback.class
        , path = "/base/org")
public interface OrgApi extends LoadService
{
    /**
     * 根据id查询待回显参数
     *
     * @param ids 唯一键（可能不是主键ID)
     * @return Map<Serializable, Object> 待回显参数
     * <AUTHOR>
     * @date 2021/9/13 10:11
     * @update zxy 2021/9/13 10:11
     * @since 1.0
     */
    @Override
    @GetMapping("/findByIds")
    Map<Serializable, Object> findByIds(@RequestParam(value = "ids") Set<Serializable> ids);

    /**
     * 根据条件查询机构信息
     *
     * @param org 查询条件
     * @return List<Org> 机构信息
     * <AUTHOR>
     * @date 2021/9/13 10:11
     * @update zxy 2021/9/13 10:11
     * @since 1.0
     */
    @PostMapping("/echo/query")
    List<Org> findOrgListByQuery(@RequestBody Org org);

    /**
     * 根据机构id查询机构信息
     *
     * @param id 机构id
     * @return Org 机构信息
     * <AUTHOR>
     * @date 2022/5/23 17:24
     * @update zxy 2022/5/23 17:24
     * @since 1.0
     */
    @PostMapping("/echo/query/findOrgByOrgId")
    Org findOrgByOrgId(@RequestParam(value = "id") Long id);

    /**
     * 根据机构id查询所有父级机构信息
     *
     * @param orgId 机构id
     * @return List<Org> 父级机构信息
     * <AUTHOR>
     * @date 2022/7/12 14:11
     * @update 2022/7/12 14:11
     * @since 1.0
     */
    @GetMapping("/echo/{orgId}/parent")
    List<Org> getParentOrgList(@PathVariable("orgId") Long orgId);

    /**
     * 根据机构id查询所有子级机构信息
     *
     * @param orgId 机构id
     * @return List<Org> 子级机构信息
     * <AUTHOR>
     * @date 2022/7/12 14:12
     * @update 2022/7/12 14:12
     * @since 1.0
     */
    @GetMapping("/echo/{orgId}/child")
    List<Org> findChildrenOrgList(@PathVariable("orgId") Long orgId);

}
