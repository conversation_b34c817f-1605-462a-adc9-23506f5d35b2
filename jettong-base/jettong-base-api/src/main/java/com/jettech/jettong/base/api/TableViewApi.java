package com.jettech.jettong.base.api;

import com.jettech.basic.model.LoadService;
import com.jettech.jettong.base.api.hystrix.TableViewApiFallback;
import com.jettech.jettong.base.dto.sys.personalized.TableViewSaveDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.io.Serializable;
import java.util.Map;
import java.util.Set;

/**
 * 筛选器API
 *
 * <AUTHOR>
 * @version 1.0
 * @projectname jettong-base-cloud
 * 2023/3/30 11:00
 */
@FeignClient(name = "${jettong.feign.base-server:jettong-base-server}", fallback = TableViewApiFallback.class
        , path = "/base/echo/tableView")
public interface TableViewApi extends LoadService {

    @Override
    @GetMapping("/findByIds")
    Map<Serializable, Object> findByIds(@RequestParam(value = "ids") Set<Serializable> ids);


    /**
     * 最后一次使用筛选记录
     *
     * @param saveDTO
     * @return
     */
    @PostMapping("/saveLast")
    public Boolean saveLastSearch(@RequestBody TableViewSaveDTO saveDTO);

}
