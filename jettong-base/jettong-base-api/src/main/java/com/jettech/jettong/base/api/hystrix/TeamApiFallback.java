package com.jettech.jettong.base.api.hystrix;

import com.jettech.jettong.base.api.TeamApi;
import com.jettech.jettong.base.entity.rbac.team.SysTeam;
import com.jettech.jettong.base.entity.rbac.user.User;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestBody;

import java.io.Serializable;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 团队相关API熔断
 *
 * <AUTHOR>
 * @version 1.0
 * @description 团队相关API熔断
 * @projectName jettong-enterprises
 * @package com.jettech.jettong.base.api.hystrix
 * @className TeamApiFallback
 * @date 2023/03/16 10:50
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Component
public class TeamApiFallback implements TeamApi
{
    @Override
    public Map<Serializable, Object> findByIds(Set<Serializable> ids)
    {
        return Collections.emptyMap();
    }

    @Override
    public List<Map<Long, List<User>>> getTeamUsers(@RequestBody List<Long> teamIds)
    {

        return Collections.emptyList();
    }

    @Override
    public Map<Long, List<SysTeam>> getSysTeamById(List<Long> userIds) {
        return new HashMap<>();
    }

    @Override
    public List<SysTeam> getSysTeamById(Long userId) {
        return null;
    }
}
