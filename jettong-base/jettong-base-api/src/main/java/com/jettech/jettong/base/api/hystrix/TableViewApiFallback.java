package com.jettech.jettong.base.api.hystrix;

import com.jettech.jettong.base.api.TableViewApi;
import com.jettech.jettong.base.dto.sys.personalized.TableViewSaveDTO;
import org.springframework.stereotype.Component;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @version 1.0
 * @projectname jettong-base-cloud
 * 2023/3/30 11:01
 */
@Component
public class TableViewApiFallback implements TableViewApi {
    @Override
    public Map<Serializable, Object> findByIds(Set<Serializable> ids) {
        return new HashMap<>();
    }

    @Override
    public Boolean saveLastSearch(TableViewSaveDTO saveDTO) {
        return false;
    }
}
