package com.jettech.jettong.base.api.hystrix;

import com.jettech.jettong.base.api.FileApi;
import com.jettech.jettong.base.entity.file.File;
import com.jettech.jettong.base.enumeration.file.FileBizType;
import org.springframework.stereotype.Component;

import java.io.Serializable;
import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @description TODO
 * @projectName jettong-enterprises
 * @package com.jettech.jettong.base.api.hystrix
 * @className FileApiFallback
 * @date 2021/12/7 16:46
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Component
public class FileApiFallback implements FileApi
{
    @Override
    public Map<Serializable, Object> findByIds(Set<Serializable> ids)
    {
        return Collections.emptyMap();
    }

    @Override
    public List<File> findFileByIds(List<Long> ids)
    {
        return new ArrayList<>();
    }

    @Override
    public boolean removeByFileBizTypeAndBizIds(FileBizType fileBizType, Collection<? extends Serializable> bizIds)
    {
        return false;
    }

    @Override
    public boolean removeBIds(Collection<? extends Serializable> ids)
    {
        return false;
    }

    @Override
    public boolean updateBatchById(List<File> files)
    {
        return false;
    }

    @Override
    public boolean saveBatch(List<File> files)
    {
        return false;
    }

    @Override
    public List<File> findByBizTypeAndBizId(FileBizType bizType, Serializable bizId)
    {
        return Collections.emptyList();
    }
}
