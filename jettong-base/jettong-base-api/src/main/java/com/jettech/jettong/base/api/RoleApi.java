package com.jettech.jettong.base.api;

import com.jettech.jettong.base.api.hystrix.RoleApiFallback;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * 角色相关API
 *
 * <AUTHOR>
 * @version 1.0
 * @description 角色相关API
 * @projectName jettong-enterprises
 * @package com.jettech.jettong.base.api
 * @className RoleApi
 * @date 2021/10/22 10:43
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@FeignClient(name = "${jettong.feign.base-server:jettong-base-server}", fallback = RoleApiFallback.class
        , path = "/base/dictionary")
public interface RoleApi
{
}
