package com.jettech.jettong.base.api;

import com.jettech.basic.base.request.PageParams;
import com.jettech.jettong.base.api.hystrix.PersonalizedTableViewApiFallback;
import com.jettech.jettong.base.entity.sys.personalized.PersonalizedTableView;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 筛选器API
 *
 * <AUTHOR>
 * @version 1.0
 * @description 筛选器API
 * @projectName jettong-base-cloud
 * @package com.jettech.jettong.base.api
 * @className PersonalizedTableViewApiFallback
 * @date 2023/5/30 11:36
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@FeignClient(name = "${jettong.feign.base-server:jettong-base-server}", fallback = PersonalizedTableViewApiFallback.class
        , path = "/base/echo/personalizedTableView")
public interface PersonalizedTableViewApi {

    /**
     * 保存最后一次使用筛选记录，并作为默认视图
     */
    @PostMapping("/saveLast")
    public Boolean saveLastSearch(@RequestBody PersonalizedTableView saveDTO);

    /**
     * 保存最后一次使用筛选记录，并作为默认视图
     */
    default public Boolean saveLastSearch(Long userId, String tableCode, PageParams<?> pageParams) {
        PersonalizedTableView lastView = PersonalizedTableView.lastView(userId, tableCode, pageParams);
        if (lastView == null) {
            return false;
        }
        return this.saveLastSearch(lastView);
    }
}
