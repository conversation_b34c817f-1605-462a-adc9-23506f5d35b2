package com.jettech.jettong.base.api;

import com.jettech.basic.base.R;
import com.jettech.basic.log.entity.OptLogDTO;
import com.jettech.jettong.base.api.hystrix.OptLogApiFallback;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;


/**
 * 操作日志保存 API
 *
 * <AUTHOR>
 * @version 1.0
 * @description 操作日志保存 API
 * @projectName jettong-enterprises
 * @package com.jettech.jettong.base.api
 * @className OptLogApi
 * @date 2021/10/21 20:36
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@FeignClient(name = "${jettong.feign.base-server:jettong-base-server}", fallback = OptLogApiFallback.class
        , path = "/base/optLog")
public interface OptLogApi
{
    /**
     * 保存操作日志
     *
     * @param log 操作日志
     * @return R<OptLogDTO> 保存结果
     * <AUTHOR>
     * @date 2021/11/4 22:00
     * @update zxy 2021/11/4 22:00
     * @since 1.0
     */
    @PostMapping("/echo")
    R<OptLogDTO> save(@RequestBody OptLogDTO log);
}
