package com.jettech.jettong.base.api.hystrix;

import com.jettech.jettong.base.api.OrgApi;
import com.jettech.jettong.base.entity.rbac.org.Org;
import org.springframework.stereotype.Component;

import java.io.Serializable;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 组织机构相关API熔断
 *
 * <AUTHOR>
 * @version 1.0
 * @description 组织机构相关API熔断
 * @projectName jettong-enterprises
 * @package com.jettech.jettong.base.api.hystrix
 * @className OrgApiFallback
 * @date 2021/10/22 10:52
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Component
public class OrgApiFallback implements OrgApi
{
    @Override
    public Map<Serializable, Object> findByIds(Set<Serializable> ids)
    {
        return Collections.emptyMap();
    }

    @Override
    public List<Org> findOrgListByQuery(Org query)
    {
        return Collections.emptyList();
    }

    @Override
    public Org findOrgByOrgId(Long id) {
        return null;
    }

    @Override
    public List<Org> getParentOrgList(Long orgId) {
        return Collections.emptyList();
    }

    @Override
    public List<Org> findChildrenOrgList(Long orgId) {
        return Collections.emptyList();
    }
}
