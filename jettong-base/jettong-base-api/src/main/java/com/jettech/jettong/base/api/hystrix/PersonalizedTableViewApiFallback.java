package com.jettech.jettong.base.api.hystrix;

import com.jettech.jettong.base.api.PersonalizedTableViewApi;
import com.jettech.jettong.base.entity.sys.personalized.PersonalizedTableView;
import org.springframework.stereotype.Component;

/**
 * 筛选器API
 *
 * <AUTHOR>
 * @version 1.0
 * @description 筛选器API
 * @projectName jettong-base-cloud
 * @package com.jettech.jettong.base.api
 * @className PersonalizedTableViewApiFallback
 * @date 2023/5/30 11:36
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Component
public class PersonalizedTableViewApiFallback implements PersonalizedTableViewApi {
    @Override
    public Boolean saveLastSearch(PersonalizedTableView saveDTO) {
        return false;
    }
}
