package com.jettech.jettong.oauth.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;

/**
 * 上传文件到系统中工具类
 * <AUTHOR>
 *
 */
@Slf4j
public final class UploadFileToLocalUtil
{
    private UploadFileToLocalUtil()
    {
        
    }
    
    /**
     * 单个文件上传
     * @param localPath 上传文件路径
     * @param file 文件
     * @param localName 文件名称
     * @return boolean 上传结果
     * <AUTHOR>
     * @date 2021/12/22 14:36
     * @update zxy 2021/12/22 14:36
     * @since 1.0
     */
    public static boolean uploadFile(String localPath, MultipartFile file, String localName)
    {
        localPath = localPath.replace("\\", "/");
        File targetPath = new File(localPath);
        
        // 判断目录是否存在
        if (!targetPath.exists())
        {
            // 创建目录
            try
            {
                Files.createDirectories(targetPath.toPath());
            }
            catch (IOException e)
            {
                log.error("创建文件目录失败,原因:{}", e.getMessage(), e);
            }
        }
        File targetFile = new File(localPath, localName);
        try
        {
            //把文件上传到平台服务器
            file.transferTo(targetFile);
        }
        catch (IOException e)
        {
            log.error("文件" + file.getOriginalFilename() + "上传到临时目录失败，原因：{}", e.getMessage(), e);
            return false;
        }
        return true;
        
    }
}
