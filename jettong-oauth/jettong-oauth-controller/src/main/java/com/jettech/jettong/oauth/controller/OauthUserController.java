package com.jettech.jettong.oauth.controller;

import com.jettech.basic.base.R;
import com.jettech.basic.security.feign.UserQuery;
import com.jettech.basic.security.model.SysUser;
import com.jettech.jettong.oauth.service.UserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

/**
 * <p>
 * 前端控制器
 * 用户
 * </p>
 *
 * <AUTHOR>
 * @date 2019-07-22
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/oauth/user")
@Api(value = "User", tags = "用户")
@AllArgsConstructor
@ApiIgnore
public class OauthUserController
{
    private final UserService userService;

    /**
     * 单体查询用户
     *
     * @param id 主键id
     * @return 查询结果
     */
    @ApiOperation(value = "查询用户详细", notes = "查询用户详细")
    @PostMapping(value = "/anno/id/{id}")
    public R<SysUser> getById(@PathVariable Long id, @RequestBody UserQuery query)
    {
        return R.success(userService.getSysUserById(id, query));
    }

}
