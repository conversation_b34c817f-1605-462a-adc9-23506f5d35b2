package com.jettech.jettong.oauth.controller;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.jettech.basic.annotation.user.LoginUser;
import com.jettech.basic.base.R;
import com.jettech.basic.dozer.DozerUtils;
import com.jettech.basic.security.model.SysRole;
import com.jettech.basic.security.model.SysUser;
import com.jettech.basic.security.properties.SecurityProperties;
import com.jettech.basic.utils.CollHelper;
import com.jettech.basic.utils.StrPool;
import com.jettech.basic.utils.TreeUtil;
import com.jettech.jettong.base.dto.rbac.role.AuthorityFunctionDTO;
import com.jettech.jettong.base.dto.rbac.role.VueRouter;
import com.jettech.jettong.base.entity.rbac.role.Menu;
import com.jettech.jettong.base.entity.rbac.role.Role;
import com.jettech.jettong.oauth.service.MenuService;
import com.jettech.jettong.oauth.service.RoleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;
import java.util.stream.Collectors;

import static com.jettech.basic.utils.StrPool.DEF_COMPONENT;

/**
 * 前端控制器资源 角色 菜单
 *
 * <AUTHOR>
 * @version 1.0
 * @description 前端控制器资源 角色 菜单
 * @projectName jettong
 * @package com.jettech.jettong.oauth.controller
 * @className OauthMenuFunctionController
 * @date 2021/10/20 15:24
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Slf4j
@RestController
@RequestMapping("/oauth")
@AllArgsConstructor
@Api(value = "OauthMenuFunction", tags = "菜单功能")
public class OauthMenuFunctionController
{
    private final DozerUtils dozer;
    private final MenuService menuService;
    private final RoleService roleService;
    private final SecurityProperties securityProperties;

    @ApiOperation(value = "查询用户可用的所有功能", notes = "查询用户可用的所有功能")
    @GetMapping("/resource/visible")
    public R<AuthorityFunctionDTO> visible(@ApiIgnore @LoginUser(isRoles = true) SysUser sysUser)
    {
        Long userId = sysUser.getId();

        List<Long> roleIds = sysUser.getRoles().stream().map(
                SysRole::getId).collect(Collectors.toList());

        if (roleIds.isEmpty())
        {
            return R.fail("获取用户可用的所有功能失败，原因：用户没有配置角色信息，请联系管理员");
        }
        List<Menu> functionList = menuService.findVisibleFunction(userId, roleIds);
        List<Role> roleList = roleService.findRoleByUserId(userId);
        return R.success(AuthorityFunctionDTO.builder()
                .roleList(roleList.parallelStream().filter(ObjectUtil::isNotEmpty).map(Role::getCode).distinct()
                        .collect(Collectors.toList()))
                .functionList(CollHelper.split(functionList, Menu::getCode, StrPool.SEMICOLON))
                .caseSensitive(securityProperties.getCaseSensitive())
                .enabled(securityProperties.getEnabled())
                .build());
    }

    @ApiOperation(value = "查询用户可用的所有菜单", notes = "查询用户可用的所有菜单")
    @GetMapping("/menu/menus")
    public R<List<Menu>> myMenus(@ApiIgnore @LoginUser(isRoles = true) SysUser sysUser)
    {
        Long userId = sysUser.getId();

        List<Long> roleIds = sysUser.getRoles().stream().map(
                SysRole::getId).collect(Collectors.toList());

        if (roleIds.isEmpty())
        {
            return R.fail("获取用户可用的所有菜单失败，原因：用户没有配置角色信息，请联系管理员");
        }

        List<Menu> list = menuService.findVisibleMenu(userId, roleIds,null);
        log.info("list={}", list.size());
        List<Menu> tree = TreeUtil.buildTree(list);
        return R.success(tree);
    }

    @ApiOperation(value = "查询用户可用的所有菜单路由树", notes = "查询用户可用的所有菜单路由树")
    @GetMapping("/menu/router")
    public R<List<VueRouter>> myRouter(@ApiIgnore @LoginUser(isRoles = true) SysUser sysUser,@RequestParam(name="platform",required = false) String platform)
    {
        Long userId = sysUser.getId();

        List<Long> roleIds = sysUser.getRoles().stream().map(
                SysRole::getId).collect(Collectors.toList());

        if (roleIds.isEmpty())
        {
            return R.fail("获取用户可用的所有菜单路由树失败，原因：用户没有配置角色信息，请联系管理员");
        }
        List<Menu> list = menuService.findVisibleMenu(userId, roleIds,platform);
        for (Menu menu : list)
        {
            if (StrUtil.isEmpty(menu.getComponent()))
            {
                menu.setComponent(DEF_COMPONENT);
            }
        }
        List<VueRouter> treeList = dozer.mapList(list, VueRouter.class);
        return R.success(TreeUtil.buildTree(treeList));
    }

}
