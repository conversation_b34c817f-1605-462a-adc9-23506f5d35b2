<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>jettong-oauth</artifactId>
        <groupId>com.jettech.jettong</groupId>
        <version>develop</version>
        <relativePath>../pom.xml</relativePath>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>jettong-oauth-controller</artifactId>
    <name>${project.artifactId}</name>
    <description>权限服务接口模块</description>
    <dependencies>
        <dependency>
            <groupId>com.jettech.jettong</groupId>
            <artifactId>jettong-oauth-biz</artifactId>
            <version>${jettong-project.version}</version>
        </dependency>
        <!-- jwt 只有Oauth服务需要使用 -->
        <dependency>
            <groupId>com.jettech.basic</groupId>
            <artifactId>jettong-jwt-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jettech.basic</groupId>
            <artifactId>jettong-dozer-starter</artifactId>
        </dependency>
    </dependencies>


</project>
