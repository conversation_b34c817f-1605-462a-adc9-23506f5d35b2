package com.jettech.jettong.oauth.config;

import com.jettech.basic.boot.config.BaseConfig;
import com.jettech.basic.log.event.SysLogListener;
import com.jettech.jettong.base.api.OptLogApi;
import com.jettech.jettong.common.properties.SystemProperties;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.ScopedProxyMode;

/**
 * <AUTHOR>
 * @date 2017-12-15 14:42
 */
@Configuration
@EnableConfigurationProperties(SystemProperties.class)
@RefreshScope(proxyMode = ScopedProxyMode.DEFAULT)
public class OauthWebConfiguration extends BaseConfig
{


    @Value("${jettong.log.query:true}")
    private Boolean query;

    /**
     * jettong.log.enabled = true 并且 jettong.log.type=DB时实例该类
     */
    @Bean
    @ConditionalOnExpression("${jettong.log.enabled:true} && 'DB'.equals('${jettong.log.type:LOGGER}')")
    public SysLogListener sysLogListener(OptLogApi optLogApi)
    {
        return new SysLogListener(optLogApi::save, query);
    }
}
