<?xml version="1.0" encoding="UTF-8"?>
<mappings xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
          xmlns="http://dozermapper.github.io/schema/bean-mapping"
          xsi:schemaLocation="http://dozermapper.github.io/schema/bean-mapping http://dozermapper.github.io/schema/bean-mapping.xsd">

    <mapping date-format="yyyy-MM-dd HH:mm:ss">
        <class-a>com.jettech.jettong.base.entity.rbac.role.Menu</class-a>
        <class-b>com.jettech.jettong.base.dto.rbac.role.VueRouter</class-b>
        <field>
            <a>id</a>
            <b>id</b>
        </field>
        <field>
            <a>parentId</a>
            <b>parentId</b>
        </field>
        <field>
            <a>code</a>
            <b>name</b>
        </field>
        <field>
            <a>id</a>
            <b>meta.menuId</b>
        </field>
        <field>
            <a>parentId</a>
            <b>meta.parentMenuId</b>
        </field>
        <field>
            <a>name</a>
            <b>meta.title</b>
        </field>
        <field>
            <a>code</a>
            <b>meta.code</b>
        </field>
        <field>
            <a>icon</a>
            <b>meta.icon</b>
        </field>
        <field>
            <a>isHide</a>
            <b>meta.hideMenu</b>
        </field>
        <field>
            <a>showChildren</a>
            <b>meta.showChildren</b>
        </field>
        <field>
            <a>isExternal</a>
            <b>meta.isExternal</b>
        </field>
        <field>
            <a>externalUrl</a>
            <b>meta.externalUrl</b>
        </field>
    </mapping>

</mappings>
