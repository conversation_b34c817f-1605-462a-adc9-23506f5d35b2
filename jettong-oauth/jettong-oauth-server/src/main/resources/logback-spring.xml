<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <!-- 服务器运行时，在bootstrap.yml中通过 logging.config=classpath:logback-spring.xml文件，
    表示服务器运行时，日志异步打印出来，增加服务器的效率 -->
    <include resource="logback/defaults-async.xml"/>

    <logger name="com.jettech.jettong.base.controller" additivity="true" level="${log.level.controller}">
        <appender-ref ref="ASYNC_CONTROLLER_APPENDER"/>
    </logger>
    <logger name="com.jettech.jettong.base.service" additivity="true" level="${log.level.service}">
        <appender-ref ref="ASYNC_SERVICE_APPENDER"/>
    </logger>
    <logger name="com.jettech.jettong.base.dao" additivity="false" level="${log.level.dao}">
        <appender-ref ref="ASYNC_DAO_APPENDER"/>
    </logger>
    <logger name="com.jettech.jettong.oauth.controller" additivity="true" level="${log.level.controller}">
        <appender-ref ref="ASYNC_CONTROLLER_APPENDER"/>
    </logger>
    <logger name="com.jettech.jettong.oauth.service" additivity="true" level="${log.level.service}">
        <appender-ref ref="ASYNC_SERVICE_APPENDER"/>
    </logger>
    <logger name="com.jettech.jettong.oauth.dao" additivity="false" level="${log.level.dao}">
        <appender-ref ref="ASYNC_DAO_APPENDER"/>
    </logger>
</configuration>
