<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>jettong-oauth</artifactId>
        <groupId>com.jettech.jettong</groupId>
        <version>develop</version>
        <relativePath>../pom.xml</relativePath>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>jettong-oauth-biz</artifactId>
    <name>${project.artifactId}</name>
    <description>认证服务业务模块</description>
    <dependencies>
        <dependency>
            <groupId>com.jettech.jettong</groupId>
            <artifactId>jettong-base-entity</artifactId>
            <version>${jettong-project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.jettech.jettong</groupId>
            <artifactId>jettong-common</artifactId>
            <version>${jettong-project.version}</version>
        </dependency>

        <dependency>
            <groupId>com.jettech.basic</groupId>
            <artifactId>jettong-databases</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-webmvc</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.jettech.basic</groupId>
            <artifactId>jettong-boot-base</artifactId>
        </dependency>

        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus</artifactId>
        </dependency>

        <dependency>
            <groupId>com.jettech.basic</groupId>
            <artifactId>jettong-security-starter</artifactId>
        </dependency>


        <!-- jwt 只有认证服务需要使用 -->
        <dependency>
            <groupId>com.jettech.basic</groupId>
            <artifactId>jettong-jwt-starter</artifactId>
        </dependency>
        <!-- log 只有认证服务biz模块引用，其他服务都在controller模块 -->
        <dependency>
            <groupId>com.jettech.basic</groupId>
            <artifactId>jettong-log-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>com.github.whvcse</groupId>
            <artifactId>easy-captcha</artifactId>
        </dependency>

        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
        </dependency>

        <dependency>
            <groupId>eu.bitwalker</groupId>
            <artifactId>UserAgentUtils</artifactId>
        </dependency>

        <!-- @RefreshScope 需要使用 -->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-context</artifactId>
        </dependency>

    </dependencies>


</project>
