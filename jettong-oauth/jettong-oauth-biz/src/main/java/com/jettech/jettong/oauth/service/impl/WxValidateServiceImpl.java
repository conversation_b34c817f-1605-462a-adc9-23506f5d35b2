package com.jettech.jettong.oauth.service.impl;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jettech.basic.cache.repository.CachePlusOps;
import com.jettech.basic.database.mybatis.conditions.Wraps;
import com.jettech.basic.exception.BizException;
import com.jettech.basic.utils.HttpUtil;
import com.jettech.jettong.base.entity.rbac.user.User;
import com.jettech.jettong.oauth.properties.WxProperties;
import com.jettech.jettong.oauth.service.UserService;
import com.jettech.jettong.oauth.service.WxValidateService;
import com.jettech.jettong.common.cache.base.rbac.user.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.stereotype.Service;

/**
 * 企业微信验证相关接口
 * <AUTHOR>
 * @version 1.0
 * @description 企业微信验证相关接口
 * @projectName jettong
 * @package com.jettech.jettong.oauth.service.impl
 * @className WxValidateServiceImpl
 * @date 2024/7/29 11:38
 * @copyright @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于沃尔沃内部传阅，禁止外泄以及用于其他的商业目的
 */
@Slf4j
@Service
@RequiredArgsConstructor
@EnableConfigurationProperties(WxProperties.class)
public class WxValidateServiceImpl implements WxValidateService
{
    /**
     * 获取ACCESS_TOKEN请求链接
     */
    private static final String ACCESS_TOKEN_URL = "/cgi-bin/gettoken";
    /**
     * 获取ACCESS_TOKEN请求链接
     */
    private static final String USER_ID_URL = "/cgi-bin/user/getuserinfo";
    /**
     * 根据微信UserId获取成员详情请求链接
     */
    private static final String USER_DETAIL_URL = "/cgi-bin/user/get";
    private final WxProperties wxProperties;
    private final UserService userService;
    private final CachePlusOps cachePlusOps;

    @Override
    public String createAccessToken()
    {
        HttpRequest request = HttpUtil.createGet(wxProperties.getUrl() + ACCESS_TOKEN_URL + "?corpid=" + wxProperties.getCorpID() + "&corpsecret=" + wxProperties.getSecret());
        HttpResponse response = request.execute();

        if (response.isOk())
        {
            // 返回值转换为JSON
            JSONObject json = JSON.parseObject(response.body());

            int errcode = json.getIntValue("errcode");

            if (errcode == 0)
            {
                return json.getString("access_token");
            }
            log.error("微信认证失败,微信原始返回数据：" + json.toJSONString());
        }
        throw BizException.validFail("微信认证失败");
    }

    @Override
    public String getWxUserId(String code)
    {
        HttpRequest request = HttpUtil.createGet(wxProperties.getUrl() + USER_ID_URL + "?access_token=" + createAccessToken() + "&code=" + code);
        HttpResponse response = request.execute();

        if (response.isOk())
        {
            // 返回值转换为JSON
            JSONObject json = JSON.parseObject(response.body());

            int errcode = json.getIntValue("errcode");

            if (errcode == 0)
            {
                return json.getString("UserId");
            }
            log.error("微信认证失败,微信原始返回数据：" + json.toJSONString());
        }
        throw BizException.validFail("微信认证失败");
    }

    @Override
    public JSONObject getUserInfo(String wxUserId)
    {
        HttpRequest request = HttpUtil.createGet(wxProperties.getUrl() + USER_DETAIL_URL + "?access_token=" + createAccessToken() + "&userid=" + wxUserId);
        HttpResponse response = request.execute();

        if (response.isOk())
        {
            // 返回值转换为JSON
            JSONObject json = JSON.parseObject(response.body());

            int errcode = json.getIntValue("errcode");

            if (errcode == 0)
            {
                return json;
            }
            log.error("微信认证失败,微信原始返回数据：" + json.toJSONString());
        }
        throw BizException.validFail("获取微信用户信息失败");
    }

    @Override
    public User checkWxUserId(String wxUserId)
    {
        // 根据微信UserId查询平台用户
        User userByWxUserId = userService.getOne(Wraps.<User>lbQ().eq(User::getWxUserId, wxUserId),false);

        if (null != userByWxUserId)
        {
            return userByWxUserId;
        }

        // 未匹配到用户调用微信【通讯录同步】-【成员管理】-【读取成员】接口，通过手机号和平台账户匹配
        JSONObject json = getUserInfo(wxUserId);

        String mobile = json.getString("mobile");

        // 根据手机号查询平台用户信息
        User userByMobile = userService.getOne(Wraps.<User>lbQ().eq(User::getMobile, mobile), false);

        if (null != userByMobile)
        {
            // 将微信UserId更新到用户表中
            userService.update(Wraps.<User>lbU().set(User::getWxUserId, wxUserId).eq(User::getId, userByMobile.getId()));

            userByMobile.setWxUserId(wxUserId);

            // 清理用户缓存
            cachePlusOps.del(new UserCacheKeyBuilder().key(userByMobile.getId()));
            cachePlusOps.del(new UserRoleCacheKeyBuilder().key(userByMobile.getId()));
            cachePlusOps.del(new UserMenuCacheKeyBuilder().key(userByMobile.getId()));
            cachePlusOps.del(new UserFunctionCacheKeyBuilder().key(userByMobile.getId()));
            cachePlusOps.del(new UserAccountCacheKeyBuilder().key(userByMobile.getAccount()));
        }

        return userByMobile;
    }
}
