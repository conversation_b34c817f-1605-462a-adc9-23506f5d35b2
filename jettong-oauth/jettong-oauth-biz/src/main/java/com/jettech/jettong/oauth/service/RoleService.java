package com.jettech.jettong.oauth.service;

import com.jettech.basic.base.service.SuperCacheService;
import com.jettech.jettong.base.entity.rbac.role.Role;

import java.util.List;

/**
 * 角色业务处理层接口
 *
 * <AUTHOR>
 * @version 1.0
 * @description 角色业务处理层接口
 * @projectName jettong
 * @package com.jettech.jettong.oauth.service
 * @className RoleService
 * @date 2021/9/15 9:43
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
public interface RoleService extends SuperCacheService<Role>
{

    /**
     * 查询用户拥有的角色信息
     *
     * @param userId 用户id
     * @return {@link List< Role>} 角色信息
     * <AUTHOR>
     * @date 2022/6/6 10:23
     * @update 2022/6/6 10:23
     * @since 1.0
     */
    List<Role> findRoleByUserId(Long userId);

}
