package com.jettech.jettong.oauth.granter;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import com.jettech.basic.base.R;
import com.jettech.basic.context.ContextUtil;
import com.jettech.basic.database.properties.DatabaseProperties;
import com.jettech.basic.database.properties.MultiTenantType;
import com.jettech.basic.exception.code.ExceptionCode;
import com.jettech.basic.jwt.TokenUtil;
import com.jettech.basic.jwt.model.AuthInfo;
import com.jettech.basic.jwt.model.JwtUserInfo;
import com.jettech.basic.security.config.EncryptionConfig;
import com.jettech.jettong.base.dto.rbac.user.LoginParamDTO;
import com.jettech.jettong.base.dto.rbac.user.Online;
import com.jettech.jettong.base.entity.rbac.tenant.Tenant;
import com.jettech.jettong.base.entity.rbac.user.User;
import com.jettech.jettong.base.enumeration.rbac.TenantStatus;
import com.jettech.jettong.common.properties.SystemProperties;
import com.jettech.jettong.oauth.event.LoginEvent;
import com.jettech.jettong.oauth.event.model.LoginStatusDTO;
import com.jettech.jettong.oauth.service.OnlineService;
import com.jettech.jettong.oauth.service.TenantService;
import com.jettech.jettong.oauth.service.UserService;
import com.jettech.basic.utils.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;

@Slf4j
@RequiredArgsConstructor
public abstract class AbstractTokenGranter implements TokenGranter
{

    protected final TokenUtil tokenUtil;
    protected final UserService userService;
    protected final TenantService tenantService;
    protected final DatabaseProperties databaseProperties;
    protected final OnlineService onlineService;
    protected final SystemProperties systemProperties;
    protected final EncryptionConfig encryptionConfig;

    /**
     * 处理登录逻辑
     *
     * @param loginParam 登录参数
     * @return 认证信息
     */
    protected R<AuthInfo> login(LoginParamDTO loginParam)
    {
        if (StrHelper.isAnyBlank(loginParam.getAccount(), loginParam.getPassword()))
        {
            return R.fail("请输入用户名或密码");
        }

        if (!MultiTenantType.NONE.eq(databaseProperties.getMultiTenantType()))
        {
            Tenant tenant = tenantService.getByCode(ContextUtil.getTenant());
            ArgumentAssert.notNull(tenant, "租户不存在");
            ArgumentAssert.equals(TenantStatus.NORMAL, tenant.getStatus(), "租户不存在~");
            if (tenant.getExpirationTime() != null)
            {
                ArgumentAssert.isFalse(LocalDateTime.now().isAfter(tenant.getExpirationTime()), "企业服务已到期!");
            }
        }

        // 3.验证登录
        R<User> result = this.getUser(loginParam.getAccount(), loginParam.getPassword());
        if (!result.getIsSuccess())
        {
            return R.fail(result.getCode(), result.getMsg());
        }

        // 生成 token
        User user = result.getData();
        AuthInfo authInfo = this.createToken(user);

        Online online = getOnline(authInfo);

        //成功登录事件
        LoginStatusDTO loginStatus = LoginStatusDTO.success(user.getId(), online);
        SpringUtils.publishEvent(new LoginEvent(loginStatus));

        onlineService.save(online);
        return R.success(authInfo);
    }

    protected Online getOnline(AuthInfo authInfo)
    {
        Online online = new Online();
        BeanPlusUtil.copyProperties(authInfo, online);
        online.setExpireTime(authInfo.getExpiration());
        online.setLoginTime(LocalDateTime.now());
        online.setAccount(authInfo.getUserAccount());
        online.setName(authInfo.getUserName());
        return online;
    }

    /**
     * 检测用户密码是否正确
     *
     * @param account 账号
     * @param password 密码
     * @return 用户信息
     */
    protected R<User> getUser(String account, String password)
    {
        User user = this.userService.getByAccount(account);
        // 密码错误
        if (user == null)
        {
            return R.fail(ExceptionCode.JWT_USER_INVALID);
        }

        // 方便开发、测试、演示环境 开发者登录别人的账号，生产环境禁用。
        if (!systemProperties.getVerifyPassword())
        {
            return R.success(user);
        }

        // 用户锁定
        Integer maxPasswordErrorNum = systemProperties.getMaxPasswordErrorNum();
        Integer passwordErrorNum = Convert.toInt(user.getPasswordErrorNum(), 0);
        if (maxPasswordErrorNum > 0 && passwordErrorNum >= maxPasswordErrorNum)
        {
            log.info("[{}][{}], 输错密码次数：{}, 最大限制次数:{}", user.getName(), user.getId(), passwordErrorNum,
                    maxPasswordErrorNum);

            LocalDateTime passwordErrorLockTime =
                    DateUtils.conversionDateTime(systemProperties.getPasswordErrorLockUserTime());
            log.info("passwordErrorLockTime={}", passwordErrorLockTime);
            if (passwordErrorLockTime.isBefore(user.getPasswordErrorLastTime()))
            {
                // 登录失败事件
                String msg = StrUtil.format("密码连续输错次数已达到{}次,用户已被锁定~", maxPasswordErrorNum);
                SpringUtils.publishEvent(new LoginEvent(LoginStatusDTO.fail(user.getId(), msg)));
                return R.fail(msg);
            }
        }

//        String passwordMd5 = SecureUtil.sha256(password + user.getSalt());
        String passwordMd5 = null;
        try {
            passwordMd5 = encryptionConfig.encryptionService().encrypt(password, user.getSalt());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        if (!passwordMd5.equalsIgnoreCase(user.getPassword()))
        {
            String msg = "用户名或密码错误!";
            // 密码错误事件
            SpringUtils.publishEvent(new LoginEvent(LoginStatusDTO.pwdError(user.getId(), msg)));
            return R.fail(msg);
        }

        if (!user.getState())
        {
            String msg = "用户被禁用，请联系管理员！";
            SpringUtils.publishEvent(new LoginEvent(LoginStatusDTO.fail(user.getId(), msg)));
            return R.fail(msg);
        }

        return R.success(user);
    }

    /**
     * 创建用户TOKEN
     *
     * @param user 用户
     * @return token
     */
    protected AuthInfo createToken(User user)
    {
        JwtUserInfo userInfo = new JwtUserInfo(user.getId(), user.getAccount(), user.getName(), user.getOrgId());
        AuthInfo authInfo = tokenUtil.createAuthInfo(userInfo, null);
        authInfo.setAvatarType(user.getAvatarType());
        authInfo.setAvatarPath(user.getAvatarPath());
        authInfo.setDescription(user.getDescription());
        return authInfo;
    }


}
