package com.jettech.jettong.oauth.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.jettech.basic.base.service.SuperCacheServiceImpl;
import com.jettech.basic.cache.model.CacheKey;
import com.jettech.basic.cache.model.CacheKeyBuilder;
import com.jettech.jettong.base.entity.rbac.role.Role;
import com.jettech.jettong.common.cache.base.rbac.role.RoleCacheKeyBuilder;
import com.jettech.jettong.common.cache.base.rbac.user.UserRoleCacheKeyBuilder;
import com.jettech.jettong.oauth.dao.RoleMapper;
import com.jettech.jettong.oauth.service.RoleService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 角色业务处理层
 *
 * <AUTHOR>
 * @version 1.0
 * @description 角色业务处理层
 * @projectName jettong
 * @package com.jettech.jettong.base.service.rbac.role.impl
 * @className RoleServiceImpl
 * @date 2021/9/15 9:43
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Slf4j
@Service
@DS("#thread.tenant")
@RequiredArgsConstructor
public class RoleServiceImpl extends SuperCacheServiceImpl<RoleMapper, Role> implements RoleService
{
    @Override
    protected CacheKeyBuilder cacheKeyBuilder()
    {
        return new RoleCacheKeyBuilder();
    }

    @Override
    public List<Role> findRoleByUserId(Long userId)
    {
        CacheKey cacheKey = new UserRoleCacheKeyBuilder().key(userId);
        return cacheOps.get(cacheKey, k ->
        {
            List<Role> roles = baseMapper.findRoleByUserId(userId);

            cacheOps.set(cacheKey, roles, false);
            return roles;
        });
    }

}
