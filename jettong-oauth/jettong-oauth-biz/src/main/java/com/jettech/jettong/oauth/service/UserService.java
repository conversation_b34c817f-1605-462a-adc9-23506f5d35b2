package com.jettech.jettong.oauth.service;

import com.jettech.basic.base.service.SuperCacheService;
import com.jettech.basic.security.feign.UserQuery;
import com.jettech.basic.security.model.SysUser;
import com.jettech.jettong.base.entity.rbac.user.User;

/**
 * 用户业务处理层接口
 *
 * <AUTHOR>
 * @version 1.0
 * @description 用户业务处理层接口
 * @projectName jettong
 * @package com.jettech.jettong.oauth.service
 * @className UserService
 * @date 2021/9/15 9:43
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
public interface UserService extends SuperCacheService<User>
{

    /**
     * 修改输错密码的次数
     *
     * @param id 用户Id
     * <AUTHOR>
     * @date 2022/6/1 10:57
     * @update 2022/6/1 10:57
     * @since 1.0
     */
    void incrPasswordErrorNumById(Long id);

    /**
     * 根据账号查询用户
     * @param account 账号
     * @return {@link User} 用户
     * <AUTHOR>
     * @date 2022/6/1 10:57
     * @update 2022/6/1 10:57
     * @since 1.0
     */
    User getByAccount(String account);


    /**
     * 重置密码错误次数
     * @param id 用户id
     * @return int 重置了多少行
     * <AUTHOR>
     * @date 2022/6/1 10:54
     * @since 1.0
     */
    int resetPassErrorNum(Long id);


    /**
     * 根据id 查询用户详情
     * @param id 用户id
     * @param query 查询条件
     * @return {@link SysUser}  用户详情
     * <AUTHOR>
     * @date 2022/6/1 10:56
     * @since 1.0
     */
    SysUser getSysUserById(Long id, UserQuery query);

}
