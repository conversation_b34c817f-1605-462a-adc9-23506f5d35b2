package com.jettech.jettong.oauth.service;

import com.alibaba.fastjson.JSONObject;
import com.jettech.jettong.base.entity.rbac.user.User;

/**
 * 企业微信验证相关接口
 * <AUTHOR>
 * @version 1.0
 * @description 企业微信验证相关接口
 * @projectName HBSC
 * @package com.jettech.jettong.oauth.service
 * @className WxValidateService
 * @date 2024/7/29 11:33
 * @copyright @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于沃尔沃内部传阅，禁止外泄以及用于其他的商业目的
 */
public interface WxValidateService
{
    /**
     * 获取企业微信ACCESS_TOKEN
     * @return String 微信ACCESS_TOKEN
     * <AUTHOR>
     * @date 2024/7/29 11:36
     * @update 2024/7/29 11:36
     * @since 1.0
     */
    String createAccessToken();

    /**
     * 根据code获取微信UserId
     * @param code 微信Oauth2.0 code
     * @return String 微信UserId
     * <AUTHOR>
     * @date 2024/7/29 11:36
     * @update 2024/7/29 11:36
     * @since 1.0
     */
    String getWxUserId(String code);

    /**
     * 根据微信UserId读取成员信息
     * @param wxUserId 微信UserId
     * @return JSONObject 成员信息
     * <AUTHOR>
     * @date 2024/7/29 12:09
     * @update 2024/7/29 12:09
     * @since 1.0
     */
    JSONObject getUserInfo(String wxUserId);

    /**
     * 根据微信UserId验证用户是否在平台中存在
     * @param wxUserId 微信userId
     * <AUTHOR>
     * @date 2024/7/29 11:37
     * @update 2024/7/29 11:37
     * @since 1.0
     */
    User checkWxUserId(String wxUserId);
}
