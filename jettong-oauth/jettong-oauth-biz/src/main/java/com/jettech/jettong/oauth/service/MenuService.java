package com.jettech.jettong.oauth.service;

import com.jettech.basic.base.service.SuperCacheService;
import com.jettech.jettong.base.entity.rbac.role.Menu;

import java.util.List;

/**
 * 菜单业务处理层接口
 *
 * <AUTHOR>
 * @version 1.0
 * @description 菜单业务处理层接口
 * @projectName jettong
 * @package com.jettech.jettong.oauth.service
 * @className MenuService
 * @date 2021/9/15 9:43
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
public interface MenuService extends SuperCacheService<Menu>
{

    /**
     * 根据用户角色查询用户可用菜单
     *
     * @param userId 用户id
     * @param roleIds 用户角色id
     * @return List<Function> 可用菜单信息
     * <AUTHOR>
     * @date 2021/10/23 16:21
     * @update zxy 2021/10/23 16:21
     * @since 1.0
     */
    List<Menu> findVisibleMenu(Long userId, List<Long> roleIds,String platform);

    /**
     * 根据用户角色查询用户可用功能
     *
     * @param userId 用户id
     * @param roleIds 用户角色id
     * @return List<Menu> 可用功能信息
     * <AUTHOR>
     * @date 2021/10/23 16:21
     * @update zxy 2021/10/23 16:21
     * @since 1.0
     */
    List<Menu> findVisibleFunction(Long userId, List<Long> roleIds);
}
