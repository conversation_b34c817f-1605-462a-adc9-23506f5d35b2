package com.jettech.jettong.oauth.granter;

import com.jettech.basic.base.R;
import com.jettech.basic.database.properties.DatabaseProperties;
import com.jettech.basic.exception.BizException;
import com.jettech.basic.jwt.TokenUtil;
import com.jettech.basic.jwt.model.AuthInfo;
import com.jettech.basic.security.config.EncryptionConfig;
import com.jettech.basic.utils.SpringUtils;
import com.jettech.jettong.base.dto.rbac.user.LoginParamDTO;
import com.jettech.jettong.common.properties.SystemProperties;
import com.jettech.jettong.oauth.event.LoginEvent;
import com.jettech.jettong.oauth.event.model.LoginStatusDTO;
import com.jettech.jettong.oauth.service.OnlineService;
import com.jettech.jettong.oauth.service.TenantService;
import com.jettech.jettong.oauth.service.UserService;
import com.jettech.jettong.oauth.service.ValidateCodeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import static com.jettech.jettong.oauth.granter.CaptchaTokenGranter.GRANT_TYPE;

/**
 * 验证码TokenGranter
 *
 * <AUTHOR>
 */
@Component(GRANT_TYPE)
@Slf4j
public class CaptchaTokenGranter extends AbstractTokenGranter implements TokenGranter
{

    public static final String GRANT_TYPE = "captcha";
    private final ValidateCodeService validateCodeService;

    public CaptchaTokenGranter(TokenUtil tokenUtil, UserService userService, TenantService tenantService,
                               DatabaseProperties databaseProperties, ValidateCodeService validateCodeService,
                               OnlineService onlineService, SystemProperties systemProperties, EncryptionConfig encryptionConfig)
    {
        super(tokenUtil, userService, tenantService, databaseProperties,
                onlineService, systemProperties, encryptionConfig);
        this.validateCodeService = validateCodeService;
    }

    @Override
    public R<AuthInfo> grant(LoginParamDTO loginParam)
    {
        if (systemProperties.getVerifyCaptcha())
        {
            R<Boolean> check = validateCodeService.check(loginParam.getKey(), loginParam.getCode());
            if (!check.getIsSuccess())
            {
                String msg = check.getMsg();
                SpringUtils.publishEvent(new LoginEvent(LoginStatusDTO.fail(loginParam.getAccount(), msg)));
                throw BizException.validFail(check.getMsg());
            }
        }

        return login(loginParam);
    }

}
