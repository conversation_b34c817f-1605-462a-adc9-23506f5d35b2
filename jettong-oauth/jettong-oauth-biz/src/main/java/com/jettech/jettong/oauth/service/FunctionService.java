package com.jettech.jettong.oauth.service;

import com.jettech.basic.base.service.SuperCacheService;
import com.jettech.jettong.base.dto.rbac.role.FunctionQueryDTO;
import com.jettech.jettong.base.entity.rbac.role.Function;

import java.util.List;

/**
 * 菜单功能业务处理层接口
 *
 * <AUTHOR>
 * @version 1.0
 * @description 菜单功能业务处理层接口
 * @projectName jettong
 * @package com.jettech.jettong.oauth.service
 * @className FunctionService
 * @date 2021/9/15 9:43
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
public interface FunctionService extends SuperCacheService<Function>
{

    /**
     * 根据条件查询拥有的功能接口权限信息
     *
     * @param functionQueryDTO 查询条件
     * @return List<Function> 拥有的功能接口信息
     * <AUTHOR>
     * @date 2021/10/23 16:21
     * @update zxy 2021/10/23 16:21
     * @since 1.0
     */
    List<Function> findVisibleFunction(FunctionQueryDTO functionQueryDTO);
}
