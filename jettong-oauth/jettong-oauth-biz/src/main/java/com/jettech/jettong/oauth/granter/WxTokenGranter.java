package com.jettech.jettong.oauth.granter;

import com.jettech.basic.base.R;
import com.jettech.basic.database.properties.DatabaseProperties;
import com.jettech.basic.jwt.TokenUtil;
import com.jettech.basic.jwt.model.AuthInfo;
import com.jettech.basic.security.config.EncryptionConfig;
import com.jettech.basic.utils.SpringUtils;
import com.jettech.jettong.base.dto.rbac.user.LoginParamDTO;
import com.jettech.jettong.base.dto.rbac.user.Online;
import com.jettech.jettong.base.entity.rbac.user.User;
import com.jettech.jettong.common.properties.SystemProperties;
import com.jettech.jettong.oauth.event.LoginEvent;
import com.jettech.jettong.oauth.event.model.LoginStatusDTO;
import com.jettech.jettong.oauth.service.OnlineService;
import com.jettech.jettong.oauth.service.TenantService;
import com.jettech.jettong.oauth.service.UserService;
import com.jettech.jettong.oauth.service.WxValidateService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import static com.jettech.jettong.oauth.granter.WxTokenGranter.GRANT_TYPE;
/**
 * 企业微信登录
 * <AUTHOR>
 * @version 1.0
 * @description 企业微信登录
 * @projectName jettong
 * @package com.jettech.jettong.oauth.granter
 * @className WxTokenGranter
 * @date 2024/7/29 11:19
 * @copyright @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于沃尔沃内部传阅，禁止外泄以及用于其他的商业目的
 */
@Slf4j
@Component(GRANT_TYPE)
public class WxTokenGranter extends AbstractTokenGranter implements TokenGranter
{
    public static final String GRANT_TYPE = "wx";

    private final WxValidateService wxValidateService;

    public WxTokenGranter(TokenUtil tokenUtil, UserService userService, TenantService tenantService,
                          DatabaseProperties databaseProperties, WxValidateService wxValidateService,
                          OnlineService onlineService, SystemProperties systemProperties, EncryptionConfig encryptionConfig)
    {
        super(tokenUtil, userService, tenantService, databaseProperties,
                onlineService, systemProperties, encryptionConfig);
        this.wxValidateService = wxValidateService;
    }

    @Override
    public R<AuthInfo> grant(LoginParamDTO loginParam)
    {
        // 获取微信UserId
        String wxUserId = wxValidateService.getWxUserId(loginParam.getCode());

        // 微信微信UserId获取平台用户信息
        User user = wxValidateService.checkWxUserId(wxUserId);

        if (null == user)
        {
            return R.fail("用户在平台中不存在，请联系管理员");
        }

        // 生成 token
        AuthInfo authInfo = this.createToken(user);

        Online online = getOnline(authInfo);

        //成功登录事件
        LoginStatusDTO loginStatus = LoginStatusDTO.success(user.getId(), online);
        SpringUtils.publishEvent(new LoginEvent(loginStatus));

        onlineService.save(online);
        return R.success(authInfo);
    }

}
