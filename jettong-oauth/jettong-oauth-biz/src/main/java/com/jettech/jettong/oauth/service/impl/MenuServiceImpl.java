package com.jettech.jettong.oauth.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.jettech.basic.base.entity.SuperEntity;
import com.jettech.basic.base.entity.TreeEntity;
import com.jettech.basic.base.service.SuperCacheServiceImpl;
import com.jettech.basic.cache.model.CacheKey;
import com.jettech.basic.cache.model.CacheKeyBuilder;
import com.jettech.jettong.base.entity.rbac.role.Menu;
import com.jettech.jettong.common.cache.base.rbac.role.MenuCacheKeyBuilder;
import com.jettech.jettong.common.cache.base.rbac.user.UserMenuCacheKeyBuilder;
import com.jettech.jettong.oauth.dao.MenuMapper;
import com.jettech.jettong.oauth.service.MenuService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 菜单业务处理层
 *
 * <AUTHOR>
 * @version 1.0
 * @description 菜单业务处理层
 * @projectName jettong
 * @package com.jettech.jettong.oauth.service.impl
 * @className MenuServiceImpl
 * @date 2021/9/15 9:43
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Slf4j
@Service
@DS("#thread.tenant")
@RequiredArgsConstructor
public class MenuServiceImpl extends SuperCacheServiceImpl<MenuMapper, Menu> implements MenuService
{

    private static final int MAX_RECURSION_DEPTH = 20;

    @Override
    protected CacheKeyBuilder cacheKeyBuilder()
    {
        return new MenuCacheKeyBuilder();
    }

    @Override
    public List<Menu> findVisibleMenu(Long userId, List<Long> roleIds,String platform)
    {
        CacheKey userMenuKey = new UserMenuCacheKeyBuilder().key(userId);

        List<Menu> list = cacheOps.get(userMenuKey, k ->
        {
            List<Menu> menus = baseMapper.findVisibleMenu(roleIds);
            supplementParent(menus, 0);
            // 将菜单父级菜单
            cacheOps.set(userMenuKey, menus, false);
            return menus;
        });
        if(StringUtils.isEmpty(platform)|| "0".equals(platform)){
            return list.stream().filter(item -> item.getIsGeneral() || (!item.getIsButton() || !"#".equals(item.getPath())))
                    .collect(Collectors.toList());
        }else {
            return list.stream().filter(item -> item.getPlatform().equals(platform))
                    .collect(Collectors.toList());
        }

    }

    /**
     * 补充缺失的父级菜单
     * @param menus 菜单集合
     * @param depth 递归深度
     */
    private void supplementParent(List<Menu> menus, int depth) {
        Set<Long> ids = menus.stream().map(SuperEntity::getId).collect(Collectors.toSet());
        Set<Long> parentIds = menus.stream()
                .map(TreeEntity::getParentId)
                .filter(Objects::nonNull)
                .filter(id -> id != 0L)
                .collect(Collectors.toSet());

        parentIds.removeAll(ids);
        if (parentIds.isEmpty()) {
            return;
        }

        // 达到最大递归深度时，认为该菜单的父级已经被删除，不返回该菜单
        if (depth >= MAX_RECURSION_DEPTH) {
            menus.removeIf(m -> parentIds.contains(m.getParentId()));
        }

        menus.addAll(this.listByIds(parentIds));
        supplementParent(menus, depth + 1);
    }


    @Override
    public List<Menu> findVisibleFunction(Long userId, List<Long> roleIds)
    {
        CacheKey userMenuKey = new UserMenuCacheKeyBuilder().key(userId);

        List<Menu> list = cacheOps.get(userMenuKey, k ->
        {
            List<Menu> menus = baseMapper.findVisibleMenu(roleIds);
            cacheOps.set(userMenuKey, menus, false);
            return menus;
        });

        return list.stream().filter(Menu::getIsButton).collect(Collectors.toList());
    }
}
