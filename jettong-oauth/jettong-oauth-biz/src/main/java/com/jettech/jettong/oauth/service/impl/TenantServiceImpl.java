package com.jettech.jettong.oauth.service.impl;

import cn.hutool.core.convert.Convert;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.jettech.basic.base.service.SuperCacheServiceImpl;
import com.jettech.basic.cache.model.CacheKey;
import com.jettech.basic.cache.model.CacheKeyBuilder;
import com.jettech.basic.database.mybatis.conditions.Wraps;
import com.jettech.jettong.base.entity.rbac.tenant.Tenant;
import com.jettech.jettong.common.cache.tenant.TenantCacheKeyBuilder;
import com.jettech.jettong.common.cache.tenant.TenantCodeCacheKeyBuilder;
import com.jettech.jettong.oauth.dao.TenantMapper;
import com.jettech.jettong.oauth.service.TenantService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.function.Function;

/**
 * 租户业务处理层
 *
 * <AUTHOR>
 * @version 1.0
 * @description 租户业务处理层
 * @projectName jettong
 * @package com.jettech.jettong.oauth.service.impl
 * @className TenantServiceImpl
 * @date 2021/9/15 9:43
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Slf4j
@Service
@DS("master")
@RequiredArgsConstructor
public class TenantServiceImpl extends SuperCacheServiceImpl<TenantMapper, Tenant> implements TenantService
{

    @Override
    protected CacheKeyBuilder cacheKeyBuilder()
    {
        return new TenantCacheKeyBuilder();
    }

    @Override
    public Tenant getByCode(String tenant)
    {
        Function<CacheKey, Object> loader = (k) ->
                getObj(Wraps.<Tenant>lbQ().select(Tenant::getId).eq(Tenant::getCode, tenant), Convert::toLong);
        CacheKey cacheKey = new TenantCodeCacheKeyBuilder().key(tenant);
        return getByKey(cacheKey, loader);
    }

}
