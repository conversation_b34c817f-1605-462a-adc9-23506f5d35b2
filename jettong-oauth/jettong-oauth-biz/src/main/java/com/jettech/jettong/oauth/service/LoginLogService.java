package com.jettech.jettong.oauth.service;

import com.jettech.basic.base.service.SuperService;
import com.jettech.jettong.base.entity.sys.log.LoginLog;

/**
 * 登录日志业务处理层接口
 *
 * <AUTHOR>
 * @version 1.0
 * @description 登录日志业务处理层接口
 * @projectName jettong
 * @package com.jettech.jettong.oauth.service
 * @className LoginLogService
 * @date 2021/9/15 9:43
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
public interface LoginLogService extends SuperService<LoginLog>
{

    /**
     * 记录登录日志
     *
     * @param userId 用户id
     * @param account 账号
     * @param ua 浏览器
     * @param ip 客户端IP
     * @param location 客户端地址
     * @param description 登陆描述消息
     * @return 登录日志
     */
    LoginLog save(Long userId, String account, String ua, String ip, String location, String description);

}
