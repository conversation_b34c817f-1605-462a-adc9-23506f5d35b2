package com.jettech.jettong.oauth.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jettech.basic.database.mybatis.conditions.Wraps;
import com.jettech.basic.exception.BizException;
import com.jettech.basic.utils.HttpUtil;
import com.jettech.jettong.base.entity.rbac.user.User;
import com.jettech.jettong.oauth.properties.OaProperties;
import com.jettech.jettong.oauth.service.OaValidateService;
import com.jettech.jettong.oauth.service.UserService;
import com.jettech.jettong.oauth.util.SecurityUtils;
import com.google.common.collect.Maps;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 企业微信验证相关接口
 * <AUTHOR>
 * @version 1.0
 * @description 企业微信验证相关接口
 * @projectName jettong
 * @package com.jettech.jettong.oauth.service.impl
 * @className WxValidateServiceImpl
 * @date 2024/7/29 11:38
 * @copyright @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于沃尔沃内部传阅，禁止外泄以及用于其他的商业目的
 */
@Slf4j
@Service
@RequiredArgsConstructor
@EnableConfigurationProperties(OaProperties.class)
public class OaValidateServiceImpl implements OaValidateService
{
    /**
     * 验证TOKEN请求链接
     */
    private static final String CHECK_TOKEN_URL = "/cus/ThirdTokenService/checkTokenUsed";

    private final OaProperties oaProperties;
    private final UserService userService;

    @Override
    public User checkToken(String oaToken, String idCard)
    {
        // 解密idCard
        String decIdCard;
        try
        {
            decIdCard = SecurityUtils.decrypt(idCard, oaProperties.getKey());
        }
        catch(Exception e)
        {
            log.error("解密idCard失败", e);
            throw BizException.validFail("认证失败，请联系管理员");
        }

        // 根据idCard查询平台用户
        User userByIdCard = userService.getOne(Wraps.<User>lbQ().eq(User::getIdCard, decIdCard),false);

        if (null == userByIdCard)
        {
            throw BizException.validFail("平台中不存在该用户，请联系管理员处理");
        }

        // 验证token
        HttpRequest request = HttpUtil.createPost(oaProperties.getUrl() + CHECK_TOKEN_URL);
        // 验证参数
        Map<String, Object> checkParam = Maps.newHashMapWithExpectedSize(4);
        checkParam.put("token", oaToken);
        checkParam.put("from", oaProperties.getAppId());
        checkParam.put("ipadress", oaProperties.getIpAdress());
        checkParam.put("time", DateUtil.formatLocalDateTime(LocalDateTime.now()));

        request.body(JSONObject.toJSONString(checkParam));
        HttpResponse response = request.execute();

        if (response.isOk())
        {
            // 返回值转换为JSON
            JSONObject json = JSON.parseObject(response.body());

            String code = json.getString("code");

            if (!"0".equals(code))
            {
                throw BizException.validFail("OA认证失败");
            }
        }

        return userByIdCard;
    }
}
