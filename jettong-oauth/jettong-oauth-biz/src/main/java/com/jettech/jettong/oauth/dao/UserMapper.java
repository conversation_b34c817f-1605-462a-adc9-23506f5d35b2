package com.jettech.jettong.oauth.dao;

import com.jettech.basic.base.mapper.SuperMapper;
import com.jettech.jettong.base.entity.rbac.user.User;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;

/**
 * 用户持久化层接口
 *
 * <AUTHOR>
 * @version 1.0
 * @description 用户持久化层接口
 * @projectName jettong
 * @package com.jettech.jettong.oauth.dao
 * @className UserMapper
 * @date 2021/9/15 9:43
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Repository
public interface UserMapper extends SuperMapper<User>
{

    /**
     * 递增 密码错误次数
     *
     * @param id 用户id
     * @return 被修改了几行数据
     */
    int incrPasswordErrorNumById(@Param("id") Long id);

    /**
     * 重置 密码错误次数
     *
     * @param id 用户id
     * @param now 当前时间
     * @return 被修改了几行数据
     */
    int resetPassErrorNum(@Param("id") Long id, @Param("now") LocalDateTime now);

}
