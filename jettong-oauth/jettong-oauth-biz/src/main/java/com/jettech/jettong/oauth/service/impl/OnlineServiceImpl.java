package com.jettech.jettong.oauth.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.jettech.basic.cache.model.CacheKey;
import com.jettech.basic.cache.repository.CachePlusOps;
import com.jettech.basic.utils.StrPool;
import com.jettech.jettong.base.dto.rbac.user.Online;
import com.jettech.jettong.common.cache.common.OnlineCacheKeyBuilder;
import com.jettech.jettong.common.cache.common.TokenUserIdCacheKeyBuilder;
import com.jettech.jettong.common.constant.BizConstant;
import com.jettech.jettong.common.constant.ParameterKey;
import com.jettech.jettong.oauth.service.OnlineService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 在线用户业务处理层
 *
 * <AUTHOR>
 * @version 1.0
 * @description 在线用户业务处理层
 * @projectName jettong
 * @package com.jettech.jettong.oauth.service.impl
 * @className OnlineServiceImpl
 * @date 2021/9/15 9:43
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Slf4j
@Service
@DS("#thread.tenant")
@RequiredArgsConstructor
public class OnlineServiceImpl implements OnlineService
{

    private final CachePlusOps cacheOps;

    @Value("${jettong.login.policy:MANY}")
    private String loginPolicy;

    /**
     * 1，MANY: 用户可以任意登录： token -> userid
     * 2，ONLY_ONE: 一个用户只能登录一次
     *
     * @param model 用户token
     * @return 是否成功
     */
    @Override
    public boolean save(Online model)
    {
        if (ParameterKey.LoginPolicy.ONLY_ONE.eq(loginPolicy))
        {
            // online:1  === {model}
            CacheKey key = new OnlineCacheKeyBuilder().key(model.getUserId());
            setToken(model, key);
        }
        else
        {
            CacheKey key = new OnlineCacheKeyBuilder().key(model.getUserId());
            key.setExpire(Duration.ofSeconds(model.getExpireMillis()));
            cacheOps.set(key, model);
        }

        // TOKEN_USER_ID:{token} === 1
        CacheKey tokenKey = new TokenUserIdCacheKeyBuilder().key(model.getToken());
        tokenKey.setExpire(Duration.ofSeconds(model.getExpireMillis()));
        cacheOps.set(tokenKey, String.valueOf(model.getUserId()));
        return true;
    }

    private void setToken(Online model, CacheKey key)
    {
        key.setExpire(Duration.ofSeconds(model.getExpireMillis()));
        evictPreviousToken(key);
        cacheOps.set(key, model);
    }

    private void evictPreviousToken(CacheKey key)
    {
        Online online = cacheOps.get(key, false);
        if (online != null && StrUtil.isNotEmpty(online.getToken()))
        {
            String previousToken = online.getToken();
            // TOKEN_USER_ID:{token} === T
            CacheKey cacheKey = new TokenUserIdCacheKeyBuilder().key(previousToken);
            cacheOps.set(cacheKey, BizConstant.LOGIN_STATUS);
        }
    }

    @Override
    public boolean clear(String token, Long userId, String clientId)
    {
        CacheKey key = new OnlineCacheKeyBuilder().key(userId);
        cacheOps.del(key);

        evictPreviousToken(key);
        CacheKey tokenKey = new TokenUserIdCacheKeyBuilder().key(token);
        cacheOps.del(tokenKey);
        return true;
    }

    @Override
    public boolean reset()
    {
        CacheKey pattern = new OnlineCacheKeyBuilder().key(StrPool.STAR);
        Set<String> keys = cacheOps.keys(pattern.getKey());
        cacheOps.del(keys.toArray(new String[0]));

        CacheKey cacheKey = new TokenUserIdCacheKeyBuilder().key(StrPool.STAR);
        Set<String> tokenUserIdKeys = cacheOps.keys(cacheKey.getKey());
        cacheOps.del(tokenUserIdKeys.toArray(new String[0]));
        return true;
    }

    @Override
    public List<Online> list(String name) {
        String pattern = new OnlineCacheKeyBuilder().getPattern();
        List<String> keys = cacheOps.scan(pattern);

        return keys.stream()
                .map(key -> (Online) cacheOps.get(key))
                .filter(ObjectUtil::isNotEmpty).filter(item ->
                        StrUtil.isEmpty(name) || StrUtil.contains(item.getName(), name)
                ).collect(Collectors.toList());
    }
}
