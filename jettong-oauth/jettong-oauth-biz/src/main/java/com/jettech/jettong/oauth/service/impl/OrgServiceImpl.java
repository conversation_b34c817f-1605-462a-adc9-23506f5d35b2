package com.jettech.jettong.oauth.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.jettech.basic.base.service.SuperCacheServiceImpl;
import com.jettech.basic.cache.model.CacheKeyBuilder;
import com.jettech.jettong.base.entity.rbac.org.Org;
import com.jettech.jettong.common.cache.base.rbac.org.OrgCacheKeyBuilder;
import com.jettech.jettong.oauth.dao.OrgMapper;
import com.jettech.jettong.oauth.service.OrgService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 组织机构业务处理层
 *
 * <AUTHOR>
 * @version 1.0
 * @description 组织机构业务处理层
 * @projectName jettong
 * @package com.jettech.jettong.base.service.rbac.org.impl
 * @className OrgServiceImpl
 * @date 2021/9/15 9:43
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Slf4j
@Service
@DS("#thread.tenant")
@RequiredArgsConstructor
public class OrgServiceImpl extends SuperCacheServiceImpl<OrgMapper, Org> implements OrgService
{
    @Override
    protected CacheKeyBuilder cacheKeyBuilder()
    {
        return new OrgCacheKeyBuilder();
    }

}
