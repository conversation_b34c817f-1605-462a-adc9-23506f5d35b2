package com.jettech.jettong.oauth.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.convert.Convert;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.jettech.basic.base.service.SuperCacheServiceImpl;
import com.jettech.basic.cache.model.CacheKey;
import com.jettech.basic.cache.model.CacheKeyBuilder;
import com.jettech.basic.database.mybatis.conditions.Wraps;
import com.jettech.basic.security.feign.UserQuery;
import com.jettech.basic.security.model.SysOrg;
import com.jettech.basic.security.model.SysRole;
import com.jettech.basic.security.model.SysUser;
import com.jettech.basic.utils.BeanPlusUtil;
import com.jettech.basic.utils.CollHelper;
import com.jettech.basic.utils.StrPool;
import com.jettech.jettong.base.dto.rbac.role.FunctionQueryDTO;
import com.jettech.jettong.base.entity.rbac.role.Function;
import com.jettech.jettong.base.entity.rbac.role.Role;
import com.jettech.jettong.base.entity.rbac.user.User;
import com.jettech.jettong.base.entity.rbac.user.UserRole;
import com.jettech.jettong.common.cache.base.rbac.user.UserAccountCacheKeyBuilder;
import com.jettech.jettong.common.cache.base.rbac.user.UserCacheKeyBuilder;
import com.jettech.jettong.oauth.dao.UserMapper;
import com.jettech.jettong.oauth.dao.UserRoleMapper;
import com.jettech.jettong.oauth.service.FunctionService;
import com.jettech.jettong.oauth.service.OrgService;
import com.jettech.jettong.oauth.service.RoleService;
import com.jettech.jettong.oauth.service.UserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户业务处理层
 *
 * <AUTHOR>
 * @version 1.0
 * @description 用户业务处理层
 * @projectName jettong
 * @package com.jettech.jettong.base.service.rbac.user.impl
 * @className UserServiceImpl
 * @date 2021/9/15 9:43
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Slf4j
@Service
@DS("#thread.tenant")
@RequiredArgsConstructor
public class UserServiceImpl extends SuperCacheServiceImpl<UserMapper, User> implements UserService
{

    private final RoleService roleService;

    private final FunctionService functionService;

    private final OrgService orgService;

    private final UserRoleMapper userRoleMapper;

    @Override
    protected CacheKeyBuilder cacheKeyBuilder()
    {
        return new UserCacheKeyBuilder();
    }

    @Override
    public User getByAccount(String account)
    {
        return getByKey(new UserAccountCacheKeyBuilder().key(account),
                k -> getObj(Wraps.<User>lbQ().select(User::getId).eq(User::getAccount, account), Convert::toLong));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void incrPasswordErrorNumById(Long id)
    {
        baseMapper.incrPasswordErrorNumById(id);
        delCache(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int resetPassErrorNum(Long id)
    {
        int count = baseMapper.resetPassErrorNum(id, LocalDateTime.now());
        delCache(id);
        return count;
    }

    @Override
    public User getByIdCache(Serializable id)
    {
        CacheKey cacheKey = cacheKeyBuilder().key(id);
        return cacheOps.get(cacheKey, k ->
        {
            User user = super.getById(id);
            if (null != user)
            {
                user.setUserRoles(userRoleMapper.selectList(Wraps.<UserRole>lbQ().eq(UserRole::getUserId, id)));
            }
            cacheOps.set(cacheKey, user, false);
            return user;
        });
    }

    @Override
    public SysUser getSysUserById(Long id, UserQuery query)
    {
        User user = getByIdCache(id);
        if (user == null)
        {
            return null;
        }
        SysUser sysUser = BeanUtil.toBean(user, SysUser.class);

        sysUser.setOrgId(user.getOrgId());

        if (query.getFull() || query.getOrg())
        {
            sysUser.setOrg(BeanUtil.toBean(orgService.getByIdCache(user.getOrgId()), SysOrg.class));
        }

        if (query.getFull() || query.getRoles())
        {
            List<Role> roles = roleService.findRoleByUserId(id);
            sysUser.setRoles(BeanPlusUtil.toBeanList(roles, SysRole.class));
        }
        if (query.getFull() || query.getResource())
        {
            List<Function> functionList =
                    functionService.findVisibleFunction(FunctionQueryDTO.builder().userId(id).build());
            sysUser.setFunctions(CollHelper.split(functionList, Function::getCode, StrPool.SEMICOLON));
        }

        return sysUser;
    }

}
