package com.jettech.jettong.oauth.util;

import javax.crypto.*;
import javax.crypto.spec.DESKeySpec;
import java.security.InvalidKeyException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.security.spec.InvalidKeySpecException;

public class SecurityUtils
{


    private static final String DES = "DES";
    private static final String SHA_1 = "SHA-1";
    private static final String SHA_256 = "SHA-256";
    private static final String MD5 = "MD5";

    public SecurityUtils()
    {
    }

    public static byte[] encrypt(byte[] src, byte[] key)
            throws InvalidKeySpecException, InvalidKeyException, NoSuchAlgorithmException, NoSuchPaddingException,
            IllegalBlockSizeException, BadPaddingException
    {
        if (key.length < 8)
        {
            byte[] sr = new byte[8];
            System.arraycopy(key, 0, sr, 0, key.length);
            key = sr;
        }

        SecureRandom sr1 = new SecureRandom();
        DESKeySpec dks = new DESKeySpec(key);
        SecretKeyFactory keyFactory = SecretKeyFactory.getInstance("DES");
        SecretKey secretKey = keyFactory.generateSecret(dks);
        Cipher cipher = Cipher.getInstance("DES");
        cipher.init(1, secretKey, sr1);
        return cipher.doFinal(src);
    }

    public static byte[] decrypt(byte[] src, byte[] key)
            throws InvalidKeyException, NoSuchAlgorithmException, InvalidKeySpecException, NoSuchPaddingException,
            IllegalBlockSizeException, BadPaddingException
    {
        if (key.length < 8)
        {
            byte[] sr = new byte[8];
            System.arraycopy(key, 0, sr, 0, key.length);
            key = sr;
        }

        SecureRandom sr1 = new SecureRandom();
        DESKeySpec dks = new DESKeySpec(key);
        SecretKeyFactory keyFactory = SecretKeyFactory.getInstance("DES");
        SecretKey secureKey = keyFactory.generateSecret(dks);
        Cipher cipher = Cipher.getInstance("DES");
        cipher.init(2, secureKey, sr1);
        return cipher.doFinal(src);
    }

    public static String decrypt(String data, String key)
    {
        try
        {
            return new String(decrypt(hex2byte(data.getBytes()), key.getBytes()));
        }
        catch (Exception var3)
        {
            throw new RuntimeException(var3);
        }
    }

    public static String encrypt(String data, String key)
    {
        if (null == data)
        {
            return null;
        }
        else
        {
            try
            {
                return byte2hex(encrypt(data.getBytes(), key.getBytes()));
            }
            catch (Exception var3)
            {
                throw new RuntimeException(var3);
            }
        }
    }

    private static String byte2hex(byte[] b)
    {
        StringBuilder builder = new StringBuilder();

        for (int n = 0; null != b && n < b.length; ++n)
        {
            String tmp = Integer.toHexString(b[n] & 255);
            if (tmp.length() == 1)
            {
                builder.append('0');
            }

            builder.append(tmp);
        }

        return builder.toString().toUpperCase();
    }

    private static byte[] hex2byte(byte[] b)
    {
        if (b.length % 2 != 0)
        {
            throw new IllegalArgumentException();
        }
        else
        {
            byte[] b2 = new byte[b.length / 2];

            for (int n = 0; n < b.length; n += 2)
            {
                String item = new String(b, n, 2);
                b2[n / 2] = (byte) Integer.parseInt(item, 16);
            }

            return b2;
        }
    }

    public static String MD5(String str)
    {
        try
        {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] e = md.digest(str.getBytes());
            return byte2hex(e);
        }
        catch (NoSuchAlgorithmException var3)
        {
            throw new RuntimeException(var3);
        }
    }

    public static String SHA1(String str)
    {
        try
        {
            MessageDigest md = MessageDigest.getInstance("SHA-1");
            byte[] e = md.digest(str.getBytes());
            return byte2hex(e);
        }
        catch (NoSuchAlgorithmException var3)
        {
            throw new RuntimeException(var3);
        }
    }

    public static String SHA256(String str)
    {
        try
        {
            MessageDigest md = MessageDigest.getInstance("SHA-256");
            byte[] e = md.digest(str.getBytes());
            return byte2hex(e);
        }
        catch (NoSuchAlgorithmException var3)
        {
            throw new RuntimeException(var3);
        }
    }


    public static void main(String[] args)
    {

        System.out.println(encrypt("12345", "<EMAIL>"));
        System.out.println(decrypt("71377AD094A3E1B3", "<EMAIL>"));

    }

}
