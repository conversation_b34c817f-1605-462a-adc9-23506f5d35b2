package com.jettech.jettong.oauth.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.jettech.basic.base.service.SuperCacheServiceImpl;
import com.jettech.basic.cache.model.CacheKey;
import com.jettech.basic.cache.model.CacheKeyBuilder;
import com.jettech.jettong.base.dto.rbac.role.FunctionQueryDTO;
import com.jettech.jettong.base.entity.rbac.role.Function;
import com.jettech.jettong.common.cache.base.rbac.role.FunctionCacheKeyBuilder;
import com.jettech.jettong.common.cache.base.rbac.user.UserFunctionCacheKeyBuilder;
import com.jettech.jettong.oauth.dao.FunctionMapper;
import com.jettech.jettong.oauth.service.FunctionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 菜单功能业务处理层
 *
 * <AUTHOR>
 * @version 1.0
 * @description 菜单功能业务处理层
 * @projectName jettong
 * @package com.jettech.jettong.oauth.service.impl
 * @className FunctionServiceImpl
 * @date 2021/9/15 9:43
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Slf4j
@Service
@DS("#thread.tenant")
@RequiredArgsConstructor
public class FunctionServiceImpl extends SuperCacheServiceImpl<FunctionMapper, Function> implements FunctionService
{
    @Override
    protected CacheKeyBuilder cacheKeyBuilder()
    {
        return new FunctionCacheKeyBuilder();
    }

    @Override
    public List<Function> findVisibleFunction(FunctionQueryDTO functionQueryDTO)
    {
        CacheKey userResourceKey = new UserFunctionCacheKeyBuilder().key(functionQueryDTO.getUserId());

        List<Function> visibleFunction = new ArrayList<>();
        List<Long> list = cacheOps.get(userResourceKey, k ->
        {
            visibleFunction.addAll(baseMapper.findVisibleResource(functionQueryDTO));
            return visibleFunction.stream().map(Function::getId).collect(Collectors.toList());
        });

        if (!visibleFunction.isEmpty())
        {
            visibleFunction.forEach(this::setCache);
        }
        else
        {
            visibleFunction.addAll(findByIds(list, this::listByIds));
        }
        return resourceListFilter(functionQueryDTO.getMenuId(), visibleFunction);
    }

    private List<Function> resourceListFilter(Long menuId, List<Function> visibleFunction)
    {
        if (menuId == null)
        {
            return visibleFunction;
        }
        return visibleFunction.stream().filter(item -> Objects.equals(menuId, item.getMenuId()))
                .collect(Collectors.toList());
    }

}
