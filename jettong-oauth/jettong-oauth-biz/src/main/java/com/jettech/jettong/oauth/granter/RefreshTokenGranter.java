/*
 * Copyright 2002-2011 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.jettech.jettong.oauth.granter;

import com.jettech.basic.base.R;
import com.jettech.basic.context.ContextConstants;
import com.jettech.basic.database.properties.DatabaseProperties;
import com.jettech.basic.jwt.TokenUtil;
import com.jettech.basic.jwt.model.AuthInfo;
import com.jettech.basic.security.config.EncryptionConfig;
import com.jettech.basic.utils.SpringUtils;
import com.jettech.basic.utils.StrHelper;
import com.jettech.jettong.base.dto.rbac.user.LoginParamDTO;
import com.jettech.jettong.base.dto.rbac.user.Online;
import com.jettech.jettong.base.entity.rbac.user.User;
import com.jettech.jettong.common.properties.SystemProperties;
import com.jettech.jettong.oauth.event.LoginEvent;
import com.jettech.jettong.oauth.event.model.LoginStatusDTO;
import com.jettech.jettong.oauth.service.OnlineService;
import com.jettech.jettong.oauth.service.TenantService;
import com.jettech.jettong.oauth.service.UserService;
import org.springframework.stereotype.Component;

import static com.jettech.jettong.oauth.granter.RefreshTokenGranter.GRANT_TYPE;

/**
 * RefreshTokenGranter
 *
 * <AUTHOR> Syer
 * <AUTHOR>
 * @date 2020年03月31日10:23:53
 */
@Component(GRANT_TYPE)
public class RefreshTokenGranter extends AbstractTokenGranter implements TokenGranter
{

    public static final String GRANT_TYPE = "refresh_token";

    public RefreshTokenGranter(TokenUtil tokenUtil, UserService userService, TenantService tenantService,
                               DatabaseProperties databaseProperties,
                               OnlineService onlineService, SystemProperties systemProperties, EncryptionConfig encryptionConfig)
    {
        super(tokenUtil, userService, tenantService,
                databaseProperties, onlineService, systemProperties, encryptionConfig);
    }

    @Override
    public R<AuthInfo> grant(LoginParamDTO loginParam)
    {
        String grantType = loginParam.getGrantType();
        String refreshTokenStr = loginParam.getRefreshToken();
        if (StrHelper.isAnyBlank(grantType, refreshTokenStr) || !GRANT_TYPE.equals(grantType))
        {
            return R.fail("加载用户信息失败");
        }

        AuthInfo authInfo = tokenUtil.parseRefreshToken(refreshTokenStr);

        if (!ContextConstants.REFRESH_TOKEN_KEY.equals(authInfo.getTokenType()))
        {
            return R.fail("refreshToken无效，无法加载用户信息");
        }

        User user = userService.getByIdCache(authInfo.getUserId());

        if (user == null || !user.getState())
        {
            String msg = "您已被禁用！";
            SpringUtils.publishEvent(new LoginEvent(LoginStatusDTO.fail(authInfo.getUserId(), msg)));
            return R.fail(msg);
        }

        AuthInfo newAuth = createToken(user);
        Online online = getOnline(newAuth);

        //成功登录事件
        LoginStatusDTO.success(user.getId(), online);
        onlineService.save(online);
        return R.success(newAuth);

    }
}
