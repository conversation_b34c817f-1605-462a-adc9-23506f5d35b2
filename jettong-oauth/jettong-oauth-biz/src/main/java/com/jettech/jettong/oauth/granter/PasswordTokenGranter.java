/*
 * Copyright 2002-2011 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.jettech.jettong.oauth.granter;

import com.jettech.basic.base.R;
import com.jettech.basic.database.properties.DatabaseProperties;
import com.jettech.basic.jwt.TokenUtil;
import com.jettech.basic.jwt.model.AuthInfo;
import com.jettech.basic.security.config.EncryptionConfig;
import com.jettech.jettong.base.dto.rbac.user.LoginParamDTO;
import com.jettech.jettong.common.properties.SystemProperties;
import com.jettech.jettong.oauth.service.OnlineService;
import com.jettech.jettong.oauth.service.TenantService;
import com.jettech.jettong.oauth.service.UserService;
import org.springframework.stereotype.Component;

import static com.jettech.jettong.oauth.granter.PasswordTokenGranter.GRANT_TYPE;

/**
 * 账号密码登录获取token
 *
 * <AUTHOR> Syer
 * <AUTHOR>
 * @date 2020年03月31日10:22:55
 */
@Component(GRANT_TYPE)
public class PasswordTokenGranter extends AbstractTokenGranter implements TokenGranter
{

    public static final String GRANT_TYPE = "password";

    public PasswordTokenGranter(TokenUtil tokenUtil, UserService userService, TenantService tenantService,
                                DatabaseProperties databaseProperties,
                                OnlineService onlineService, SystemProperties systemProperties, EncryptionConfig encryptionConfig)
    {
        super(tokenUtil, userService, tenantService, databaseProperties, onlineService,
                systemProperties, encryptionConfig);
    }

    @Override
    public R<AuthInfo> grant(LoginParamDTO tokenParameter)
    {
        return login(tokenParameter);
    }
}
