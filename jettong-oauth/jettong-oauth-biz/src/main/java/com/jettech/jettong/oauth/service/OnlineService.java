package com.jettech.jettong.oauth.service;

import com.jettech.jettong.base.dto.rbac.user.Online;

import java.util.List;

/**
 * 在线用户业务处理层接口
 *
 * <AUTHOR>
 * @version 1.0
 * @description 在线用户业务处理层接口
 * @projectName jettong
 * @package com.jettech.jettong.oauth.service
 * @className OnlineService
 * @date 2021/9/15 9:43
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
public interface OnlineService
{
    /**
     * 重置用户登录
     *
     * @return 是否成功
     */
    boolean reset();

    /**
     * 查询在线用户
     *
     * @param name 姓名
     * @return
     */
    List<Online> list(String name);

    /**
     * 保存在线用户
     *
     * @param model
     * @return
     */
    boolean save(Online model);

    /**
     * 清理在线用户
     *
     * @param token
     * @param userId
     * @param clientId
     * @return
     */
    boolean clear(String token, Long userId, String clientId);
}
