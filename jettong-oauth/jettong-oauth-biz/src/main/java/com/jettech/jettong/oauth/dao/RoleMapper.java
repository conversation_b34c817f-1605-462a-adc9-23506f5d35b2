package com.jettech.jettong.oauth.dao;

import com.jettech.basic.base.mapper.SuperMapper;
import com.jettech.jettong.base.entity.rbac.role.Role;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 角色持久化层接口
 *
 * <AUTHOR>
 * @version 1.0
 * @description 角色持久化层接口
 * @projectName jettong
 * @package com.jettech.jettong.oauth.dao
 * @className RoleMapper
 * @date 2021/9/15 9:43
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Repository
public interface RoleMapper extends SuperMapper<Role>
{

    /**
     * 查询用户拥有的角色
     *
     * @param userId 用户id
     * @return {@link List< Role>} 角色信息
     * <AUTHOR>
     * @date 2022/6/6 10:22
     * @update 2022/6/6 10:22
     * @since 1.0
     */
    List<Role> findRoleByUserId(@Param("userId") Long userId);
}
