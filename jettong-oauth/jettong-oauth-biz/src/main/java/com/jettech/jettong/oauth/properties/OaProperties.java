package com.jettech.jettong.oauth.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * 企业微信配置
 * <AUTHOR>
 * @version 1.0
 * @description 企业微信配置
 * @projectName jettong
 * @package com.jettech.jettong.oauth.properties
 * @className WxProperties
 * @date 2024/7/29 11:14
 * @copyright @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于沃尔沃内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@ConfigurationProperties(OaProperties.PREFIX)
public class OaProperties
{
    public static final String PREFIX = "jettong.oa";
    /**
     * OA认证请求根路径
     */
    private String url;

    /**
     * 加/解密密匙
     */
    private String key;

    /**
     * 加密密钥
     */
    private String secret;

    /**
     * 平台在OA系统中的唯一标识
     */
    private String appId;

    /**
     * 调用接口的客户端的ip地址
     */
    private String ipAdress;

}
