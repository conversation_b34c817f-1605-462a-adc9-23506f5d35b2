package com.jettech.jettong.oauth.service;

import com.jettech.jettong.base.entity.rbac.user.User;

/**
 * OA验证相关接口
 * <AUTHOR>
 * @version 1.0
 * @description OA验证相关接口
 * @projectName HBSC
 * @package com.jettech.jettong.oauth.service
 * @className WxValidateService
 * @date 2024/7/29 11:33
 * @copyright @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于沃尔沃内部传阅，禁止外泄以及用于其他的商业目的
 */
public interface OaValidateService
{
    /**
     * 验证OA系统Token
     * @param oaToken OA系统返回token
     * @param idCard 工号
     * @return User 合法则返回用户信息
     * <AUTHOR>
     * @date 2024/7/29 11:36
     * @update 2024/7/29 11:36
     * @since 1.0
     */
    User checkToken(String oaToken, String idCard);

}
