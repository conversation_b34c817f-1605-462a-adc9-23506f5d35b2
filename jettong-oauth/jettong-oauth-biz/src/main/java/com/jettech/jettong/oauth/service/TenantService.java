package com.jettech.jettong.oauth.service;

import com.jettech.basic.base.service.SuperCacheService;
import com.jettech.jettong.base.entity.rbac.tenant.Tenant;

/**
 * 租户业务处理层接口
 *
 * <AUTHOR>
 * @version 1.0
 * @description 租户业务处理层接口
 * @projectName jettong
 * @package com.jettech.jettong.oauth.service
 * @className TenantService
 * @date 2021/9/15 9:43
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
public interface TenantService extends SuperCacheService<Tenant>
{

    /**
     * 根据编码获取租户信息
     *
     * @param code 租户编码
     * @return Tenant 租户信息
     * <AUTHOR>
     * @date 2021/11/5 15:49
     * @update zxy 2021/11/5 15:49
     * @since 1.0
     */
    Tenant getByCode(String code);

}
