package com.jettech.jettong.oauth.dao;

import com.jettech.basic.base.mapper.SuperMapper;
import com.jettech.jettong.base.entity.rbac.role.Menu;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 菜单持久化层接口
 *
 * <AUTHOR>
 * @version 1.0
 * @description 菜单持久化层接口
 * @projectName jettong
 * @package com.jettech.jettong.oauth.dao
 * @className MenuMapper
 * @date 2021/9/15 9:43
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Repository
public interface MenuMapper extends SuperMapper<Menu>
{


    /**
     * 根据角色id查询菜单功能信息
     *
     * @param roleIds 角色id
     * @return {@link List< Menu>} 菜单功能信息
     * <AUTHOR>
     * @date 2022/6/6 15:24
     * @update 2022/6/6 15:24
     * @since 1.0
     */
    List<Menu> findVisibleMenu(@Param("roleIds") List<Long> roleIds);

}
