<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jettech.jettong.oauth.dao.MenuMapper">

    <select id="findVisibleMenu" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        from sys_menu where `state` = true
        and `id` in (
        SELECT distinct `authority_id` FROM sys_role_authority ra
        INNER JOIN sys_user_role ur on ra.`role_id` = ur.`role_id`
        INNER JOIN sys_role r on r.`id` = ra.`role_id`
        where ur.`role_id` in
        <foreach close=")" collection="roleIds" item="roleId" open="(" separator=",">
            #{roleId}
        </foreach>
        and r.`state` = true) ORDER BY `sort` asc
    </select>

</mapper>
