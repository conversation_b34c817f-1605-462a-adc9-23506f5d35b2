<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jettech.jettong.oauth.dao.FunctionMapper">

    <select id="findVisibleResource" parameterType="map" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from `sys_function`
        where exists (
        select
        sm.`id`
        from
        `sys_menu` sm
        inner join `sys_role_authority` sua on sm.`id` = sua.`authority_id`
        inner join `sys_user_role` sur on sur.`role_id` = sua.`role_id`
        where
        sm.`is_button` = true
        and
        sur.`user_id` = #{userId, jdbcType=BIGINT}
        and sm.`id` = `menu_id`
        )
    </select>

</mapper>
