<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jettech.jettong.oauth.dao.UserMapper">

    <update id="incrPasswordErrorNumById">
        update sys_user
        set `password_error_num`       = `password_error_num` + 1,
            `password_error_last_time` = SYSDATE()
        where `id` = #{id, jdbcType=BIGINT}
    </update>
    <update id="resetPassErrorNum">
        update sys_user
        set `password_error_num`       = 0,
            `password_error_last_time` = #{now, jdbcType=TIMESTAMP}
        where `id` = #{id, jdbcType=BIGINT}
    </update>

</mapper>
