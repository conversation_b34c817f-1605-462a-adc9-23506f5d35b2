<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jettech.jettong.oauth.dao.UserMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jettech.jettong.base.entity.rbac.user.User">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="created_by" jdbcType="BIGINT" property="createdBy"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="updated_by" jdbcType="BIGINT" property="updatedBy"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="account" jdbcType="VARCHAR" property="account"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="org_id" jdbcType="BIGINT" property="orgId"/>
        <result column="readonly" jdbcType="BIT" property="readonly"/>
        <result column="id_card" jdbcType="VARCHAR" property="idCard"/>
        <result column="email" jdbcType="VARCHAR" property="email"/>
        <result column="mobile" jdbcType="VARCHAR" property="mobile"/>
        <result column="state" jdbcType="BIT" property="state"/>
        <result column="avatar_type" jdbcType="BIT" property="avatarType"/>
        <result column="avatar" jdbcType="BIGINT" property="avatar"/>
        <result column="avatarPath" jdbcType="VARCHAR" property="avatarPath"/>
        <result column="description" jdbcType="VARCHAR" property="description"/>
        <result column="password_error_last_time" jdbcType="TIMESTAMP" property="passwordErrorLastTime"/>
        <result column="password_error_num" jdbcType="INTEGER" property="passwordErrorNum"/>
        <result column="password" jdbcType="VARCHAR" property="password"/>
        <result column="salt" jdbcType="VARCHAR" property="salt"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        `id`
        ,`created_by`,`create_time`,`updated_by`,`update_time`,
        `account`, `name`, `org_id`, `readonly`, `id_card`, `email`, `mobile`, `state`, `avatar_type`, `avatar`, `description`, `password_error_last_time`, `password_error_num`, `password`, `salt`
    </sql>

</mapper>
