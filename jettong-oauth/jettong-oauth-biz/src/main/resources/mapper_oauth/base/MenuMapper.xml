<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jettech.jettong.oauth.dao.MenuMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jettech.jettong.base.entity.rbac.role.Menu">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="code" jdbcType="VARCHAR" property="code"/>
        <result column="sort" jdbcType="INTEGER" property="sort"/>
        <result column="parent_id" jdbcType="BIGINT" property="parentId"/>
        <result column="created_by" jdbcType="BIGINT" property="createdBy"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="updated_by" jdbcType="BIGINT" property="updatedBy"/>
        <result column="platform" jdbcType="VARCHAR" property="platform"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="description" jdbcType="VARCHAR" property="description"/>
        <result column="is_general" jdbcType="BIT" property="isGeneral"/>
        <result column="is_hide" jdbcType="BIT" property="isHide"/>
        <result column="show_children" jdbcType="BIT" property="showChildren"/>
        <result column="is_button" jdbcType="BIT" property="isButton"/>
        <result column="path" jdbcType="VARCHAR" property="path"/>
        <result column="state" jdbcType="BIT" property="state"/>
        <result column="icon" jdbcType="VARCHAR" property="icon"/>
        <result column="component" jdbcType="VARCHAR" property="component"/>
        <result column="readonly" jdbcType="BIT" property="readonly"/>
        <result column="is_external" jdbcType="BIT" property="isExternal"/>
        <result column="external_url" jdbcType="VARCHAR" property="externalUrl"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        `id`
        ,
        `code`,`name`,`sort`,`parent_id`,`created_by`,`create_time`,`updated_by`,`update_time`,`platform`,
        `description`, `is_general`, `is_hide`, `show_children`, `is_button`, `path`, `state`, `icon`, `component`, `readonly`,`is_external`, `external_url`
    </sql>

</mapper>
