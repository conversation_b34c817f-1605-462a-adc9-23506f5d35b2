package com.jettech.jettong.common.event.enumeration;

import cn.hutool.core.convert.Convert;
import com.jettech.basic.base.BaseEnum;

import java.util.Arrays;
import java.util.Optional;

/**
 * 消息通知人类型
 *
 * <AUTHOR>
 * @version 1.0
 * @description 消息通知人类型
 * @projectName jettong-base-cloud
 * @package com.jettech.jettong.common.event.enumeration
 * @className MsgReceiveType
 * @date 2023/5/25 16:40
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
public enum MsgReceiveType implements BaseEnum {
    createdBy("创建人"),
    updatedBy("修改人"),

    putBy("提出人"),
    leadingBy("责任人"),
    handleBy("处理人"),

    maintainer("维护人"),

    // 工作项评论
    COMMENTED("评论人"),
    REPLIED("回复人"),
    MENTIONED("被@人"),
    ;

    private final String desc;

    MsgReceiveType(String desc) {
        this.desc = desc;
    }


    public static MsgReceiveType getEnum(String value) {
        return Arrays.stream(MsgReceiveType.values())
                .filter(msgReceiveType -> msgReceiveType.name().equalsIgnoreCase(value))
                .findFirst()
                .orElse(getMenuByIndex(value));
    }

    private static MsgReceiveType getMenuByIndex(String index) {
        return Optional.ofNullable(index)
                .map(Convert::toInt)
                .map(MsgReceiveType::getMenuByIndex)
                .orElse(null);
    }

    public static MsgReceiveType getMenuByIndex(int index) {
        MsgReceiveType[] values = MsgReceiveType.values();
        if (index < 0 || index >= values.length) {
            return null;
        }
        return values[index];
    }

    @Override
    public String getDesc() {
        return desc;
    }

    @Override
    public String toString() {
        return super.toString();
    }
}
