package com.jettech.jettong.common.constant;

/**
 * 仅仅用于记录 RemoteField 注解相关的 接口和方法名称
 * <p>
 * 切记，该类下的接口和方法，一定要自己手动创建，否则会注入失败
 *
 * <AUTHOR>
 * @date 2020年01月20日11:16:37
 */
public interface BaseEchoConstants
{

    /**
     * 数据字典项 service查询类
     */
    String DICTIONARY_ITEM_CLASS = "dictionaryServiceImpl";

    /**
     * 数据字典项 feign查询类 全类名
     */
    String DICTIONARY_ITEM_FEIGN_CLASS = "com.jettech.jettong.base.api.DictionaryApi";

    /**
     * 组织 service查询类
     */
    String ORG_ID_CLASS = "orgServiceImpl";

    /**
     * 组织 feign查询类
     */
    String ORG_ID_FEIGN_CLASS = "com.jettech.jettong.base.api.OrgApi";

    /**
     * 用户 service查询类
     */
    String USER_ID_CLASS = "userServiceImpl";

    /**
     * 团队查询service类
     */
    String TEAM_SERVICE_ID_CLASS = "sysTeamServiceImpl";

    /**
     * 用户 feign查询类
     */
    String USER_ID_FEIGN_CLASS = "com.jettech.jettong.base.api.UserApi";

    /**
     * 角色 service查询类
     */
    String ROLE_ID_CLASS = "roleServiceImpl";

    /**
     * webhook service查询类
     */
    String WEBHOOK_ID_CLASS = "webhookServiceImpl";

    /**
     * 附件 feign查询类
     */
    String FILE_ID_FEIGN_CLASS = "com.jettech.jettong.base.api.FileApi";

    /**
     * 附件查询类
     */
    String FILE_ID_CLASS = "fileServiceImpl";

}
