package com.jettech.jettong.common.dto;

import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.jettech.basic.database.mybatis.conditions.query.LbqWrapper;
import com.jettech.basic.database.mybatis.conditions.query.QueryWrap;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 区间范围查询条件，时间、数值
 *
 * <AUTHOR>
 * @version 1.0
 * @description 时间段范围查询条件
 * @projectName jettong-tm
 * @package com.jettech.jettong.alm.issue.dto
 * @className PeriodQuery
 * @date 2022/12/1 上午9:42
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PeriodQuery<T> {
    private T start;
    private T end;

    public static <T> PeriodQuery<T> of(T start, T end) {
        return new PeriodQuery<>(start, end);
    }

    public static <E, T> void appendQuery(LbqWrapper<E> wrapper, PeriodQuery<T> query, SFunction<E, ?> func) {

        if (query == null || wrapper == null) {
            return;
        }
        query.appendQuery(wrapper, func);
    }

    public <E> void appendQuery(LbqWrapper<E> wrapper, SFunction<E, ?> func) {
        if (this.getStart() != null && this.getEnd() != null) {
            wrapper.between(func, getStart(), getEnd());
        } else {
            wrapper.ge(func, this.getStart());
            wrapper.le(func, this.getEnd());
        }
    }

    public <E> void appendQuery(QueryWrap<E> wrapper, String field) {
        if (this.getStart() != null && this.getEnd() != null) {
            wrapper.between(field, this.getStart(), this.getEnd());
        } else {
            wrapper.ge(field, this.getStart());
            wrapper.le(field, this.getEnd());
        }
    }
}
