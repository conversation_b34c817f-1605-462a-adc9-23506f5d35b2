package com.jettech.jettong.common.event;

import com.jettech.jettong.common.event.enumeration.MsgEventType;
import com.jettech.jettong.common.event.enumeration.MsgReceiveType;

import java.util.List;
import java.util.Map;
import java.util.function.Function;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @projectName jettong-base-cloud
 * @package com.jettech.jettong.common.event
 * @className MsgEventFactory
 * @date 2023/6/2 18:02
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
public interface MsgEventFactory<E> {
    /**
     * 处理的消息事件类型
     */
    MsgEventType getType();

    /**
     * 消息接收人类型，及接收人id获取方式
     */
    Map<MsgReceiveType, Function<E, List<Long>>> getReceiveFunctionMap();

    /**
     * 创建消息事件
     * @param oldData
     * @param newData
     * @return
     */
    BizMsgEvent<E> createEvent(E oldData, E newData);

}
