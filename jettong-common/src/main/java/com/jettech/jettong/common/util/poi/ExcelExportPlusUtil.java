package com.jettech.jettong.common.util.poi;


import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.params.ExcelExportEntity;
import com.jettech.basic.uuid.UidGeneratorUtil;
import org.apache.poi.hssf.usermodel.DVConstraint;
import org.apache.poi.hssf.usermodel.HSSFDataValidation;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddressList;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 增强EasyPoi的ExcelExportUtil工具类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 增强EasyPoi的ExcelExportUtil工具类
 * @projectName jettong-enterprises
 * @package com.jettech.jettong.common.util.poi
 * @className ExcelExportPlusUtil
 * @date 2021/12/1 15:10
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
public class ExcelExportPlusUtil
{
    /**
     * 导出Ecel
     *
     * @return org.apache.poi.ss.usermodel.Workbook
     */
    public static Workbook exportExcel(List<Map<String, Object>> list)
    {
        Workbook workbook = new XSSFWorkbook();
        for (Map<String, Object> map : list)
        {
            ExcelExportPlusService service = new ExcelExportPlusService();
            service.createSheetWithList(workbook, (ExportParams) map.get("exportParams"), ExportParams.class,
                    (List<ExcelExportEntity>) map.get("entityList"), (Collection<?>) map.get("data"));
        }
        return workbook;
    }

    /**
     * excel下拉选项(不限制菜单可选项个数)
     *
     * @param workbook workbook
     * @param tarSheet 目标单元格所在的sheet
     * @param menuItems 下拉菜单可选项数组
     * @param firstRow 第一个目标单元格所在的行号(从0开始)
     * @param lastRow 最后一个目标单元格所在的行(从0开始)
     * @param column 待添加下拉菜单的单元格所在的列(从0开始)
     * <AUTHOR>
     * @date 2021/12/1 20:07
     * @update zxy 2021/12/1 20:07
     * @since 1.0
     */
    public static void addDropDownList(Workbook workbook, Sheet tarSheet, String[] menuItems, int firstRow,
            int lastRow, int column)
    {
        if (null == workbook)
        {
            return;
        }
        if (null == tarSheet)
        {
            return;
        }

        //必须以字母开头，最长为31位
        String hiddenSheetName = "a" + UidGeneratorUtil.getId();
        //excel中的"名称"，用于标记隐藏sheet中的用作菜单下拉项的所有单元格
        String formulaId = "form" + UidGeneratorUtil.getId();
        //用于存储 下拉菜单数据
        Sheet hiddenSheet = workbook.createSheet(hiddenSheetName);

        //存储下拉菜单项的sheet页不显示
        workbook.setSheetHidden(workbook.getSheetIndex(hiddenSheet), true);

        //隐藏sheet中添加菜单数据
        for (int i = 0; i < menuItems.length; i++)
        {
            Row row = hiddenSheet.createRow(i);
            //隐藏表的数据列必须和添加下拉菜单的列序号相同，否则不能显示下拉菜单
            Cell cell = row.createCell(column);
            cell.setCellValue(menuItems[i]);
        }
        //创建"名称"标签，用于链接
        Name namedCell = workbook.createName();
        namedCell.setNameName(formulaId);
        namedCell.setRefersToFormula(hiddenSheetName + "!A$1:A$" + menuItems.length);

        CellRangeAddressList addressList = new CellRangeAddressList(firstRow, lastRow, column, column);

        DataValidation validation;
        if (tarSheet instanceof XSSFSheet || tarSheet instanceof SXSSFSheet)
        {
            DataValidationHelper dvHelper = tarSheet.getDataValidationHelper();
            DataValidationConstraint constraint = dvHelper.createFormulaListConstraint(formulaId);
            validation = dvHelper.createValidation(constraint, addressList);
        }
        else
        {
            DataValidationConstraint constraint = DVConstraint.createFormulaListConstraint(formulaId);
            validation = new HSSFDataValidation(addressList, constraint);
        }
        if (validation instanceof HSSFDataValidation)
        {
            validation.setSuppressDropDownArrow(false);
        }
        else
        {
            validation.setSuppressDropDownArrow(true);
            validation.setShowErrorBox(true);
        }
        tarSheet.addValidationData(validation);
    }

}
