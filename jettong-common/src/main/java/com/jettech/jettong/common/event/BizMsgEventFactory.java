package com.jettech.jettong.common.event;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import com.jettech.jettong.common.event.enumeration.MsgEventType;
import com.jettech.jettong.common.event.enumeration.MsgReceiveType;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;

/**
 * 通用业务消息事件
 * 使用 MsgEventBuilder 进行操作
 *
 * <AUTHOR>
 * @version 1.0
 * @description 通用业务消息事件
 * @projectName jettong-base-cloud
 * @package com.jettech.jettong.common.event
 * @className BizMsgEvent
 * @date 2023/5/24 15:21
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
public class BizMsgEventFactory extends AbstractMsgEventFactory<Map<String, Object>> {

    public BizMsgEventFactory(MsgEventType type) {
        super(type);
    }

    @Override
    public Map<MsgReceiveType, Function<Map<String, Object>, List<Long>>> getReceiveFunctionMap() {
        Map<MsgReceiveType, Function<Map<String, Object>, List<Long>>> map = new HashMap<>();
        map.put(MsgReceiveType.createdBy, created());
        map.put(MsgReceiveType.updatedBy, updated());
        return map;
    }

    /**
     * 创建人
     */
    protected static Function<Map<String, Object>, List<Long>> created() {
        return map -> {
            Object createdBy = map.get("createdBy");
            createdBy = createdBy == null ? map.get("created_by") : createdBy;
            if (createdBy == null) {
                return new ArrayList<>();
            }
            Long convert = Convert.convert(Long.class, createdBy);
            return ListUtil.of(convert);
        };
    }

    /**
     * 更新人
     */
    protected static Function<Map<String, Object>, List<Long>> updated() {
        return map -> {
            Object updatedBy = map.get("updatedBy");
            updatedBy = updatedBy == null ? map.get("updated_by") : updatedBy;
            if (updatedBy == null) {
                return new ArrayList<>();
            }
            Long convert = Convert.convert(Long.class, updatedBy);
            return ListUtil.of(convert);
        };
    }

}
