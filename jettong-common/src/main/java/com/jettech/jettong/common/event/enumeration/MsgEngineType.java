package com.jettech.jettong.common.event.enumeration;

import com.jettech.basic.base.BaseEnum;

import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @projectName jettong-base-cloud
 * @package com.jettech.jettong.base.entity.msg.engine
 * @className MsgEngineType
 * @date 2023/5/16 17:44
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
public enum MsgEngineType implements BaseEnum {
    LETTER("站内信"),
    MAIL("邮件"),
    DING_DING("钉钉"),
    FEI_SHU("飞书"),
    ENTERPRISE_WE_CHAT("企业微信"),
    ;

    private final String desc;

    MsgEngineType(String desc) {
        this.desc = desc;
    }

    /**
     * 根据当前枚举的name匹配
     */
    public static MsgEngineType match(String val, MsgEngineType def)
    {
        return Stream.of(values()).filter(item -> item.name().equalsIgnoreCase(val)).findAny().orElse(def);
    }

    public static MsgEngineType get(String val)
    {
        return match(val, null);
    }

    @Override
    public String getDesc() {
        return desc;
    }
}
