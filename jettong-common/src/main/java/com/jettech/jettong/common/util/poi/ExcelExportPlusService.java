package com.jettech.jettong.common.util.poi;

import cn.afterturn.easypoi.excel.annotation.ExcelTarget;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.afterturn.easypoi.excel.entity.params.ExcelExportEntity;
import cn.afterturn.easypoi.excel.export.ExcelExportService;
import cn.afterturn.easypoi.exception.excel.ExcelExportException;
import cn.afterturn.easypoi.exception.excel.enums.ExcelExportEnum;
import cn.afterturn.easypoi.util.PoiPublicUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;

import java.lang.reflect.Field;
import java.util.Collection;
import java.util.List;

/**
 * 增强EasyPoi的ExcelExportService，使其支持List<ExcelExportEntity>
 *
 * <AUTHOR>
 * @version 1.0
 * @description 增强EasyPoi的ExcelExportService，使其支持List<ExcelExportEntity>
 * @projectName jettong-enterprises
 * @package com.jettech.jettong.common.util.poi
 * @className ExcelExportPlusService
 * @date 2021/12/1 15:15
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Slf4j
public class ExcelExportPlusService extends ExcelExportService
{
    public void createSheetWithList(Workbook workbook, ExportParams entity, Class<?> pojoClass,
            List<ExcelExportEntity> entityList, Collection<?> dataSet)
    {
        if (log.isDebugEnabled())
        {
            log.debug("Excel export start ,class is {}", pojoClass);
            log.debug("Excel version is {}",
                    entity.getType().equals(ExcelType.HSSF) ? "03" : "07");
        }
        if (workbook == null || entity == null || pojoClass == null || dataSet == null)
        {
            throw new ExcelExportException(ExcelExportEnum.PARAMETER_ERROR);
        }
        try
        {
            List<ExcelExportEntity> excelParams = entityList;
            // 得到所有字段
            Field[] fileds = PoiPublicUtil.getClassFields(pojoClass);
            ExcelTarget etarget = pojoClass.getAnnotation(ExcelTarget.class);
            String targetId = etarget == null ? null : etarget.value();
            getAllExcelField(entity.getExclusions(), targetId, fileds, excelParams, pojoClass,
                    null, null);
            //获取所有参数后,后面的逻辑判断就一致了
            createSheetForMap(workbook, entity, excelParams, dataSet);
        }
        catch (Exception e)
        {
            log.error(e.getMessage(), e);
            throw new ExcelExportException(ExcelExportEnum.EXPORT_ERROR, e.getCause());
        }
    }
}
