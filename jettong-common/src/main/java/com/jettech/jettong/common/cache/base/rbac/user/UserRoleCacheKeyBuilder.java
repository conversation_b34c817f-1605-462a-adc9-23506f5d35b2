package com.jettech.jettong.common.cache.base.rbac.user;

import com.jettech.basic.cache.model.CacheKeyBuilder;
import com.jettech.jettong.common.cache.BaseCacheKeyDefinition;

import java.time.Duration;

/**
 * 用户角色缓存key生成器
 *
 * <AUTHOR>
 * @version 1.0
 * @description 用户角色缓存key生成器
 * @projectName jettong
 * @package com.jettech.jettong.common.cache.base.rbac.user
 * @className UserRoleCacheKeyBuilder
 * @date 2021/9/15 9:43
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
public class UserRoleCacheKeyBuilder implements CacheKeyBuilder
{
    @Override
    public String getPrefix()
    {
        return BaseCacheKeyDefinition.BASE_RBAC_USER_ROLE;
    }

    @Override
    public Duration getExpire()
    {
        return Duration.ofHours(8);
    }
}
