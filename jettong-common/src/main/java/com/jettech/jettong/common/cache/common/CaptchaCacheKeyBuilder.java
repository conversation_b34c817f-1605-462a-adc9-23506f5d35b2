package com.jettech.jettong.common.cache.common;


import com.jettech.basic.cache.model.CacheKeyBuilder;
import com.jettech.jettong.common.cache.BaseCacheKeyDefinition;

import java.time.Duration;

/**
 * 登录验证码缓存Key生成器
 *
 * <AUTHOR>
 * @version 1.0
 * @description 登录验证码缓存Key生成器
 * @projectName jettong
 * @package com.jettech.jettong.cmdb.cache.common
 * @className CaptchaCacheKeyBuilder
 * @date 2021/9/15 10:01
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
public class CaptchaCacheKeyBuilder implements CacheKeyBuilder
{
    @Override
    public String getPrefix()
    {
        return BaseCacheKeyDefinition.OAUTH_LOGIN_CAPTCHA;
    }

    @Override
    public String getTenant()
    {
        return null;
    }

    @Override
    public Duration getExpire()
    {
        return Duration.ofMinutes(15);
    }
}
