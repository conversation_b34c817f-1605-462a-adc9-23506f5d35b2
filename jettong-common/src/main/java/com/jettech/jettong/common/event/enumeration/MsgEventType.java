package com.jettech.jettong.common.event.enumeration;

import com.jettech.basic.base.BaseEnum;
import io.swagger.annotations.ApiModelProperty;

import static com.jettech.jettong.common.event.enumeration.MsgReceiveType.*;

/**
 * 消息事件类型-枚举
 *
 * <AUTHOR>
 * @version 1.0
 * @description 消息事件类型
 * @projectName jettong-base-cloud
 * @package com.jettech.jettong.base.enumeration.msg
 * @className MsgEventType
 * @date 2023/5/22 18:53
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
public enum MsgEventType implements BaseEnum {

    NOTICE("通知信息"),

    /**
     * PROJECT="项目"
     */
    PROJECT("项目", createdBy, updatedBy),
    /**
     * PIPELINE="流水线"
     */
    PIPELINE("流水线", createdBy, updatedBy, maintainer),
    /**
     * 代码库
     */
    CODE("代码库", createdBy, updatedBy, maintainer),

    ISSUE_ADVENT("工作项临期", createdBy, updatedBy, handleBy, putBy, leadingBy),
    ISSUE_DELAY("工作项延期", createdBy, updatedBy, handleBy, putBy, leadingBy),

    ISSUE_DETECTION("工作项评论", COMMENTED, REPLIED, MENTIONED),

    IDEA_UPDATE("意向修改", createdBy, updatedBy, handleBy, putBy, leadingBy),
    IDEA_CREATE("意向创建", createdBy, updatedBy, handleBy, putBy, leadingBy),
    IDEA_DELETE("意向删除", createdBy, updatedBy, handleBy, putBy, leadingBy),
    IDEA_TRANSITION("意向状态流转", createdBy, updatedBy, handleBy, putBy, leadingBy),

    ISSUE_CREATE("需求创建", createdBy, updatedBy, handleBy, putBy, leadingBy),
    ISSUE_UPDATE("需求修改", createdBy, updatedBy, handleBy, putBy, leadingBy),
    ISSUE_DELETE("需求删除", createdBy, updatedBy, handleBy, putBy, leadingBy),
    ISSUE_TRANSITION("需求状态流转", createdBy, updatedBy, handleBy, putBy, leadingBy),

    BUG_CREATE("缺陷创建", createdBy, updatedBy, handleBy, putBy, leadingBy),
    BUG_UPDATE("缺陷修改", createdBy, updatedBy, handleBy, putBy, leadingBy),
    BUG_DELETE("缺陷删除", createdBy, updatedBy, handleBy, putBy, leadingBy),
    BUG_TRANSITION("缺陷状态流转", createdBy, updatedBy, handleBy, putBy, leadingBy),

    TASK_CREATE("任务创建", createdBy, updatedBy, handleBy, putBy, leadingBy),
    TASK_UPDATE("任务修改", createdBy, updatedBy, handleBy, putBy, leadingBy),
    TASK_DELETE("任务删除", createdBy, updatedBy, handleBy, putBy, leadingBy),
    TASK_TRANSITION("任务状态流转", createdBy, updatedBy, handleBy, putBy, leadingBy),

    RISK_CREATE("风险创建", createdBy, updatedBy, handleBy, putBy, leadingBy),
    RISK_UPDATE("风险修改", createdBy, updatedBy, handleBy, putBy, leadingBy),
    RISK_DELETE("风险删除", createdBy, updatedBy, handleBy, putBy, leadingBy),
    RISK_TRANSITION("风险状态流转", createdBy, updatedBy, handleBy, putBy, leadingBy),

    ;

    private final String desc;
    @ApiModelProperty(value = "接收人类型")
    private final MsgReceiveType[] receiveTypes;

    MsgEventType(String desc, MsgReceiveType... receiveTypes) {
        this.desc = desc;
        this.receiveTypes = receiveTypes;
    }

    @Override
    public String getDesc() {
        return desc;
    }

    @ApiModelProperty(value = "接收人类型")
    public MsgReceiveType[] getReceiveTypes() {
        return receiveTypes;
    }
}
