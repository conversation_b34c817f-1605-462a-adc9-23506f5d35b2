package com.jettech.jettong.common.enumeration;

import com.jettech.basic.base.BaseEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.stream.Stream;


/**
 * LINUX:Linux;WINDOWS:Windows;OTHER:其它
 *
 * <AUTHOR>
 * @version 1.0
 * @description LINUX:Linux;WINDOWS:Windows;OTHER:其它
 * @projectName jettong
 * @package com.jettech.jettong.cmdb.application.enumeration
 * @className OperatingSystemType
 * @date 2021-10-22
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "OperatingSystemType", description = "操作系统枚举。LINUX:Linux;WINDOWS:Windows;OTHER:其它")
public enum OperatingSystemType implements BaseEnum
{

    /**
     * LINUX="Linux"
     */
    LINUX("Linux"),
    /**
     * WINDOWS="Windows"
     */
    WINDOWS("Windows"),
    /**
     * OTHER="其它"
     */
    OTHER("其它"),
    ;

    @ApiModelProperty(value = "描述")
    private String desc;


    /**
     * 根据当前枚举的name匹配
     */
    public static OperatingSystemType match(String val, OperatingSystemType def)
    {
        return Stream.of(values()).parallel().filter(item -> item.name().equalsIgnoreCase(val)).findAny().orElse(def);
    }

    public static OperatingSystemType get(String val)
    {
        return match(val, null);
    }

    public boolean eq(OperatingSystemType val)
    {
        return val != null && eq(val.name());
    }

    @Override
    @ApiModelProperty(value = "操作系统类型编码", allowableValues = "LINUX,WINDOWS,OTHER", example = "LINUX")
    public String getCode()
    {
        return this.name();
    }

}
