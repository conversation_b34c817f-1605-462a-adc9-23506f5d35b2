package com.jettech.jettong.common.cache.base.rbac.role;

import com.jettech.basic.cache.model.CacheKeyBuilder;
import com.jettech.jettong.common.cache.BaseCacheKeyDefinition;
import org.springframework.lang.NonNull;

import java.time.Duration;

/**
 * 菜单功能页面缓存key生成器
 *
 * <AUTHOR>
 * @version 1.0
 * @description 菜单功能页面缓存key生成器，区别与 MenuCacheKeyBuilder
 * @projectName jettong
 * @package com.jettech.jettong.common.cache.base.rbac.role
 * @className MenuManageCacheKeyBuilder
 * @date 2022/3/31 16:50
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
public class MenuManageCacheKeyBuilder implements CacheKeyBuilder
{
    @Override
    @NonNull
    public String getPrefix()
    {
        return BaseCacheKeyDefinition.BASE_RBAC_MENU_MANAGE;
    }

    @Override
    public Duration getExpire()
    {
        return Duration.ofHours(8);
    }


}
