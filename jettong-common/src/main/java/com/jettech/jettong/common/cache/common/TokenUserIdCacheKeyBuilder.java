package com.jettech.jettong.common.cache.common;


import com.jettech.basic.cache.model.CacheKeyBuilder;
import com.jettech.jettong.common.cache.BaseCacheKeyDefinition;

/**
 * token缓存Key生成器
 *
 * <AUTHOR>
 * @version 1.0
 * @description token缓存Key生成器
 * @projectName jettong
 * @package com.jettech.jettong.cmdb.cache.common
 * @className TokenUserIdCacheKeyBuilder
 * @date 2021/9/15 10:01
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
public class TokenUserIdCacheKeyBuilder implements CacheKeyBuilder
{
    @Override
    public String getPrefix()
    {
        return BaseCacheKeyDefinition.OAUTH_TOKEN_USER_ID;
    }

}
