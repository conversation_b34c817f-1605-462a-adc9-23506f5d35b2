package com.jettech.jettong.common.util.converter;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * TB、GB、MB和KB转换为B工具类
 *
 * <AUTHOR>
 * @version 1.0
 * @description MB和KB转换为B工具类
 * @projectName jettong-enterprises
 * @package com.jettech.jettong.common.util.converter
 * @className SizeConverterUtil
 * @date 2021/12/1 9:43
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
public class SizeConverterUtil
{

    /**
     * 数据正则匹配
     */
    private static final Pattern PATTERN = Pattern.compile("^\\d+([KMGT])B$");

    private static final Pattern NUMBER_PATTERN = Pattern.compile("-?\\d+(\\.\\d+)?");

    /**
     * 判断字符串是否有数字
     *
     * @param str 字符串
     * @return {@link boolean} 是否有数字 true/false
     * <AUTHOR>
     * @date 2024/1/11 10:55
     * @update 2024/1/11 10:55
     * @since 1.0
     */
    public static boolean isNumeric2(String str)
    {
        return str != null && PATTERN.matcher(str).matches();
    }

    /**
     * 将TB、GB、MB和KB转换为B
     *
     * @param size 大小
     * @return {@link long} B
     * <AUTHOR>
     * @date 2024/1/10 16:56
     * @update 2024/1/10 16:56
     * @since 1.0
     */
    public static long convertToBytes(String size)
    {
        if (!isNumeric2(size))
        {
            throw new IllegalArgumentException("Invalid input format");
        }

        // 提取数量部分
        Matcher matcher = NUMBER_PATTERN.matcher(size);

        double quantity = 0;
        try
        {
            if (matcher.find())
            {
                // 获取数量部分并转换为double类型
                quantity = Double.parseDouble(matcher.group());
            }
        }
        catch (NumberFormatException e)
        {
            throw new IllegalArgumentException("Invalid input format");
        }

        // 提取单位部分
        String unit = size.replace(matcher.group(), "");

        if ("MB".equalsIgnoreCase(unit))
        {
            // 将MB转换为B
            return Math.round(quantity * 1024 * 1024);
        }
        else if ("GB".equalsIgnoreCase(unit))
        {
            // 将GB转换为B
            return Math.round(quantity * 1024 * 1024 * 1024);
        }
        else if ("KB".equalsIgnoreCase(unit))
        {
            // 将KB转换为B
            return Math.round(quantity * 1024);
        }
        else if ("TB".equalsIgnoreCase(unit))
        {
            // 将TB转换为B
            return Math.round(quantity * 1024 * 1024 * 1024 * 1024);
        }
        else
        {
            throw new IllegalArgumentException("Unsupported unit of measurement");
        }
    }

}