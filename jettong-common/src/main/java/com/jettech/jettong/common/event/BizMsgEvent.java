package com.jettech.jettong.common.event;

import com.jettech.jettong.common.event.enumeration.MsgEventType;
import com.jettech.jettong.common.event.enumeration.MsgReceiveType;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 消息通知事件
 *
 * <AUTHOR>
 * @version 1.0
 * @description 消息事件
 * @projectName jettong-base-cloud
 * @package com.jettech.jettong.common.event
 * @className MsgEvent
 * @date 2023/5/26 10:38
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Getter
public class BizMsgEvent<E> extends ApplicationEvent {

    private final MsgEventType type;
    private final Map<MsgReceiveType, List<Long>> receiveMap;
    private final Long currentUserId;
    /**
     * 旧数据，新增时为null
     */
    private final E oldData;
    /**
     * 新数据，删除时为null
     */
    private final E newData;

    @JsonCreator
    public BizMsgEvent(
            @JsonProperty("type") MsgEventType type,
            @JsonProperty("receiveMap") Map<MsgReceiveType, List<Long>> receiveMap,
            @JsonProperty("currentUserId") Long currentUserId,
            @JsonProperty("oldData") E oldData,
            @JsonProperty("newData") E newData) {
        super(Objects.hash(oldData, newData));
        this.type = type;
        this.receiveMap = receiveMap;
        this.currentUserId = currentUserId;
        this.oldData = oldData;
        this.newData = newData;
    }

}
