package com.jettech.jettong.common.util.poi;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @description TODO
 * @projectName jettong-enterprises
 * @package com.jettech.jettong.common.util.poi
 * @className ExcelHeaderModel
 * @date 2021/12/1 14:24
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
public class ExcelHeaderModel implements Serializable
{
    /**
     * key
     */
    private String key;

    /**
     * 显示值
     */
    private String name;

    /**
     * 宽度，默认20
     */
    private Integer width = 30;

}
