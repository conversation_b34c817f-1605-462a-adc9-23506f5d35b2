package com.jettech.jettong.common.cache.tenant;

import com.jettech.basic.cache.model.CacheKeyBuilder;
import com.jettech.basic.utils.StrPool;
import com.jettech.jettong.common.cache.BaseCacheKeyDefinition;

import java.time.Duration;


/**
 * 租户缓存Key生成器
 *
 * <AUTHOR>
 * @version 1.0
 * @description 租户缓存Key生成器
 * @projectName jettong
 * @package com.jettech.jettong.cmdb.cache.tenant
 * @className CaptchaCacheKeyBuilder
 * @date 2021/9/15 10:01
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
public class TenantCacheKeyBuilder implements CacheKeyBuilder
{
    @Override
    public String getTenant()
    {
        return StrPool.EMPTY;
    }

    @Override
    public String getPrefix()
    {
        return BaseCacheKeyDefinition.BASE_RBAC_TENANT;
    }

    @Override
    public Duration getExpire()
    {
        return Duration.ofHours(24);
    }
}
