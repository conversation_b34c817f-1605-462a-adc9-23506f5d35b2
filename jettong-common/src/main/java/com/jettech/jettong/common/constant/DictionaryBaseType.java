package com.jettech.jettong.common.constant;

/**
 * 存放数据字典中的类型，必须和数据字典枚举一一对应
 *
 * <AUTHOR>
 * @version 1.0
 * @description 存放数据字典中的类型，必须和数据字典枚举一一对应
 * @projectName jettong
 * @package com.jettech.jettong.common.constant
 * @className DictionaryBaseType
 * @date 2021/10/23 14:32
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
public interface DictionaryBaseType
{
    /**
     * 用户类型
     */
    String USER_TYPE = "USER_TYPE";

    /**
     * 团队类型
     */
    String TEAM_TYPE = "TEAM_TYPE";

}
