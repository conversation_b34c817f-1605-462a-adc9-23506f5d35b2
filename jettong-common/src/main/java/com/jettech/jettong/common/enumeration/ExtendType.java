package com.jettech.jettong.common.enumeration;

import com.jettech.basic.base.BaseEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.stream.Stream;

/**
 * 扩展属性参数类型枚举
 *
 * <AUTHOR>
 * @version 1.0
 * @description 扩展属性参数类型枚举
 * @projectName jettong-enterprises
 * @package com.jettech.jettong.common.enumeration
 * @className ExtendType
 * @date 2021/10/26 11:12
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "ExtendType", description = "扩展属性参数类型-枚举")
public enum ExtendType implements BaseEnum
{
    /**
     * 密码-枚举
     */
    PASSWORD("密码框"),
    /**
     * 文本框-枚举
     */
    INPUT("文本框"),
    /**
     * 文本域-枚举
     */
    TEXTAREA("文本域"),
    /**
     * 其它-枚举
     */
    OTHER("其它");


    @ApiModelProperty(value = "描述")
    private String desc;


    /**
     * 根据当前枚举的name匹配
     */
    public static ExtendType match(String val, ExtendType def)
    {
        return Stream.of(values()).parallel().filter(item -> item.name().equalsIgnoreCase(val)).findAny().orElse(def);
    }

    public static ExtendType get(String val)
    {
        return match(val, null);
    }

    public boolean eq(ExtendType val)
    {
        return val != null && eq(val.name());
    }

    @Override
    @ApiModelProperty(value = "编码", allowableValues = "PASSWORD,INPUT,TEXTAREA,OTHER", example = "PASSWORD")
    public String getCode()
    {
        return this.name();
    }
}
