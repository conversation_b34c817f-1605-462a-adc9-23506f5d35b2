package com.jettech.jettong.common.event;

import com.jettech.basic.context.ContextUtil;
import com.jettech.jettong.common.event.enumeration.MsgEventType;
import com.jettech.jettong.common.event.enumeration.MsgReceiveType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 业务消息事件
 *
 * @param <E> 事件数据类型
 * <AUTHOR>
 * @version 1.0
 * @description 业务消息事件
 * @projectName jettong-base-cloud
 * @package com.jettech.jettong.common.event
 * @className BizMsgEvent
 * @date 2023/5/24 15:21
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
public abstract class AbstractMsgEventFactory<E> implements MsgEventFactory<E> {

    private static final Logger log = LoggerFactory.getLogger(AbstractMsgEventFactory.class);

    /**
     * 消息类型
     */
    protected final MsgEventType type;

    public AbstractMsgEventFactory(MsgEventType type) {
        this.type = type;
    }

    @Override
    public MsgEventType getType() {
        return type;
    }

    @Override
    public BizMsgEvent<E> createEvent(E oldData, E newData) {
        Long userId = ContextUtil.getUserId();
        Map<MsgReceiveType, List<Long>> map = getReceiveFunctionMap().entrySet().stream().collect(Collectors.toMap(
                Map.Entry::getKey,
                entry -> getReceiveUserIds(oldData, newData, entry.getValue())
        ));
        return new BizMsgEvent<>(type, map, userId, oldData, newData);
    }

    protected List<Long> getReceiveUserIds(E oldData, E newData, Function<E, List<Long>> function) {
        List<Long> result = new ArrayList<>();
        if (function == null) {
            return result;
        }

        List<Long> oldUserIds = function.apply(oldData);
        result.addAll(oldUserIds);

        List<Long> newUserIds = function.apply(newData);
        result.addAll(newUserIds);

        return result;
    }

}
