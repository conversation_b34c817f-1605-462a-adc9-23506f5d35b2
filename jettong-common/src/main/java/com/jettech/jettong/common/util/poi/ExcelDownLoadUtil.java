package com.jettech.jettong.common.util.poi;

import cn.hutool.core.io.IoUtil;
import org.apache.poi.ss.usermodel.Workbook;

import javax.servlet.http.HttpServletResponse;
import java.io.BufferedOutputStream;
import java.io.IOException;
import java.net.URLEncoder;

/**
 * 导出Excl文件公共类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 导出Excl文件公共类
 * @projectName jettong-enterprises
 * @package com.jettech.jettong.common.util.poi
 * @className ExcelDownLoadUtil
 * @date 2021/12/1 9:43
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
public class ExcelDownLoadUtil
{

    /**
     * 导出文件
     *
     * @param response HttpServletResponse
     * @param workbook workbook
     * @param fileName 文件名称
     * <AUTHOR>
     * @date 2021/12/1 9:43
     * @update zxy 2021/12/1 9:43
     * @since 1.0
     */
    public static void export(HttpServletResponse response, Workbook workbook, String fileName) throws IOException
    {
        response.setHeader("Content-Disposition",
                "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));
        response.setContentType("application/vnd.ms-excel;charset=UTF-8");
        response.setHeader("Pragma", "no-cache");
        response.setHeader("Cache-Control", "no-cache");
        response.setDateHeader("Expires", 0);
        BufferedOutputStream bufferedOutPut = null;
        try
        {
            bufferedOutPut = new BufferedOutputStream(response.getOutputStream());
            workbook.write(bufferedOutPut);
            bufferedOutPut.flush();
        }
        finally
        {
            IoUtil.close(bufferedOutPut);
        }
    }

}
