package com.jettech.jettong.common.cache.base.sys.dictionary;

import com.jettech.basic.cache.model.CacheKeyBuilder;
import com.jettech.jettong.common.cache.BaseCacheKeyDefinition;

/**
 * 数据字典类型缓存Key生成器
 *
 * <AUTHOR>
 * @version 1.0
 * @description 数据字典类型缓存Key生成器
 * @projectName jettong
 * @package com.jettech.jettong.common.cache.base.sys.dictionary
 * @className DictionaryTypeCacheKeyBuilder
 * @date 2021/9/15 10:01
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
public class DictionaryTypeCacheKeyBuilder implements CacheKeyBuilder
{
    @Override
    public String getPrefix()
    {
        return BaseCacheKeyDefinition.BASE_SYS_DICTIONARY_TYPE;
    }


}
