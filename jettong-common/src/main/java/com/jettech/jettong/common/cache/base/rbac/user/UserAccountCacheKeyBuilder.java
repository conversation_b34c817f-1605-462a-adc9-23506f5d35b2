package com.jettech.jettong.common.cache.base.rbac.user;

import com.jettech.basic.cache.model.CacheKeyBuilder;
import com.jettech.jettong.common.cache.BaseCacheKeyDefinition;

import java.time.Duration;

/**
 * 用户账号缓存key生成器
 *
 * <AUTHOR>
 * @version 1.0
 * @description 用户账号缓存key生成器
 * @projectName jettong
 * @package com.jettech.jettong.common.cache.base.rbac.user
 * @className UserAccountCacheKeyBuilder
 * @date 2021/10/15 20:06
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
public class UserAccountCacheKeyBuilder implements CacheKeyBuilder
{
    @Override
    public String getPrefix()
    {
        return BaseCacheKeyDefinition.BASE_RBAC_USER_ACCOUNT;
    }

    @Override
    public Duration getExpire()
    {
        return Duration.ofHours(8);
    }
}
