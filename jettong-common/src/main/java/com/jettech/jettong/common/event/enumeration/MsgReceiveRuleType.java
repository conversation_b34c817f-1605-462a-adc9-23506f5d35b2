package com.jettech.jettong.common.event.enumeration;

import com.jettech.basic.base.BaseEnum;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @projectName jettong-base-cloud
 * @package com.jettech.jettong.base.entity.msg
 * @className MsgReceiveRuleType
 * @date 2023/6/1 11:31
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
public enum MsgReceiveRuleType implements BaseEnum {
    USER("用户"),
    ROLE("角色"),
    TEAM("团队"),

    BIZ("业务")
    ;


    private final String desc;

    MsgReceiveRuleType(String desc) {
        this.desc = desc;
    }

    @Override
    public String getDesc() {
        return desc;
    }
}
