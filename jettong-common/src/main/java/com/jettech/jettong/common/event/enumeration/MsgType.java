package com.jettech.jettong.common.event.enumeration;

import com.jettech.basic.base.BaseEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.stream.Stream;

/**
 * 消息类型-枚举
 *
 * <AUTHOR>
 * @version 1.0
 * @description 消息类型-枚举
 * @projectName jettong
 * @package com.jettech.jettong.base.enumeration.msg
 * @className MsgType
 * @date 2021/9/15 9:43
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "MsgType", description = "消息类型-枚举")
public enum MsgType implements BaseEnum
{

    /**
     * TO_DO="待办"
     */
    TO_DO("待办"),
    /**
     * NOTIFY="通知"
     */
    NOTIFY("通知"),
    /**
     * NOTICE="公告"
     */
    NOTICE("公告"),
    /**
     * EARLY_WARNING="预警"
     */
    EARLY_WARNING("预警"),
    ;

    @ApiModelProperty(value = "描述")
    private String desc;


    /**
     * 根据当前枚举的name匹配
     */
    public static MsgType match(String val, MsgType def)
    {
        return Stream.of(values()).parallel().filter(item -> item.name().equalsIgnoreCase(val)).findAny().orElse(def);
    }

    public static MsgType get(String val)
    {
        return match(val, null);
    }

    public boolean eq(MsgType val)
    {
        return val != null && eq(val.name());
    }

    @Override
    @ApiModelProperty(value = "编码", allowableValues = "TO_DO,NOTIFY,NOTICE,EARLY_WARNING", example = "TO_DO")
    public String getCode()
    {
        return this.name();
    }

}
