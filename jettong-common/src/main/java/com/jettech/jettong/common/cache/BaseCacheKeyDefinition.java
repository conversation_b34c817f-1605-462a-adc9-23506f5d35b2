package com.jettech.jettong.common.cache;

/**
 * 用于同一管理和生成缓存的key， 避免多个项目使用的key重复
 * <p>
 * 使用@Cacheable时， 其value值一定要在此处指定
 *
 * <AUTHOR>
 * @date 2020/10/21
 */
public interface BaseCacheKeyDefinition
{

    // 认证服务缓存 start
    /**
     * 验证码 前缀
     */
    String OAUTH_LOGIN_CAPTCHA = "oauth:login:captcha";

    /**
     * 在用用户 前缀
     */
    String OAUTH_USER_ONLINE = "oauth:user:online";

    /**
     * 用户token 前缀
     */
    String OAUTH_TOKEN_USER_ID = "oauth:token:token_user_id";

    // 认证服务缓存 end

    // 基础服务缓存 start

    /**
     * 租户 前缀
     */
    String BASE_RBAC_TENANT = "base:rbac:tenant";
    /**
     * 租户 前缀
     */
    String BASE_RBAC_TENANT_CODE = "base:rbac:tenant_code";

    /**
     * 组织机构 前缀
     */
    String BASE_RBAC_ORG = "base:rbac:org";

    /**
     * 角色 前缀
     */
    String BASE_RBAC_ROLE = "base:rbac:role";

    /**
     * 菜单 前缀
     */
    String BASE_RBAC_MENU = "base:rbac:menu";

    /**
     * 菜单资源页面功能 前缀
     */
    String BASE_RBAC_MENU_MANAGE = "base:rbac:menu_manage";

    /**
     * 菜单功能 前缀
     */
    String BASE_RBAC_FUNCTION = "base:rbac:function";

    /**
     * 角色菜单 前缀
     */
    String BASE_RBAC_ROLE_MENU = "base:rbac:role_menu";

    /**
     * 角色功能 前缀
     */
    String BASE_RBAC_ROLE_FUNCTION = "base:rbac:role_function";

    /**
     * 用户 前缀
     */
    String BASE_RBAC_USER = "base:rbac:user";

    /**
     * 团队前缀
     */
    String BASE_RBAC_USER_GROUP = "base:rbac:user_group";

    /**
     * 用户账号 前缀
     */
    String BASE_RBAC_USER_ACCOUNT = "base:rbac:user_account";

    /**
     * 用户角色 前缀
     */
    String BASE_RBAC_USER_ROLE = "base:rbac:user_role";

    /**
     * 用户菜单 前缀
     */
    String BASE_RBAC_USER_MENU = "base:rbac:user_menu";

    /**
     * 用户菜单功能 前缀
     */
    String BASE_RBAC_USER_FUNCTION = "base:rbac:user_function";

    /**
     * 用户团队 前缀
     */
    String BASE_RBAC_USER_USER_GROUP = "base:rbac:user_user_group";

    /**
     * 数据字典 前缀
     */
    String BASE_SYS_DICTIONARY = "base:sys:dictionary";

    /**
     * 数字字典类型 前缀
     */
    String BASE_SYS_DICTIONARY_TYPE = "base:sys:dictionary_type";

    /**
     * webhook签名 前缀
     */
    String BASE_WEBHOOK_SIGN = "base:webhook:sign";

    // 基础服务缓存 end


}
