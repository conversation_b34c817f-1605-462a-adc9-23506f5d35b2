#!/bin/bash
cd /home

echo "java -XX:NewSize=128m -XX:MaxNewSize=128m -XX:SurvivorRatio=8 -Xms512m -Xmx512m -jar ${PACKAGENAME} --jettong.nacos.ip=${NACOS_IP} --jettong.nacos.port=${NACOS_PORT} --jettong.nacos.namespace=${NACOS_NS} --jettong.nacos.username=${NACOS_USER} --jettong.nacos.password=${NACOS_PWD} --logging.file.path=${LOG_PATH}"
java -XX:NewSize=128m -XX:MaxNewSize=128m -XX:SurvivorRatio=8 -Xms512m -Xmx512m -jar ${PACKAGENAME} --jettong.nacos.ip=${NACOS_IP} --jettong.nacos.port=${NACOS_PORT} --jettong.nacos.namespace=${NACOS_NS} --jettong.nacos.username=${NACOS_USER} --jettong.nacos.password=${NACOS_PWD} --logging.file.path=${LOG_PATH}